buildscript {
    repositories {
        // ...
        maven { url 'https://plugins.gradle.org/m2/' } // Gradle Plugin Portal
//        mavenCentral() // Add this line if missing
    }
    dependencies {
        //
        }
}
plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.salawati.app"
    compileSdkVersion = 35
    ndkVersion = "26.1.10909125"

    compileOptions {
        coreLibraryDesugaringEnabled = true

        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
     sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.salawati.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion  23
        targetSdkVersion  34
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        multiDexEnabled  true
    }
    signingConfigs {
        release {
            keyAlias = keystoreProperties['keyAlias']
            keyPassword = keystoreProperties['keyPassword']
            storeFile = keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword = keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
          debug {
            signingConfig signingConfigs.release
        }
    }

   lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }
     kotlinOptions {
        jvmTarget = '1.8'
    }
     buildFeatures {
        compose true
         viewBinding true
     }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.4.4"
    }

}


flutter {
    source = "../.."
}


dependencies {

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'

    implementation 'com.google.android.material:material:1.6.0'
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'androidx.browser:browser:1.3.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0'
    implementation 'androidx.glance:glance-appwidget:1.0.0'

    implementation "androidx.multidex:multidex:2.0.1"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
    // for workmanger to work!
    // Kotlin + coroutines
    implementation("androidx.work:work-runtime-ktx:2.10.1")

    // optional - RxJava2 support
    implementation("androidx.work:work-rxjava2:2.10.1")

    // optional - GCMNetworkManager support
    implementation("androidx.work:work-gcm:2.10.1")

    // optional - Test helpers
    androidTestImplementation("androidx.work:work-testing:2.10.1")

    // optional - Multiprocess support
    implementation("androidx.work:work-multiprocess:2.10.1")




}

googleServices { disableVersionCheck = true }