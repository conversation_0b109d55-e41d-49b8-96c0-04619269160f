<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/widget_img"

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/homeWidget"
        android:scaleType="centerCrop"
        tools:ignore="ImageContrastCheck"
        />

    <TextView
        android:id="@+id/widget_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="36sp"
        android:textStyle="bold"
        tools:text="Title" />

</FrameLayout>
