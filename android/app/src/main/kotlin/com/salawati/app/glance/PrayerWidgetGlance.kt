package com.salawati.app.glance

import HomeWidgetGlanceState
import HomeWidgetGlanceStateDefinition
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Bitmap
import androidx.compose.runtime.Composable

import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.Image

import androidx.glance.appwidget.GlanceAppWidget

import androidx.glance.appwidget.provideContent

import androidx.glance.currentState
import androidx.glance.layout.Alignment
import androidx.glance.layout.Column
import androidx.glance.layout.fillMaxSize
import com.salawati.app.MainActivity
import androidx.glance.layout.ContentScale

class PrayerWidgetGlance : GlanceAppWidget() {

    /** Needed for Updating */
    override val stateDefinition = HomeWidgetGlanceStateDefinition()

    override suspend fun provideGlance(context: Context, id: GlanceId) {
    provideContent { GlanceContent(context, currentState()) }
    }

    @Composable
    private fun GlanceContent(context: Context, currentState: HomeWidgetGlanceState) {
        val data = currentState.preferences
        val imagePath = data.getString("prayer_times_image", null)

        Column(
            modifier = GlanceModifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            imagePath?.let {
                val bitmap = BitmapFactory.decodeFile(it)


                Image(
                    provider = androidx.glance.ImageProvider(bitmap),
                    contentDescription = "prayer image",
                    // contentScale = ContentScale.FitWidth(),
                )

            }
        }
    }
}
