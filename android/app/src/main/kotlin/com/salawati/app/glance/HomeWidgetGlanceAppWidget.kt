package com.salawati.app.glance

import HomeWidgetGlanceState
import HomeWidgetGlanceStateDefinition
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Bitmap
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.Image

import androidx.glance.appwidget.GlanceAppWidget

import androidx.glance.appwidget.provideContent

import androidx.glance.currentState
import androidx.glance.layout.Alignment
import androidx.glance.layout.Column
import androidx.glance.layout.fillMaxSize
import com.salawati.app.MainActivity
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextStyle

class HomeWidgetGlanceAppWidget : GlanceAppWidget() {

  /** Needed for Updating */
  override val stateDefinition = HomeWidgetGlanceStateDefinition()

  override suspend fun provideGlance(context: Context, id: GlanceId) {
    provideContent { GlanceContent(context, currentState()) }
  }

  @Composable
  private fun GlanceContent(context: Context, currentState: HomeWidgetGlanceState) {
    val data = currentState.preferences
    val imagePath = data.getString("widget1", null)
  val title = data.getString("title", "")!!
      val widgetWidth = 500
    val widgetHeight = 500

    Column(
            modifier = GlanceModifier.fillMaxSize(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalAlignment = Alignment.CenterHorizontally
          ) {
                Text(
                        title,
                        style = TextStyle(fontSize = 36.sp, fontWeight = FontWeight.Bold),
                )
            imagePath?.let {
              val bitmap = BitmapFactory.decodeFile(it)
                val scaledBitmap = Bitmap.createScaledBitmap(bitmap, widgetWidth, widgetHeight, true)


              Image(androidx.glance.ImageProvider(scaledBitmap),  "Dashboard Icon",

                )

            }
          }
        }
}
