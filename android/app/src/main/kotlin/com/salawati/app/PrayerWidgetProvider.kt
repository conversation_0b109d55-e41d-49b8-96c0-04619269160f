package com.salawati.app

import android.appwidget.AppWidgetManager
import android.content.Context
import android.content.SharedPreferences
import android.graphics.BitmapFactory
import android.view.View
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetLaunchIntent
import es.antonborri.home_widget.HomeWidgetProvider

class PrayerWidgetProvider : HomeWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray,
        widgetData: SharedPreferences
    ) {
        appWidgetIds.forEach { widgetId ->
            val views =
                RemoteViews(context.packageName, R.layout.prayer_widget_layout).apply {
                    // Open App on Widget Click
                    val pendingIntent =
                        HomeWidgetLaunchIntent.getActivity(context, MainActivity::class.java)
                    setOnClickPendingIntent(R.id.widget_container, pendingIntent)

                    // Show Images saved with `renderFlutterWidget`
                    val image = widgetData.getString("widget2", null)
                    if (image != null) {
                        setImageViewBitmap(R.id.widget_img, BitmapFactory.decodeFile(image))
                        setViewVisibility(R.id.widget_img, View.VISIBLE)
                    } else {
                        setViewVisibility(R.id.widget_img, View.GONE)
                    }


                }

            appWidgetManager.updateAppWidget(widgetId, views)
        }
    }
}