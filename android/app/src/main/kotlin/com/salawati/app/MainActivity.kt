package com.salawati.app


import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import android.util.Log
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

// for alarm package
import android.content.Intent
import android.os.Bundle

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.salawati.app/notification"

    // // for alarm package
    // override fun onCreate(savedInstanceState: Bundle?) {
    //     super.onCreate(savedInstanceState)
    // }

    // override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
    //     super.configureFlutterEngine(flutterEngine)
    //     //createNotificationChannels()
    //     // MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
    //     //     call, result ->
    //     //     if (call.method == "logNotificationChannels") {
    //     //         logNotificationChannels()
    //     //         result.success("Logged notification channels")
    //     //     } else {
    //     //         result.notImplemented()
    //     //     }
    //     // }
    // }

    // private fun createNotificationChannels() {
    //     if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
    //         val fullAthanChannel = NotificationChannel(
    //             "salawati_full_athan",
    //             "Salawati Full Athan",
    //             NotificationManager.IMPORTANCE_HIGH
    //         ).apply {
    //             description = "This is the channel for full Athan notifications"
    //         }

    //         val shortAthanChannel = NotificationChannel(
    //             "salawati_short_athan",
    //             "Salawati Short Athan",
    //             NotificationManager.IMPORTANCE_HIGH
    //         ).apply {
    //             description = "This is the channel for short Athan notifications"
    //         }

    //         val defaultChannel = NotificationChannel(
    //             "salawati",
    //             "Salawati Default",
    //             NotificationManager.IMPORTANCE_HIGH
    //         ).apply {
    //             description = "This is the default notification channel"
    //         }

    //         val notificationManager: NotificationManager =
    //             getSystemService(NotificationManager::class.java)
    //         notificationManager.createNotificationChannel(fullAthanChannel)
    //         notificationManager.createNotificationChannel(shortAthanChannel)
    //         notificationManager.createNotificationChannel(defaultChannel)
    //     }
    // }

    private fun logNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)
            val channels = notificationManager.notificationChannels
            for (channel in channels) {
                Log.d("NotificationChannel", "Channel ID: ${channel.id}, Name: ${channel.name}")
            }
        }
    }
}
