import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/qiblah/presentation/widgets/qiblah_view.dart';
import 'package:salawati/features/qiblah/presentation/widgets/rive_widget.dart';

// class QiblahScreen extends GetView<PrayerController> {
//   const QiblahScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return SmoothEdgesContainer(
//       borderRadius: BorderRadius.vertical(top: Radius.circular(60.r)),
//       width: double.infinity,
//       color: Theme.of(context).scaffoldBackgroundColor,
//       child: const QiblahView(),
//     );
//   }
// }

class QiblahHeader extends GetView<PrayerController> {
  final bool isMap;
  const QiblahHeader({
    super.key,
    required this.isMap,
  });
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final kaabaDistance = controller.getKaabaDistance();

      final heading = controller.getQiblahDegree();
      final label = getDirectionLabel(
          degree: heading, languageCode: Get.locale?.languageCode ?? 'ar');
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            0.02.sh.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SettingsShareRow(),
                InkWell(
                  onTap: () async {
                    if (kDebugMode) {
                    } else {
                      AppFunctions.shareText(context,
                          Get.find<PrayerController>().printPrayerTimes());
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset(AppSvgs.kShare),
                  ),
                ),
              ],
            ),
            0.02.sh.verticalSpace,
            if (!isMap) ...[
              GridView.count(
                shrinkWrap: true,
                primary: false,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                crossAxisCount: 2,
                childAspectRatio: 1.8,
                children: [
                  QiblahOptions(
                      option: 'qibla_distance',
                      optionValue:
                          "${NumberLocalization.localizeNumber(kaabaDistance > 1000 ? kaabaDistance / 1000 : kaabaDistance)} ${kaabaDistance > 1000 ? 'KM'.tr : 'M'}"),
                  QiblahOptions(
                      option: 'qibla_direction',
                      optionValue:
                          '${NumberLocalization.localizeNumber(heading ?? 0)}° $label'),
                  // StreamBuilder<CompassXEvent>(
                  //   stream: CompassX.events,
                  //   builder: (context, snapshot) {
                  //     if (snapshot.hasError) {
                  //       return Text('Error'.tr);
                  //     }
                  //     if (!snapshot.hasData) {
                  //       return Center(child: const CircularProgressIndicator());
                  //     }

                  //     final compass = snapshot.data;
                  //     if (compass?.heading == null) {
                  //       return CustomText('Error'.tr);
                  //     }
                  //     return QiblahOptions(
                  //       option: 'sensor_accuracy',
                  //       optionValue: compass!.shouldCalibrate
                  //           ? 'low_accuracy'
                  //           : 'high_accuracy',
                  //       isWarning: compass.shouldCalibrate,
                  //     );
                  //   },
                  // ),
                  QiblahOptions(
                    option: 'sensor_accuracy',
                    optionValue: 'high_accuracy',
                    // optionValue: compass!.shouldCalibrate
                    //     ? 'low_accuracy'
                    //     : 'high_accuracy',
                    isWarning: false,
                  ),
                  if (controller.currentCity.value != null)
                    Obx(() {
                      return QiblahOptions(
                        option: 'current_location',
                        optionValue: controller.currentCity.value!,
                        extra: Row(
                          children: [
                            InkWell(
                              onTap: () {
                                Get.toNamed(AppRouter.kSettingsLocationScreen);
                              },
                              child: const Icon(
                                Icons.refresh_rounded,
                                size: 20,
                                color: AppColor.kWhiteColor,
                              ),
                            ),
                            // InkWell(
                            //   child: const Icon(
                            //     Icons.refresh_rounded,
                            //     size: 20,
                            //     color: AppColor.kWhiteColor,
                            //   ),
                            //   onTap: () {
                            //     Get.dialog(
                            //       Dialog(
                            //         backgroundColor: AppColor.kScaffoldColor,
                            //         shape: RoundedRectangleBorder(
                            //           borderRadius: BorderRadius.circular(20.r),
                            //         ),
                            //         child: Padding(
                            //           padding: EdgeInsets.all(16.w),
                            //           child: Column(
                            //             mainAxisSize: MainAxisSize.min,
                            //             children: [
                            //               CustomText(
                            //                 'location_settings',
                            //                 style: TextStyle(
                            //                   fontSize: 18.sp,
                            //                   fontWeight: FontWeight.bold,
                            //                 ),
                            //               ),
                            //               16.verticalSpace,
                            //               // Auto location toggle
                            //               GetBuilder<SettingsController>(
                            //                   builder: (settingsController) {
                            //                 return SmoothEdgesContainer(
                            //                   borderRadius:
                            //                       BorderRadius.circular(15.r),
                            //                   padding: EdgeInsets.all(12.w),
                            //                   color: AppColor.kRectangleColor,
                            //                   child: SettingsSwitchItemBuilder(
                            //                     title: 'Automatic',
                            //                     switchValue: settingsController
                            //                         .isLocationAuto.value,
                            //                     onChanged: settingsController
                            //                             .isLoading.value
                            //                         ? null
                            //                         : (value) async {
                            //                             await settingsController
                            //                                 .changeLocationAuto(
                            //                                     value);
                            //                           },
                            //                   ),
                            //                 );
                            //               }),
                            //               16.verticalSpace,
                            //               // Manual location search (only shown when auto is off)
                            //               GetBuilder<SettingsController>(
                            //                   builder: (settingsController) {
                            //                 if (!settingsController
                            //                     .isLocationAuto.value) {
                            //                   return Column(
                            //                     children: [
                            //                       CustomTextField(
                            //                         controller: settingsController
                            //                             .locationTextEditingController
                            //                             .value,
                            //                         hintText:
                            //                             'Find Location....'.tr,
                            //                         prefixIconData:
                            //                             SvgPicture.asset(AppSvgs
                            //                                 .kLocationAdd),
                            //                         onFieldSubmitted: (text) {
                            //                           if (text.isNotEmpty) {
                            //                             Get.find<
                            //                                     SettingsSearchController>()
                            //                                 .getLocalCitiesByName(
                            //                               cityName: text,
                            //                             );
                            //                           }
                            //                         },
                            //                       ),
                            //                       16.verticalSpace,
                            //                       SizedBox(
                            //                         height: 150.h,
                            //                         child: CitiesList(
                            //                           settingsSearchController:
                            //                               Get.find<
                            //                                   SettingsSearchController>(),
                            //                           settingsController:
                            //                               settingsController,
                            //                         ),
                            //                       ),
                            //                     ],
                            //                   );
                            //                 }
                            //                 return const SizedBox.shrink();
                            //               }),
                            //               16.verticalSpace,
                            //               TextButton(
                            //                 onPressed: () => Get.back(),
                            //                 child: CustomText('Cancel'),
                            //               ),
                            //             ],
                            //           ),
                            //         ),
                            //       ),
                            //     );
                            //   },
                            // ),
                          ],
                        ),
                      );
                    }),
                ],
              ),
            ]
          ],
        ),
      );
    });
  }
}
