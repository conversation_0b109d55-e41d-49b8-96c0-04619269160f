import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/qiblah/presentation/cubit/qiblah_state.dart';

class QiblahCubit extends Cubit<QiblahState> {
  QiblahCubit() : super(QiblahInitial());

  PolylinePoints polylinePoints = PolylinePoints();
  Map<PolylineId, Polyline> polylines = {};
  List<LatLng> polylineCoordinates = [];

  void drawRoute(double startLat, double startLng) async {
    double endLat = KAABA_LATITUDE;
    double endLng = KAABA_LONGITUDE;
    emit(GetMapQiblahLoading());
    await polylinePoints
        .getRouteBetweenCoordinates(
      googleApiKey: GOOGLE_API_KEY,
      request: PolylineRequest(
        origin: PointLatLng(startLat, startLng),
        destination: PointLatLng(endLat, endLng),
        mode: TravelMode.driving,
        wayPoints: [
          PolylineWayPoint(location: "Makkah"),
        ],
      ),
    )
        .then((value) {
      for (var point in value.points) {
        polylineCoordinates.add(LatLng(point.latitude, point.longitude));
      }
      _addPolyline();
      emit(GetMapQiblahSuccess());
    }).catchError((error) {
      emit(GetMapQiblahFailure(error.toString()));
    });
  }

  void _addPolyline() {
    PolylineId id = const PolylineId("poly");
    Polyline polyline = Polyline(
      polylineId: id,
      color: Colors.red,
      points: polylineCoordinates,
    );
    polylines[id] = polyline;
  }
}
