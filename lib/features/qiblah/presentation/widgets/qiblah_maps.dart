import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_compass/flutter_compass.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/widgets/custom_lottie_api.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';

class QiblahMaps extends StatefulWidget {
  static const meccaLatLong = LatLng(21.422487, 39.826206);
  static final meccaMarker = Marker(
    markerId: const MarkerId("mecca"),
    position: meccaLatLong,
    icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange),
    draggable: false,
  );

  const QiblahMaps({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _QiblahMapsState createState() => _QiblahMapsState();
}

class _QiblahMapsState extends State<QiblahMaps> {
  final Completer<GoogleMapController> _controller = Completer();
  late LatLng position;

  late double latitude;
  late double longitude;
  PrayerController prayerController = Get.find<PrayerController>();
  late String? style;

  @override
  void initState() {
    super.initState();
    latitude = prayerController.latitude;
    longitude = prayerController.longitude;
    position = LatLng(latitude, longitude);
    DefaultAssetBundle.of(context)
        .loadString('assets/json/qiblah_map.json')
        .then((string) {
      setState(
        () {
          style = string;
        },
      );
    });
  }

  @override
  void dispose() {
    _controller.future.then((controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double zoom = 15;
    bool cameraMoving = false;
    return AspectRatio(
      aspectRatio: 1,
      child: Center(
        child: SmoothEdgesContainer(
          borderRadius: BorderRadius.circular(54),
          child: FutureBuilder(
            future: AppFunctions.customMarker(),
            builder: (context, markerSnapshot) {
              if (markerSnapshot.data == null) {
                return const Center(
                    child: CustomLottieApi(lottieApi: LottieApi.loading));
              }
              return GoogleMap(
                mapType: MapType.normal,
                zoomGesturesEnabled: true,
                zoomControlsEnabled: true,
                compassEnabled: true,
                myLocationButtonEnabled: true,
                initialCameraPosition: CameraPosition(
                  target: position,
                  zoom: zoom,
                ),
                // ignore: prefer_collection_literals
                gestureRecognizers: Set()
                  ..add(Factory<PanGestureRecognizer>(
                      () => PanGestureRecognizer()))
                  ..add(Factory<ScaleGestureRecognizer>(
                      () => ScaleGestureRecognizer()))
                  ..add(Factory<TapGestureRecognizer>(
                      () => TapGestureRecognizer()))
                  ..add(Factory<VerticalDragGestureRecognizer>(
                      () => VerticalDragGestureRecognizer())),
                markers: <Marker>{
                  QiblahMaps.meccaMarker,
                  Marker(
                    draggable: true,
                    markerId: const MarkerId('Marker'),
                    position: position,
                    icon: markerSnapshot.data!,
                  ),
                },
                circles: <Circle>{
                  Circle(
                    circleId: const CircleId("Circle"),
                    radius: 10,
                    center: position,
                    fillColor:
                        Theme.of(context).primaryColorLight.withAlpha(100),
                    strokeWidth: 1,
                    strokeColor:
                        Theme.of(context).primaryColorDark.withAlpha(100),
                    zIndex: 3,
                  )
                },
                polylines: <Polyline>{
                  Polyline(
                    polylineId: const PolylineId("Line"),
                    points: [position, QiblahMaps.meccaLatLong],
                    color: AppColor.kOrangeColor,
                    width: 10,
                    zIndex: 4,
                    geodesic: true,
                  ),
                },

                style: style,
                onCameraMoveStarted: () {
                  cameraMoving = true;
                },
                onCameraIdle: () async {
                  cameraMoving = false;
                },

                onMapCreated: (GoogleMapController controller) async {
                  _controller.complete(controller);
                  FlutterCompass.events?.listen((event) async {
                    if (cameraMoving) {
                      return;
                    }
                    await controller.moveCamera(CameraUpdate.newCameraPosition(
                      CameraPosition(
                        target: position,
                        bearing: event.heading ?? 0.0,
                        zoom: await controller.getZoomLevel(),
                      ),
                    ));
                  });
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
