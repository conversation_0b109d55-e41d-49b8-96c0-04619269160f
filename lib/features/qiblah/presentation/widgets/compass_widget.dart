// import 'dart:async';
// import 'dart:io';
// import 'dart:math' as math;

// import 'package:compassx/compassx.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_compass/flutter_compass.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:get/get.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:salawati/core/utils/app_assets.dart';
// import 'package:salawati/core/utils/custom_text.dart';
// import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
// import 'package:salawati/features/qiblah/presentation/widgets/qiblah_maps.dart';
// import 'package:sensors_plus/sensors_plus.dart';

// import '../../../../core/utils/app_color.dart';

// class CompassWidget2 extends StatefulWidget {
//   const CompassWidget2({super.key});

//   @override
//   State<CompassWidget2> createState() => _CompassWidget2State();
// }

// class _CompassWidget2State extends State<CompassWidget2>
//     with TickerProviderStateMixin {
//   double? _direction;
//   StreamSubscription<CompassEvent>? _subscription;
//   Timer? _timer;
//   var diff = 0.0;
//   bool dirbool = false;
//   static const double alpha = 0.3;

//   late AnimationController _controller;
//   double _currentPitch = 0.0;
//   double _currentRoll = 0.0;
//   double _targetPitch = 0.0;
//   double _targetRoll = 0.0;

//   StreamSubscription<AccelerometerEvent>? _accelSub;
//   Orientation _currentOrientation = Orientation.portrait;

//   bool _isDialogVisible = false;

//   @override
//   void initState() {
//     super.initState();
//     _initializeCompass();
//     _startListening();

//     Get.put(PrayerController());

//     _controller = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 16),
//     )..addListener(() {
//         _currentPitch += (_targetPitch - _currentPitch) * 0.3;
//         _currentRoll += (_targetRoll - _currentRoll) * 0.3;
//         setState(() {});
//       });
//     _controller.repeat();

//     _accelSub = accelerometerEventStream().listen((event) {
//       double x = event.x;
//       double y = event.y;
//       if (_currentOrientation == Orientation.landscape) {
//         x = event.y;
//         y = -event.x;
//       }

//       final double rawPitch =
//           (180 / math.pi) * math.atan2(x, math.sqrt(y * y + event.z * event.z));
//       final double rawRoll =
//           (180 / math.pi) * math.atan2(y, math.sqrt(x * x + event.z * event.z));

//       _targetPitch = alpha * rawPitch + (1 - alpha) * _targetPitch;
//       _targetRoll = alpha * rawRoll + (1 - alpha) * _targetRoll;
//     });
//   }

//   void _startListening() {
//     _subscription?.cancel();
//     _subscription = FlutterCompass.events?.listen((CompassEvent event) {
//       setState(() {
//         _direction = event.heading;
//       });
//     });

//     _timer = Timer.periodic(const Duration(seconds: 1), (_) {
//       setState(() {});
//     });
//   }

//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     _startListening();
//   }

//   @override
//   void dispose() {
//     _subscription?.cancel();
//     _timer?.cancel();
//     _controller.dispose();
//     _accelSub?.cancel();
//     SystemChrome.setPreferredOrientations([]);
//     super.dispose();
//   }

//   Future<void> _initializeCompass() async {
//     if (Platform.isAndroid) {
//       await Permission.location.request();
//     }
//   }

//   String getMovementInstructions(
//       double currentDirection, double targetDirection) {
//     diff = (targetDirection - currentDirection + 360) % 360;
//     if (diff > 180) {
//       diff = 360 - diff;
//     }
//     if (diff <= 5 || (360 - diff) <= 5) {
//       return 'اتجاه القبلة';
//     } else if (diff > 175) {
//       dirbool = true;
//       return 'تحرك ${diff.round()} درجة إلى اليسار';
//     } else {
//       dirbool = false;
//       return 'تحرك ${diff.round()} درجة إلى اليمين';
//     }
//   }

//   double getSignedDifference(double current, double target) {
//     double diff = (target - current + 360) % 360;
//     if (diff > 180) {
//       diff = diff - 360;
//     }
//     return diff;
//   }

//   void _showForcePopup() {
//     _isDialogVisible = true;
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (context) {
//         return AlertDialog(
//           contentPadding: const EdgeInsets.all(0),
//           backgroundColor: Theme.of(context).primaryColor,
//           content: Image.asset('assets/images/isNotLeveled.png'),
//         );
//       },
//     ).then((_) {
//       _isDialogVisible = false;
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     _currentOrientation = MediaQuery.of(context).orientation;
//     if (_direction == null) {
//       return const Center(child: CircularProgressIndicator());
//     }
//     if (_direction == 0.0) {
//       return const QiblahMaps();
//     }
//     double adjustedDirection = (_direction! + 180) % 360 - 180;

//     return GetBuilder<PrayerController>(builder: (controller) {
//       final qiblahDegree = controller.qiblahDegree.value ?? 0;
//       getMovementInstructions(_direction!, qiblahDegree);
//       double signedDiff = getSignedDifference(_direction!, qiblahDegree);
//       bool isBetweenExclusive = signedDiff.isBetween(-5, 5);
//       bool isNotLevel = _currentPitch.abs() < 5 && _currentRoll.abs() < 5;

//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         if (isNotLevel && !_isDialogVisible) {
//           _showForcePopup();
//         } else if (isNotLevel && _isDialogVisible) {
//           if (Navigator.canPop(context)) {
//             Navigator.of(context).pop();
//             _isDialogVisible = false;
//           }
//         }
//       });

//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           AspectRatio(
//             aspectRatio: 1,
//             child: Stack(
//               alignment: Alignment.center,
//               children: [
//                 Stack(
//                   children: [
//                     Transform.rotate(
//                       angle: adjustedDirection * (math.pi / 180) * -1,
//                       child: SvgPicture.asset(
//                         'assets/svgs/compass4.svg',
//                         height: 320,
//                         width: 320,
//                         fit: BoxFit.fill,
//                       ),
//                     ),
//                     Positioned(
//                       right: 90,
//                       bottom: 80,
//                       child: Row(
//                         crossAxisAlignment: CrossAxisAlignment.end,
//                         children: [
//                           Opacity(
//                             opacity: signedDiff <= 0 ? 0 : 1,
//                             child: Padding(
//                               padding:
//                                   const EdgeInsets.only(bottom: 5, right: 5),
//                               child: SvgPicture.asset(
//                                 'assets/svgs/compassrightarrow.svg',
//                                 height: 20,
//                                 width: 40,
//                                 fit: BoxFit.fill,
//                               ),
//                             ),
//                           ),
//                           Container(
//                             width: 50,
//                             alignment: Alignment.center,
//                             child: Text(
//                               '${signedDiff.round()}',
//                               style: const TextStyle(color: Colors.white),
//                             ),
//                           ),
//                           Opacity(
//                             opacity: signedDiff >= 0 ? 0 : 1,
//                             child: Padding(
//                               padding:
//                                   const EdgeInsets.only(bottom: 5, right: 5),
//                               child: SvgPicture.asset(
//                                 'assets/svgs/compasslefttarrow.svg',
//                                 height: 20,
//                                 width: 40,
//                                 fit: BoxFit.fill,
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     )
//                   ],
//                 ),
//                 Center(
//                   child: Stack(
//                     alignment: Alignment.center,
//                     children: [
//                       StreamBuilder<CompassXEvent>(
//                         stream: CompassX.events,
//                         builder: (context, snapshot) {
//                           if (snapshot.hasError) {
//                             debugPrint('amin ${snapshot.hasError}');
//                             return const SizedBox();
//                           }
//                           if (!snapshot.hasData) {
//                             return const Center(
//                                 child: CircularProgressIndicator());
//                           }
//                           final compass = snapshot.data!;

//                           // debugPrint('aminaminq1- ${compass.heading}');

//                           return Container(
//                             width: 110.0,
//                             height: 110.0,
//                             decoration: BoxDecoration(
//                               color: !compass.shouldCalibrate
//                                   ? AppColor.kRedColor
//                                   : isBetweenExclusive
//                                       ? AppColor.kGreenColor
//                                       : AppColor.kOrangeColor,
//                               shape: BoxShape.circle,
//                               border: Border.all(
//                                 color: Theme.of(context).primaryColor,
//                                 width: 3.0,
//                               ),
//                             ),
//                           );
//                         },
//                       ),
//                       Container(
//                         width: 85.0,
//                         height: 85.0,
//                         decoration: BoxDecoration(
//                           color: Theme.of(context).scaffoldBackgroundColor,
//                           shape: BoxShape.circle,
//                           boxShadow: const [
//                             BoxShadow(
//                               blurRadius: 2,
//                               spreadRadius: 2,
//                               color: AppColor.kBlackColor,
//                             )
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 SvgPicture.asset(
//                   PrayerController.instance.qiblahDegree.value == null
//                       ? 'assets/svgs/kaaba-2.svg'
//                       : isBetweenExclusive
//                           ? 'assets/svgs/kaaba-1.svg'
//                           : 'assets/svgs/kaaba-2.svg',
//                   height: 30,
//                   width: 30,
//                 ),
//               ],
//             ),
//           ),
//         ],
//       );
//     });
//   }
// }

// class CompassWidget extends StatelessWidget {
//   const CompassWidget({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return StreamBuilder<CompassXEvent>(
//         stream: CompassX.events,
//         builder: (context, snapshot) {
//           if (snapshot.hasError) {
//             return const SizedBox();
//           }
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(
//               child: SizedBox(),
//             );
//           }
//           final compass = snapshot.data!;
//           double? direction = compass.heading;

//           if (direction == 0.0) {
//             return const QiblahMaps();
//           }
//           return AspectRatio(
//             aspectRatio: 1,
//             child: Container(
//               alignment: Alignment.center,
//               decoration: const BoxDecoration(
//                 shape: BoxShape.circle,
//               ),
//               child: Transform.rotate(
//                 angle: direction * (math.pi / 180) * -1,
//                 child: Stack(
//                   alignment: Alignment.center,
//                   children: [
//                     SvgPicture.asset(
//                       AppSvgs.kCompass,
//                       fit: BoxFit.fill,
//                       colorFilter: ColorFilter.mode(
//                         PrayerController.instance.qiblahDegree.value == null
//                             ? AppColor.kRedColor
//                             : (direction.toInt() >=
//                                         PrayerController
//                                                 .instance.qiblahDegree.value!
//                                                 .toInt() -
//                                             2 &&
//                                     direction.toInt() <=
//                                         PrayerController
//                                                 .instance.qiblahDegree.value!
//                                                 .toInt() +
//                                             2)
//                                 ? AppColor.kGreenColor
//                                 : AppColor.kRedColor,
//                         BlendMode.srcIn,
//                       ),
//                     ),
//                     Transform.rotate(
//                       angle:
//                           ((Get.find<PrayerController>().qiblahDegree.value ??
//                                   0) *
//                               (math.pi / 180)),
//                       child: Transform.translate(
//                         offset: Offset(0, -60.h),
//                         child: SvgPicture.asset(
//                           AppSvgs.kQiblah,
//                           height: 70.w,
//                           width: 70.w,
//                         ),
//                       ),
//                     ),
//                     GetBuilder<PrayerController>(builder: (controller) {
//                       return Padding(
//                         padding: const EdgeInsets.only(top: 8),
//                         child: GestureDetector(
//                           onTap: () => Feedback.forTap(context),
//                           child: CustomText(
//                             '°${controller.qiblahDegree.toStringAsFixed(1)}',
//                             style: TextStyle(fontSize: 16.sp),
//                           ),
//                         ),
//                       );
//                     }),
//                     Positioned(
//                       right: 0.08.sw,
//                       child: Padding(
//                         padding: const EdgeInsets.only(top: 8),
//                         child: CustomText(
//                           'East',
//                           style: TextStyle(fontSize: 16.sp),
//                         ),
//                       ),
//                     ),
//                     Positioned(
//                       left: 0.08.sw,
//                       child: Padding(
//                         padding: const EdgeInsets.only(top: 8),
//                         child: CustomText(
//                           'West',
//                           style: TextStyle(fontSize: 16.sp),
//                         ),
//                       ),
//                     ),
//                     PositionedDirectional(
//                       top: 0.07.sw,
//                       child: Padding(
//                         padding: const EdgeInsets.only(top: 8),
//                         child: CustomText(
//                           'North',
//                           style: TextStyle(fontSize: 16.sp),
//                         ),
//                       ),
//                     ),
//                     Positioned(
//                       bottom: 0.07.sw,
//                       child: Padding(
//                         padding: const EdgeInsets.only(top: 8),
//                         child: CustomText(
//                           'South',
//                           style: TextStyle(fontSize: 16.sp),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           );
//         });
//   }
// }

// class CompassCalibrationPopup extends StatefulWidget {
//   const CompassCalibrationPopup({super.key});

//   @override
//   State<CompassCalibrationPopup> createState() =>
//       _CompassCalibrationPopupState();
// }

// class _CompassCalibrationPopupState extends State<CompassCalibrationPopup> {
//   @override
//   Widget build(BuildContext context) {
//     return StreamBuilder<CompassXEvent>(
//       stream: CompassX.events,
//       builder: (context, snapshot) {
//         if (snapshot.hasError) {
//           return const SizedBox();
//         }
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return const Center(
//             child: SizedBox(),
//           );
//         }

//         final compass = snapshot.data!;
//         final accuracy = compass.accuracy;

//         if (!compass.shouldCalibrate) {
//           return const SizedBox.shrink();
//         }
//         return Padding(
//           padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
//           child: Row(
//             children: [
//               CustomText('Compass Accuracy: '),
//               const SizedBox(
//                 width: 8,
//               ),
//               CustomText(
//                 accuracy <= 0 ? 'Good' : 'Bad',
//                 style: TextStyle(
//                   color: accuracy <= 0 ? Colors.green : Colors.red,
//                 ),
//               ),
//               const Spacer(),
//               InkWell(
//                 child: Padding(
//                   padding: const EdgeInsets.all(8.0),
//                   child: CustomText(
//                     'Calibrate',
//                     style: TextStyle(
//                         fontSize: 13.sp, color: AppColor.kOrangeColor),
//                   ),
//                 ),
//                 onTap: () {
//                   WidgetsBinding.instance.addPostFrameCallback((_) {
//                     showDialog(
//                       context: context,
//                       builder: (dialogContext) => AlertDialog(
//                         contentPadding: const EdgeInsets.all(30),
//                         backgroundColor: Theme.of(context).primaryColor,
//                         content: DefaultTextStyle(
//                           textAlign: TextAlign.start,
//                           style: Theme.of(context)
//                               .textTheme
//                               .titleSmall!
//                               .copyWith(color: Colors.black),
//                           child: StreamBuilder<CompassXEvent>(
//                             stream: CompassX.events,
//                             initialData: compass,
//                             builder: (context, snapshot) {
//                               if (snapshot.hasError) {
//                                 return Text(snapshot.error.toString());
//                               }
//                               if (!snapshot.hasData) {
//                                 return const CircularProgressIndicator();
//                               }

//                               final dialogCompass = snapshot.data!;

//                               return SingleChildScrollView(
//                                 child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     CustomText('Compass accuracy is low?'),
//                                     const SizedBox(height: 5),
//                                     CustomText(
//                                       'Kindly rotate your phone and make an 8 in the air in order to get accurate Qibla directions.',
//                                       style: const TextStyle(fontSize: 12),
//                                     ),
//                                     Image.asset(
//                                       "assets/gif/compass-calibration.gif",
//                                       height: 300.0,
//                                       width: 250.0,
//                                     ),
//                                     Row(
//                                       children: [
//                                         CustomText("Compass Accuracy: "),
//                                         const SizedBox(
//                                           width: 8,
//                                         ),
//                                         CustomText(
//                                           dialogCompass.accuracy <= 0
//                                               ? 'Good'
//                                               : 'Bad',
//                                           style: TextStyle(
//                                             color: dialogCompass.accuracy <= 0
//                                                 ? AppColor.kGreenColor
//                                                 : AppColor.kRedColor,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ],
//                                 ),
//                               );
//                             },
//                           ),
//                         ),
//                         actionsAlignment: MainAxisAlignment.center,
//                         actions: [
//                           IconButton(
//                               onPressed: () {
//                                 Navigator.of(context).pop();
//                               },
//                               icon: const Icon(
//                                 Icons.close,
//                                 color: Colors.white,
//                               ))
//                         ],
//                       ),
//                     );
//                   });
//                 },
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }
// }

// extension RangeCheck on num {
//   bool isBetween(num lower, num upper,
//       {bool includeLower = true, bool includeUpper = true}) {
//     if (includeLower && includeUpper) {
//       return this >= lower && this <= upper;
//     } else if (includeLower && !includeUpper) {
//       return this >= lower && this < upper;
//     } else if (!includeLower && includeUpper) {
//       return this > lower && this <= upper;
//     } else {
//       return this > lower && this < upper;
//     }
//   }
// }
