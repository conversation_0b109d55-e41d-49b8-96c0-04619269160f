import 'dart:async';
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sensors_plus/sensors_plus.dart';

class BubbleLevelScreen extends StatefulWidget {
  const BubbleLevelScreen({super.key});

  @override
  State<BubbleLevelScreen> createState() => _BubbleLevelScreenState();
}

class _BubbleLevelScreenState extends State<BubbleLevelScreen>
    with TickerProviderStateMixin {
  static const double alpha = 0.3;

  late AnimationController _controller;
  double _currentPitch = 0.0;
  double _currentRoll = 0.0;
  double _targetPitch = 0.0;
  double _targetRoll = 0.0;

  StreamSubscription<AccelerometerEvent>? _accelSub;
  Orientation _currentOrientation = Orientation.portrait;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 16),
    )..addListener(() {
        _currentPitch += (_targetPitch - _currentPitch) * 0.3;
        _currentRoll += (_targetRoll - _currentRoll) * 0.3;
        setState(() {});
      });
    _controller.repeat();

    _accelSub = accelerometerEventStream().listen((event) {
      double x = event.x;
      double y = event.y;
      if (_currentOrientation == Orientation.landscape) {
        x = event.y;
        y = -event.x;
      }

      final double rawPitch =
          (180 / math.pi) * math.atan2(x, math.sqrt(y * y + event.z * event.z));
      final double rawRoll =
          (180 / math.pi) * math.atan2(y, math.sqrt(x * x + event.z * event.z));

      _targetPitch = alpha * rawPitch + (1 - alpha) * _targetPitch;
      _targetRoll = alpha * rawRoll + (1 - alpha) * _targetRoll;
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _accelSub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _currentOrientation = MediaQuery.of(context).orientation;
    final bool isLevel = _currentPitch.abs() < 5 && _currentRoll.abs() < 5;

    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Container(
          //   padding: const EdgeInsets.all(16),
          //   decoration: BoxDecoration(
          //     color: Colors.white10,
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          //   child: Column(
          //     children: [
          //       Text('Pitch: ${_currentPitch.toStringAsFixed(1)}°',
          //           style: const TextStyle(color: Colors.white)),
          //       Text('Roll: ${_currentRoll.toStringAsFixed(1)}°',
          //           style: const TextStyle(color: Colors.white)),
          //       const SizedBox(height: 8),
          //       Text(isLevel ? 'Level' : 'Adjust Device',
          //           style: TextStyle(
          //               color: isLevel ? Colors.green : Colors.white70)),
          //     ],
          //   ),
          // ),
          // const SizedBox(height: 40),
          // Bubble Level
          // BubbleLevelWidget(pitch: _currentPitch, roll: _currentRoll),
        ],
      ),
    );
  }
}

class BubbleLevelWidget extends StatefulWidget {
  final double pitch;
  final double roll;

  const BubbleLevelWidget({super.key, required this.pitch, required this.roll});

  @override
  State<BubbleLevelWidget> createState() => _BubbleLevelWidgetState();
}

class _BubbleLevelWidgetState extends State<BubbleLevelWidget> {
  ui.Image? _frameImage;
  ui.Image? _bubbleImage;
  static const double _bubbleSize = 25.0;

  @override
  void initState() {
    super.initState();
    _loadImages();
  }

  Future<void> _loadImages() async {
    _frameImage = await _loadImage('assets/images/level_frame.png');
    _bubbleImage = await _loadImage('assets/images/bubble.png');
    setState(() {});
  }

  Future<ui.Image> _loadImage(String assetPath) async {
    final ByteData data = await rootBundle.load(assetPath);
    final codec = await ui.instantiateImageCodec(data.buffer.asUint8List());
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  @override
  Widget build(BuildContext context) {
    if (_frameImage == null || _bubbleImage == null) {
      return const CircularProgressIndicator();
    }

    return SizedBox(
      width: 120,
      height: 120,
      child: CustomPaint(
        painter: _BubblePainter(
          pitch: widget.pitch,
          roll: widget.roll,
          frameImage: _frameImage!,
          bubbleImage: _bubbleImage!,
          bubbleSize: _bubbleSize,
        ),
      ),
    );
  }
}

class _BubblePainter extends CustomPainter {
  final double pitch;
  final double roll;
  final ui.Image frameImage;
  final ui.Image bubbleImage;
  final double bubbleSize;
  static const double maxTilt = 15.0;

  _BubblePainter({
    required this.pitch,
    required this.roll,
    required this.frameImage,
    required this.bubbleImage,
    required this.bubbleSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw frame
    canvas.drawImageRect(
      frameImage,
      Rect.fromLTWH(
          0, 0, frameImage.width.toDouble(), frameImage.height.toDouble()),
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint(),
    );

    // Calculate bubble position
    final center = Offset(size.width / 2, size.height / 2);
    final maxOffset = (size.width / 2) - (bubbleSize / 2);
    final scale = maxOffset / maxTilt;

    double dx = roll * scale;
    double dy = -pitch * scale;
    final distance = math.sqrt(dx * dx + dy * dy);
    if (distance > maxOffset) {
      dx *= maxOffset / distance;
      dy *= maxOffset / distance;
    }

    // Draw bubble
    final bubblePos = center + Offset(dx, dy);
    canvas.drawImageRect(
      bubbleImage,
      Rect.fromLTWH(
          0, 0, bubbleImage.width.toDouble(), bubbleImage.height.toDouble()),
      Rect.fromCenter(
        center: bubblePos,
        width: bubbleSize,
        height: bubbleSize,
      ),
      Paint(),
    );
  }

  @override
  bool shouldRepaint(_BubblePainter oldDelegate) =>
      oldDelegate.pitch != pitch ||
      oldDelegate.roll != roll ||
      oldDelegate.bubbleImage != bubbleImage ||
      oldDelegate.frameImage != frameImage;
}
