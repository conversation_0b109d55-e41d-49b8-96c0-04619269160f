import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_theme.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/presentation/widgets/appearance_color_circle.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsApearanceScreen extends StatelessWidget {
  const SettingsApearanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AppTheme>(
        builder: (controller) => Scaffold(
              body: SafeArea(
                child: Stack(
                  children: [
                    Image.asset(
                      AppImages.kMainbg,
                      height: double.infinity,
                      width: double.infinity,
                      fit: BoxFit.fill,
                    ),
                    SingleChildScrollView(
                      child: Column(
                        children: [
                          const SettingsMainBackground(),
                          SmoothEdgesContainer(
                            borderRadius: BorderRadius.circular(60.r),
                            width: double.infinity,
                            color: Theme.of(context).primaryColor,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                48.verticalSpace,
                                const SettingsAppBar(
                                    title: 'Settings', subtitle: 'Appearance'),
                                24.verticalSpace,
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 32.w),
                                  child: CustomText(
                                    'Change app primary color',
                                    style: TextStyle(
                                        color: AppColor.kWhiteColor
                                            .withOpacity(0.6)),
                                  ),
                                ),
                                16.verticalSpace,
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 48.w),
                                  child: GridView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: colors.length,
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 5,
                                      crossAxisSpacing: 8.w,
                                      mainAxisSpacing: 8.w,
                                    ),
                                    itemBuilder:
                                        (BuildContext context, int index) =>
                                            AppearanceColorCircle(
                                                colorIndex: index),
                                  ),
                                ),
                                0.7.sh.verticalSpace,
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }
}
