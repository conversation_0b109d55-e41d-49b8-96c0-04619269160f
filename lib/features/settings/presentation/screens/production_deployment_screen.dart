import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/config/production_deployment_config.dart';
import '../../../../core/services/production_monitoring_service.dart';
import '../../../../core/services/notification_service_manager.dart';
import '../../../../core/utils/custom_text.dart';

class ProductionDeploymentScreen extends StatefulWidget {
  const ProductionDeploymentScreen({super.key});

  @override
  State<ProductionDeploymentScreen> createState() => _ProductionDeploymentScreenState();
}

class _ProductionDeploymentScreenState extends State<ProductionDeploymentScreen> {
  bool _isLoading = false;
  String _statusMessage = '';
  Map<String, dynamic> _deploymentStatus = {};
  Map<String, dynamic> _monitoringReport = {};
  Map<String, dynamic> _systemHealth = {};

  @override
  void initState() {
    super.initState();
    _loadStatus();
  }

  void _loadStatus() {
    setState(() {
      _deploymentStatus = ProductionDeploymentConfig.getDeploymentStatus();
      _monitoringReport = ProductionMonitoringService.getMonitoringReport();
      _systemHealth = ProductionMonitoringService.getSystemHealth();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomText('Production Deployment'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusMessage(),
                  SizedBox(height: 16.h),
                  _buildDeploymentStatus(),
                  SizedBox(height: 16.h),
                  _buildSystemHealth(),
                  SizedBox(height: 16.h),
                  _buildDeploymentControls(),
                  SizedBox(height: 16.h),
                  _buildMonitoringReport(),
                  SizedBox(height: 16.h),
                  _buildQuickActions(),
                ],
              ),
            ),
    );
  }

  Widget _buildStatusMessage() {
    if (_statusMessage.isEmpty) return const SizedBox.shrink();
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: CustomText(
        _statusMessage,
        style: TextStyle(
          color: Colors.blue.shade800,
          fontSize: 14.sp,
        ),
      ),
    );
  }

  Widget _buildDeploymentStatus() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              'Deployment Status',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildStatusRow('Mode', _deploymentStatus['mode'] ?? 'Unknown'),
            _buildStatusRow('User Group', _deploymentStatus['userGroup'] ?? 'Unknown'),
            _buildStatusRow('Rollout %', '${_deploymentStatus['rolloutPercentage'] ?? 0}%'),
            _buildStatusRow('Using Awesome', _deploymentStatus['shouldUseAwesome']?.toString() ?? 'Unknown'),
            _buildStatusRow('Version', _deploymentStatus['deploymentVersion'] ?? 'Unknown'),
            if (_deploymentStatus['isForced'] == true)
              _buildStatusRow('Override', 'Developer Force Enabled', color: Colors.orange),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemHealth() {
    final status = _systemHealth['status'] ?? 'unknown';
    Color statusColor = Colors.green;
    if (status == 'warning') statusColor = Colors.orange;
    if (status == 'critical') statusColor = Colors.red;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomText(
                  'System Health',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: CustomText(
                    status.toUpperCase(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            _buildStatusRow('Awesome Success', '${_systemHealth['awesome_success_rate']?.toStringAsFixed(1) ?? '0'}%'),
            _buildStatusRow('Legacy Success', '${_systemHealth['legacy_success_rate']?.toStringAsFixed(1) ?? '0'}%'),
            _buildStatusRow('Recent Errors (24h)', '${_systemHealth['recent_errors_24h'] ?? 0}'),
            SizedBox(height: 8.h),
            CustomText(
              'Recommendation: ${_systemHealth['recommendation'] ?? 'Continue monitoring'}',
              style: TextStyle(
                fontSize: 12.sp,
                fontStyle: FontStyle.italic,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeploymentControls() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              'Deployment Controls',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: [
                _buildPresetButton('Conservative', 'conservative'),
                _buildPresetButton('Beta (10%)', 'beta'),
                _buildPresetButton('Gradual 25%', 'gradual_25'),
                _buildPresetButton('Gradual 50%', 'gradual_50'),
                _buildPresetButton('Gradual 75%', 'gradual_75'),
                _buildPresetButton('Full (100%)', 'full'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonitoringReport() {
    final deliveryMetrics = _monitoringReport['delivery_metrics'] as Map<String, dynamic>? ?? {};
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              'Monitoring Report',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildStatusRow('Awesome Scheduled', '${deliveryMetrics['awesome_scheduled'] ?? 0}'),
            _buildStatusRow('Awesome Delivered', '${deliveryMetrics['awesome_delivered'] ?? 0}'),
            _buildStatusRow('Legacy Scheduled', '${deliveryMetrics['legacy_scheduled'] ?? 0}'),
            _buildStatusRow('Legacy Delivered', '${deliveryMetrics['legacy_delivered'] ?? 0}'),
            _buildStatusRow('Prayer Notifications', '${deliveryMetrics['prayer_notifications'] ?? 0}'),
            _buildStatusRow('Athkar Notifications', '${deliveryMetrics['athkar_notifications'] ?? 0}'),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _refreshStatus,
                    child: CustomText('Refresh Status'),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _exportMetrics,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                    child: CustomText('Export Metrics', style: TextStyle(color: Colors.white)),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _resetMetrics,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                    child: CustomText('Reset Metrics', style: TextStyle(color: Colors.white)),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _resetConfiguration,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                    child: CustomText('Reset Config', style: TextStyle(color: Colors.white)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, {Color? color}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText(
            label,
            style: TextStyle(fontSize: 14.sp),
          ),
          CustomText(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButton(String label, String preset) {
    return ElevatedButton(
      onPressed: () => _applyPreset(preset),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      ),
      child: CustomText(
        label,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12.sp,
        ),
      ),
    );
  }

  Future<void> _applyPreset(String preset) async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Applying preset: $preset...';
    });

    try {
      await ProductionDeploymentConfig.applyPreset(preset);
      await NotificationServiceManager.initialize(); // Reinitialize with new config
      
      setState(() {
        _statusMessage = 'Preset applied successfully: $preset';
      });
      
      _loadStatus();
    } catch (e) {
      setState(() {
        _statusMessage = 'Error applying preset: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _refreshStatus() {
    setState(() {
      _statusMessage = 'Status refreshed';
    });
    _loadStatus();
  }

  void _exportMetrics() {
    final metrics = ProductionMonitoringService.exportMetricsAsJson();
    if (kDebugMode) {
      print('📊 Exported Metrics:\n$metrics');
    }
    setState(() {
      _statusMessage = 'Metrics exported to debug console';
    });
  }

  Future<void> _resetMetrics() async {
    await ProductionMonitoringService.resetMetrics();
    setState(() {
      _statusMessage = 'All metrics reset';
    });
    _loadStatus();
  }

  Future<void> _resetConfiguration() async {
    await ProductionDeploymentConfig.resetConfiguration();
    await NotificationServiceManager.initialize(); // Reinitialize
    setState(() {
      _statusMessage = 'Configuration reset to defaults';
    });
    _loadStatus();
  }
}
