// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    debugPrint(Get.height.toString());
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SizedBox(
              height: Get.height,
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  Expanded(
                    child: SmoothEdgesContainer(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(60.r)),
                      width: double.infinity,
                      color: Theme.of(context).primaryColor,
                      child: Column(
                        children: [
                          48.verticalSpace,
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 35.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                CustomText(
                                  'About Us',
                                  style: TextStyle(
                                    fontSize: 21.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                InkWell(
                                  onTap: () => Get.back(),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: SvgPicture.asset(AppSvgs.kClose),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // 24.verticalSpace,
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: Column(
                                children: [
                                  const Spacer(),
                                  GestureDetector(
                                    onTap: () async {
                                      var url = 'https://www.mp3quran.net';
                                      if (await canLaunchUrl(Uri.parse(url))) {
                                        await launchUrl(Uri.parse(url));
                                      } else {
                                        throw 'Could not launch $url';
                                      }
                                    },
                                    child: SmoothEdgesContainer(
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(60.r),
                                      ),
                                      color: AppColor.kRectangleColor,
                                      padding: const EdgeInsets.all(24),
                                      width: 130,
                                      child: AspectRatio(
                                        aspectRatio: 1,
                                        child: SvgPicture.asset(
                                          AppSvgs.kLogo,
                                        ),
                                      ),
                                    ),
                                  ),
                                  32.verticalSpace,
                                  SizedBox(
                                    width: 300,
                                    child: CustomText(
                                      "About App".tr,
                                      textAlign: TextAlign.center,
                                      height: 1.5,
                                    ),
                                  ),
                                  IconButton(
                                      onPressed: () async {
                                        var url = 'https://www.mp3quran.net';
                                        if (await canLaunchUrl(
                                            Uri.parse(url))) {
                                          await launchUrl(Uri.parse(url));
                                        } else {
                                          throw 'Could not launch $url';
                                        }
                                      },
                                      icon: const Icon(
                                        Icons.link,
                                        color: Colors.white,
                                      )),
                                  // const Spacer(),
                                  30.verticalSpace,
                                  CustomText('Developed By:'.tr),
                                  GestureDetector(
                                      onTap: () {
                                        launchUrlString(
                                            'https://wa.me/967773095254',
                                            mode: LaunchMode
                                                .externalNonBrowserApplication);
                                      },
                                      child: SvgPicture.asset(
                                          AppSvgs.kSmartFingersLogoDark)),
                                  const Spacer(),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
