import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/auth/data/models/get_auth_model.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_state.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    GetAuthModel profile = GetAuthModel.fromJson(cacheMemory.read(PROFILE));
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    padding: EdgeInsets.symmetric(horizontal: 35.w),
                    child: Column(
                      children: [
                        48.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              'Settings',
                              style: TextStyle(
                                fontSize: 21.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            InkWell(
                                onTap: () => Get.back(),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: SvgPicture.asset(AppSvgs.kClose),
                                )),
                          ],
                        ),
                        0.06.sh.verticalSpace,
                        Container(
                          height: 100.h,
                          width: 100.h,
                          padding: EdgeInsets.all(3.w),
                          decoration: BoxDecoration(
                            color: AppColor.kRectangleColor,
                            shape: BoxShape.circle,
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(100),
                            child: CachedNetworkImage(
                              imageUrl: profile.userImage,
                              placeholder: (context, url) =>
                                  const CustomShimmer(radius: 100),
                              errorWidget: (context, url, error) =>
                                  SvgPicture.asset(AppSvgs.kProfile),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        16.verticalSpace,
                        SizedBox(
                          width: double.infinity,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            alignment: Alignment.center,
                            child: CustomText(
                              profile.userName,
                              style: TextStyle(
                                fontSize: 26.sp,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: double.infinity,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            alignment: Alignment.center,
                            child: CustomText(
                              profile.userEmail,
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColor.kWhiteColor.withOpacity(0.5)),
                            ),
                          ),
                        ),
                        16.verticalSpace,
                        BlocBuilder<AuthCubit, AuthState>(
                            builder: (context, state) {
                          return state is LogOutLoading
                              ? const CircularProgressIndicator()
                              : InkWell(
                                  onTap: () =>
                                      BlocProvider.of<AuthCubit>(context)
                                          .logOut(),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppColor.kOrangeColor,
                                      borderRadius: BorderRadius.circular(5.r),
                                    ),
                                    padding: EdgeInsets.symmetric(
                                        vertical: 16.h, horizontal: 20.w),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        SvgPicture.asset(AppSvgs.kLogout),
                                        8.horizontalSpace,
                                        CustomText(
                                          'Signout',
                                          style: const TextStyle(
                                            color: Color(0xff172343),
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                        }),
                        0.4.sh.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
