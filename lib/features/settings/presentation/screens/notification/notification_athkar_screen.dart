import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

import '../../controller/settings_controller.dart';

class NotificationAthkarScreen extends StatelessWidget {
  const NotificationAthkarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Settings', subtitle: 'Athkar'),
                        24.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            children: [
                              // Container(
                              //   padding: EdgeInsets.all(24.w),
                              //   decoration: BoxDecoration(
                              //     color: AppColor.kRectangleColor,
                              //     borderRadius: BorderRadius.circular(21.r),
                              //   ),
                              //   child: const SettingsSwitchItemBuilder(
                              //       title: 'Athkar'),
                              // ),
                              16.verticalSpace,
                              GetBuilder<SettingsController>(
                                  builder: (controller) {
                                return Container(
                                  padding: EdgeInsets.all(24.w),
                                  decoration: BoxDecoration(
                                    color: AppColor.kRectangleColor,
                                    borderRadius: BorderRadius.circular(21.r),
                                  ),
                                  child: Builder(builder: (context) {
                                    return Column(
                                      children: [
                                        SettingsSwitchItemBuilder(
                                          title: 'Morning athkar',
                                          switchValue: controller
                                              .isMorningNotificationOn.value,
                                          onChanged: (p0) => controller
                                              .changeMorningAthkarNotificationValue(
                                                  p0),
                                        ),
                                        4.verticalSpace,
                                        const SettingsDivider(),
                                        4.verticalSpace,
                                        SettingsSwitchItemBuilder(
                                          title: 'Evning athkar',
                                          switchValue: controller
                                              .isEveningAthkarNotificationOn
                                              .value,
                                          onChanged: (p0) => controller
                                              .changeEveningAthkarNotificationValue(
                                                  p0),
                                        ),
                                      ],
                                    );
                                  }),
                                );
                              }),
                              16.verticalSpace,
                              // Container(
                              //   padding: EdgeInsets.all(24.w),
                              //   decoration: BoxDecoration(
                              //     color: AppColor.kRectangleColor,
                              //     borderRadius: BorderRadius.circular(21.r),
                              //   ),
                              //   child: Column(
                              //     crossAxisAlignment: CrossAxisAlignment.start,
                              //     children: [
                              //       const SettingsSwitchItemBuilder(
                              //           title: 'Random'),
                              //       const SettingsDivider(),
                              //       CustomText('How many time'),
                              //       CustomText(
                              //         '30 times and more',
                              //         style: TextStyle(
                              //             fontSize: 12.sp,
                              //             color: AppColor.kWhiteColor
                              //                 .withOpacity(0.6)),
                              //       ),
                              //       8.verticalSpace,
                              //       Row(
                              //         mainAxisAlignment:
                              //             MainAxisAlignment.spaceBetween,
                              //         children: [
                              //           NotificationRandomAthkarButton(
                              //             title: 'High',
                              //             isSelected: true,
                              //           ),
                              //           NotificationRandomAthkarButton(
                              //               title: 'Medium'),
                              //           NotificationRandomAthkarButton(
                              //               title: 'Low'),
                              //           NotificationRandomAthkarButton(
                              //               title: 'Rare'),
                              //         ],
                              //       ),
                              //     ],
                              //   ),
                              // ),
                              32.verticalSpace,
                            ],
                          ),
                        ),
                        0.3.sh.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class NotificationRandomAthkarButton extends StatelessWidget {
  NotificationRandomAthkarButton({
    super.key,
    required this.title,
    this.isSelected = false,
  });
  final String title;
  bool isSelected;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: isSelected ? AppColor.kOrangeColor : null,
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      child: CustomText(
        title,
        style: TextStyle(fontSize: 11.sp),
      ),
    );
  }
}
