import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/settings/data/models/adhan_model.dart';
import 'package:salawati/features/settings/presentation/controller/audio_service_controller.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/screens/notification/settings_notification_screen.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

// Helper function to get athan name
String getAthanName(AthanSoundType type) {
  final sounds = SoundUtils.getAvailableAthanSounds();
  final soundInfo = sounds.firstWhere(
    (sound) => sound.type == type,
    orElse: () => sounds.first,
  );
  return Get.locale?.languageCode == 'ar' ? soundInfo.name : soundInfo.nameEn;
}

class AthanSoundScreen extends StatefulWidget {
  const AthanSoundScreen({super.key});

  @override
  State<AthanSoundScreen> createState() => _AthanSoundScreenState();
}

class _AthanSoundScreenState extends State<AthanSoundScreen> {
  late AudioService _audioService;
  SettingsController controller = SettingsController.instance;
  bool _isServiceInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAudioService();
  }

  Future<void> _initializeAudioService() async {
    _audioService = await Get.putAsync<AudioService>(() async {
      final service = AudioService();
      service.onInit();
      return service;
    });
    setState(() {
      _isServiceInitialized = true;
    });
  }

  void _playSelectedAudio(AthanSoundType athanSoundType) async {
    if (_isServiceInitialized) {
      await _audioService.audioPlayer.stop();
      final soundPath = SoundUtils.getAthanSoundPath(
          athanSoundType, true); // Use short sounds for preview
      await _audioService.audioPlayer.setAsset(soundPath);
      await _audioService.audioPlayer.play();
    }
  }

  void _handleSelection(AthanSoundType athanSoundType) {
    setState(() {
      controller.setAdhanSound(athanSoundType.index);
    });
    _playSelectedAudio(athanSoundType);
  }

  @override
  void dispose() {
    if (_isServiceInitialized) {
      Get.delete<AudioService>(); // Crucial for cleanup!
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SettingsController controller = SettingsController.instance;
    if (!_isServiceInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Adhan', subtitle: 'Adhan Sound'),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            children: [
                              16.verticalSpace,
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.all(24.w),
                                color: AppColor.kRectangleColor,
                                child: SettingsSwitchItemBuilder(
                                  title: "Default Notification Sound".tr,
                                  switchValue:
                                      controller.isAdhanDefaultNotification(),
                                  onChanged: (value) async {
                                    setState(
                                      () {
                                        controller
                                            .setAdhanSoundToDefaultNotification(
                                                value);
                                      },
                                    );
                                    await _audioService.audioPlayer.stop();
                                  },
                                ),
                              ),
                              16.verticalSpace,
                              if (!controller.isAdhanDefaultNotification())
                                SmoothEdgesContainer(
                                  borderRadius: BorderRadius.circular(60.r),
                                  padding: EdgeInsets.all(24.w),
                                  color: AppColor.kRectangleColor,
                                  child: ListView.separated(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      return AthanSoundOption2(
                                          athanSoundType:
                                              AthanSoundType.values[index],
                                          selected: index ==
                                              controller.getAdhanSound(),
                                          onSelection: _handleSelection

                                          //  (athanSoundType) {
                                          //   setState(() {
                                          //     controller.setAdhanSound(index);
                                          //   });
                                          // },
                                          );
                                    },
                                    separatorBuilder:
                                        (BuildContext context, int index) {
                                      return const SettingsDivider(
                                        verticalMargin: 16,
                                      );
                                    },
                                    itemCount: AthanSoundType.values.length,
                                  ),
                                ),
                              16.verticalSpace,
                              // Test notification button
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.all(24.w),
                                color: AppColor.kRectangleColor,
                                child: InkWell(
                                  onTap: () async {
                                    try {
                                      final prayerController =
                                          Get.find<PrayerController>();
                                      await prayerController
                                          .testAthanNotificationSound(
                                        soundIndex: controller.getAdhanSound(),
                                      );
                                    } catch (e) {
                                      Get.snackbar(
                                        'Error',
                                        'Failed to test notification: $e',
                                        duration: const Duration(seconds: 3),
                                      );
                                    }
                                  },
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.notifications_active,
                                        color: AppColor.kOrangeColor,
                                      ),
                                      16.horizontalSpace,
                                      CustomText(
                                        'Test Athan Notification',
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const Spacer(),
                                      const Icon(
                                        Icons.play_arrow,
                                        color: AppColor.kWhiteColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        0.5.sh.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AthanSoundOption2 extends StatelessWidget {
  const AthanSoundOption2({
    super.key,
    required this.athanSoundType,
    required this.selected,
    required this.onSelection,
  });

  final AthanSoundType athanSoundType;
  final bool selected;
  final void Function(AthanSoundType) onSelection;

  @override
  Widget build(BuildContext context) {
    // Access the AudioPlayer directly using Get.find<AudioService>().audioPlayer
    final AudioPlayer audioPlayer = Get.find<AudioService>().audioPlayer;

    return StreamBuilder<PlayerState>(
      stream: audioPlayer.playerStateStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox();
        }

        final playing = snapshot.data!.playing;

        return InkWell(
          onTap: () {
            onSelection(athanSoundType);
            if (playing) {
              audioPlayer.stop();
            } else {
              onSelection(athanSoundType);
            }
          },
          child: Row(
            children: [
              selected
                  ? const Icon(
                      Icons.radio_button_checked,
                      color: AppColor.kOrangeColor,
                    )
                  : const Icon(
                      Icons.radio_button_unchecked,
                      color: AppColor.kWhiteColor,
                    ),
              16.horizontalSpace,
              CustomText(
                getAthanName(athanSoundType),
              ),
              const Spacer(),
              IconButton(
                icon: SvgPicture.asset(
                  selected && playing ? AppSvgs.kMp3Pause : AppSvgs.kMp3Play,
                  width: 25,
                  colorFilter: const ColorFilter.mode(
                      AppColor.kWhiteColor, BlendMode.srcIn),
                ),
                onPressed: () {
                  if (playing) {
                    audioPlayer.stop();
                  } else {
                    onSelection(athanSoundType);
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class AthanSoundOption extends StatelessWidget {
  const AthanSoundOption({
    super.key,
    required this.athanSoundType,
    required this.selected,
    required this.onTap,
  });
  final AthanSoundType athanSoundType;
  final bool selected;
  final void Function(AthanSoundType athanSoundType) onTap;
  @override
  Widget build(BuildContext context) {
    var player = AudioPlayer();
    bool loaded = false;
    return InkWell(
      onTap: () => onTap(athanSoundType),
      child: Row(
        children: [
          selected
              ? const Icon(
                  Icons.radio_button_checked,
                  color: AppColor.kOrangeColor,
                )
              : const Icon(
                  Icons.radio_button_unchecked,
                  color: AppColor.kWhiteColor,
                ),
          16.horizontalSpace,
          CustomText(
            getAthanName(athanSoundType),
          ),
          const Spacer(),
          StreamBuilder(
              stream: player.playerStateStream,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const SizedBox();
                }
                var playing = snapshot.data?.playing ?? false;
                return IconButton(
                  icon: SvgPicture.asset(
                    playing ? AppSvgs.kMp3Pause : AppSvgs.kMp3Play,
                    width: 25,
                    colorFilter: const ColorFilter.mode(
                        AppColor.kWhiteColor, BlendMode.srcIn),
                  ),
                  onPressed: () {
                    if (!loaded) {
                      final soundPath = SoundUtils.getAthanSoundPath(
                          athanSoundType, true); // Use short sounds for preview
                      player.setAsset(soundPath);
                      loaded = true;
                    }
                    if (playing) {
                      player.pause();
                    } else {
                      player.play();
                    }
                  },
                );
              }),
        ],
      ),
    );
  }
}

class AthanSoundSelectionDialog extends StatelessWidget {
  final String prayerKey;

  const AthanSoundSelectionDialog({
    super.key,
    required this.prayerKey,
  });

  @override
  Widget build(BuildContext context) {
    final SettingsController controller = SettingsController.instance;
    final AdhanModel model = controller.athanNotificationsMap[prayerKey]!;
    // final SettingsController settingsController = Get.find();

    final NotificationPermissionController permissionController =
        Get.put(NotificationPermissionController());

    // Call the permission check once when the screen is initialized
    permissionController.checkAndRequestPermission();

    return AlertDialog(
      backgroundColor: Theme.of(context).primaryColor,
      title: Row(
        children: [
          Row(
            children: [
              CustomText(
                'Notification Settings:',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              6.horizontalSpace,
              CustomText(
                model.title.tr,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: CustomText('Use Default'.tr),
            onTap: () {
              // Set to mute
              model.isSilent = true;
              controller.setNotification(model, prayerKey);
              Get.back();
            },
          ),
          const Divider(color: AppColor.kWhiteColor),
          ListTile(
            title: CustomText('choose_adhan_sound'.tr),
            onTap: () {
              // Close this dialog and open sound selection dialog
              Get.back();
              Get.dialog(AthanSoundDialog(prayerKey: prayerKey));
            },
          ),
        ],
      ),
    );
  }
}

class AthanSoundDialog extends StatefulWidget {
  final String prayerKey;
  const AthanSoundDialog({super.key, required this.prayerKey});

  @override
  _AthanSoundDialogState createState() => _AthanSoundDialogState();
}

class _AthanSoundDialogState extends State<AthanSoundDialog> {
  late AudioService _audioService;
  late AdhanModel _model;
  final SettingsController controller = SettingsController.instance;
  bool _ready = false;
  int? _playingIndex;

  String _getAthanName(AthanSoundType type) {
    final sounds = SoundUtils.getAvailableAthanSounds();
    final soundInfo = sounds.firstWhere(
      (sound) => sound.type == type,
      orElse: () => sounds.first,
    );
    return Get.locale?.languageCode == 'ar' ? soundInfo.name : soundInfo.nameEn;
  }

  @override
  void initState() {
    super.initState();
    _model = controller.athanNotificationsMap[widget.prayerKey]!;
    _initAudioService();
  }

  Future<void> _initAudioService() async {
    _audioService = await Get.putAsync<AudioService>(() async {
      final svc = AudioService()..onInit();
      return svc;
    });
    setState(() => _ready = true);
  }

  Future<void> _playPause(AthanSoundType type, int idx) async {
    final player = _audioService.audioPlayer;
    final same = _playingIndex == idx;
    if (same && player.playing) {
      setState(() => _playingIndex = null);
      await player.stop();
    } else {
      if (_playingIndex != null && _playingIndex != idx) {
        setState(() => _playingIndex = null);
        await player.stop();
      }
      setState(() => _playingIndex = idx);
      await player.stop();
      final path = SoundUtils.getAthanSoundPathAssets(
          type, true); // Use short sounds for preview
      if (path != null) {
        await player.setAudioSource(AudioSource.asset(path));
        await player.play();
      }
    }
  }

  @override
  void dispose() {
    if (_ready) {
      _audioService.audioPlayer.stop();
      Get.delete<AudioService>();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).primaryColor,
      title: CustomText('choose_adhan_sound'),
      content: SizedBox(
        width: double.maxFinite,
        child: !_ready
            ? const Center(child: CircularProgressIndicator())
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // ─── CUSTOM ATHAN SOUNDS ─────────────────────────────
                  Flexible(
                    child: ListView.separated(
                      shrinkWrap: true,
                      separatorBuilder: (_, __) => const Divider(),
                      itemCount: AthanSoundType.values.length - 1,
                      itemBuilder: (_, rawIndex) {
                        final types = AthanSoundType.values.toList();
                        types.removeLast();
                        final type = types[rawIndex];
                        final idx = AthanSoundType.values.indexOf(type);

                        final isSelected =
                            _model.soundIndex == idx && !_model.isSilent;
                        final isPlaying = _playingIndex == idx;

                        return ListTile(
                          onTap: () {
                            // 1) disable global default/silent
                            controller
                                .setAdhanSoundToDefaultNotification(false);
                            // 2) save choice
                            _model.isSilent = false;
                            _model.soundIndex = idx;
                            controller.setNotification(
                                _model, widget.prayerKey);
                            // 3) close
                            Get.back();
                          },
                          leading: Icon(
                            isSelected
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: isSelected
                                ? AppColor.kOrangeColor
                                : AppColor.kWhiteColor,
                          ),
                          title: CustomText(_getAthanName(type)),
                          trailing: IconButton(
                            icon: SvgPicture.asset(
                              isPlaying ? AppSvgs.kMp3Pause : AppSvgs.kMp3Play,
                              width: 25,
                              colorFilter: const ColorFilter.mode(
                                  AppColor.kWhiteColor, BlendMode.srcIn),
                            ),
                            onPressed: () => _playPause(type, idx),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
