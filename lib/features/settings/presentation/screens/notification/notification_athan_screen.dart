import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/prayer_notification_widget.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class NotificationAthanScreen extends StatelessWidget {
  const NotificationAthanScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Settings', subtitle: 'Athan'),
                        24.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: GetBuilder<SettingsController>(
                              builder: (controller) {
                            return Column(
                              children: [
                                ListView.separated(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemBuilder: (context, index) =>
                                      PrayerNotificationWidget(
                                          title: prayers[index]),
                                  separatorBuilder: (context, index) =>
                                      16.verticalSpace,
                                  itemCount: prayers.length,
                                ),
                                32.verticalSpace,
                              ],
                            );
                          }),
                        ),
                        0.1.sh.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
