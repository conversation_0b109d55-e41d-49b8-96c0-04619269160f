import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/location/data/models/location_model.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/cubit/settings_cubit.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsLocationScreen extends StatefulWidget {
  const SettingsLocationScreen({super.key});

  @override
  State<SettingsLocationScreen> createState() => _SettingsLocationScreenState();
}

class _SettingsLocationScreenState extends State<SettingsLocationScreen> {
  final SettingsController _settingsController = Get.find();
  final SettingsSearchController _settingsSearchController = Get.find();

  final FocusNode _locationFocusNode = FocusNode(); // Add FocusNode
  Timer? _debounceTimer;

  void _onSearchTextChanged(String text) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (text.length >= 2) {
        handleLocationUpdate();
      }
    });
  }

  Future<void> handleLocationUpdate() async {
    try {
      final controller =
          _settingsController.locationTextEditingController.value;

      if (controller.text.isNotEmpty) {
        await _settingsSearchController.getLocalCitiesByName(
            cityName: controller.text);
      }
    } catch (e) {
      _settingsSearchController.errorMessage.value =
          'Failed to fetch location: $e';
    }
  }

  @override
  void dispose() {
    _locationFocusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                          title: 'Settings',
                          subtitle: 'Location',
                        ),
                        32.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            children: [
                              Obx(() => SmoothEdgesContainer(
                                    borderRadius: BorderRadius.circular(60.r),
                                    padding: EdgeInsets.all(24.w),
                                    color: AppColor.kRectangleColor,
                                    child: Column(
                                      children: [
                                        SettingsSwitchItemBuilder(
                                          title: 'Automatic',
                                          switchValue: _settingsController
                                              .isLocationAuto.value,
                                          onChanged: _settingsController
                                                  .isLoading.value
                                              ? null // Disable when loading
                                              : (value) async {
                                                  Get.log(
                                                      "amin changeLocationAuto $value");
                                                  await _settingsController
                                                      .changeLocationAuto(
                                                          value);
                                                },
                                        ),
                                        if (_settingsController.isLoading.value)
                                          Padding(
                                            padding: EdgeInsets.only(top: 8.h),
                                            child:
                                                const LinearProgressIndicator(),
                                          ),
                                      ],
                                    ),
                                  )),
                              20.verticalSpace,
                              Obx(() {
                                return Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      CustomText('Detected'),
                                      LocationService.instance.currentLocation
                                                  .value ==
                                              null
                                          ? CustomText('')
                                          : CustomText(
                                              LocationService
                                                      .instance
                                                      .currentLocation
                                                      .value
                                                      ?.cityName ??
                                                  "",
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: AppColor.kOrangeColor,
                                              ),
                                            ),
                                    ],
                                  ),
                                );
                              }),
                              // GetBuilder<PrayerController>(
                              //   builder: (prayerController) {
                              //     return Padding(
                              //       padding:
                              //           EdgeInsets.symmetric(horizontal: 16.w),
                              //       child: Row(
                              //         mainAxisAlignment:
                              //             MainAxisAlignment.spaceBetween,
                              //         children: [
                              //           CustomText('Detected'),
                              //           Obx(() {
                              //             if (prayerController
                              //                     .currentCity.value ==
                              //                 null) {
                              //               return SizedBox(
                              //                 height: 10.h,
                              //                 width: 100.w,
                              //                 child: const CustomShimmer(),
                              //               );
                              //             }
                              //             return CustomText(
                              //               prayerController.currentCity.value!,
                              //               style: const TextStyle(
                              //                 fontWeight: FontWeight.bold,
                              //                 color: AppColor.kOrangeColor,
                              //               ),
                              //             );
                              //           }),
                              //         ],
                              //       ),
                              //     );
                              //   },
                              // ),
                              20.verticalSpace,
                              Obx(() {
                                if (!_settingsController.isLocationAuto.value) {
                                  return SmoothEdgesContainer(
                                    borderRadius: BorderRadius.circular(60.r),
                                    padding: EdgeInsets.all(24.w),
                                    color: AppColor.kRectangleColor,
                                    child: Column(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: AppColor.kRectangleColor,
                                            borderRadius:
                                                BorderRadius.circular(15.r),
                                          ),
                                          child: CustomTextField(
                                            controller: _settingsController
                                                .locationTextEditingController
                                                .value,
                                            onChanged: _onSearchTextChanged,
                                            onFieldSubmitted: (text) =>
                                                handleLocationUpdate(),
                                            textInputAction: TextInputAction.go,
                                            hintText: 'Find Location....'.tr,
                                            prefixIconData: SvgPicture.asset(
                                                AppSvgs.kLocationAdd),
                                            suffixIcon: LocationSuffixIcon(
                                              // Use the SettingsController's controller here
                                              controller: _settingsController
                                                  .locationTextEditingController
                                                  .value,
                                              onTap: () {
                                                if (_settingsController
                                                    .locationTextEditingController
                                                    .value
                                                    .text
                                                    .isEmpty) {
                                                  handleLocationUpdate();
                                                }
                                              },
                                            ),
                                          ),
                                        ),
                                        16.verticalSpace,
                                        CitiesList(
                                          settingsSearchController:
                                              _settingsSearchController,
                                          settingsController:
                                              _settingsController,
                                        )
                                      ],
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              }),
                              0.5.sh.verticalSpace,
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CitiesList extends StatefulWidget {
  final SettingsSearchController settingsSearchController;
  final SettingsController settingsController;

  const CitiesList({
    super.key,
    required this.settingsSearchController,
    required this.settingsController,
  });

  @override
  State<CitiesList> createState() => _CitiesListState();
}

class _CitiesListState extends State<CitiesList> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (widget.settingsSearchController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      // if (widget.settingsSearchController.errorMessage.value.isNotEmpty) {
      //   return Center(
      //     child: Text(
      //       'Error: ${widget.settingsSearchController.errorMessage.value}',
      //       style: const TextStyle(color: Colors.red),
      //     ),
      //   );
      // }

      final cities = widget.settingsSearchController.searchedLocalCities;

      // if (cities.isEmpty) {
      //   return Center(
      //     child: CustomText('No results found.'),
      //   );
      // }

      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: cities.length,
        itemBuilder: (context, index) {
          final city = cities[index];
          final languageMap = {
            'en': city.englishName,
            'ar': city.arabicName,
          };
          final countryLanguageMap = {
            'en': city.countryName,
            'ar': city.countryArabicName,
          };

          final userLocale = Get.locale?.languageCode ?? 'ar';

          final cityNameKey = languageMap[userLocale] ?? '';
          final countryName = countryLanguageMap[userLocale] ?? '';
          return GestureDetector(
            onTap: () async {
              await widget.settingsController.setLocalLocation(
                cityNameKey,
                countryName,
                city.latitude,
                city.longitude,
              );

              // Clear in the correct order
              widget.settingsController.locationTextEditingController.value
                  .clear();
              widget.settingsSearchController.clearSearchResults();
              widget.settingsController.clearLocationText();

              // Explicitly update the suffix icon state
              if (mounted) setState(() {});
            },
            child: ListTile(
              onTap: () async {
                await widget.settingsController.setLocalLocation(
                  cityNameKey,
                  countryName,
                  city.latitude,
                  city.longitude,
                );

                LocationService.instance.currentLocation.value = LocationModel(
                  cityName: cityNameKey,
                  countryName: countryName,
                  latitude: city.latitude,
                  longitude: city.longitude,
                  isLocationAuto: false,
                  isManualLocation: true,
                );

                // Clear in the correct order
                widget.settingsController.locationTextEditingController.value
                    .clear();
                widget.settingsSearchController.clearSearchResults();
                widget.settingsController.clearLocationText();

                // Explicitly update the suffix icon state
                if (mounted) setState(() {});
              },
              title: CustomText(
                '$cityNameKey${countryName.isNotEmpty ? ' - $countryName' : ''}',
              ),
            ),
          );
        },
      );
    });
  }
}

class LocationSuffixIcon extends StatefulWidget {
  final TextEditingController? controller;
  final VoidCallback onTap;

  const LocationSuffixIcon({super.key, required this.onTap, this.controller});

  @override
  LocationSuffixIconState createState() => LocationSuffixIconState();
}

class LocationSuffixIconState extends State<LocationSuffixIcon> {
  bool hasLocationText = false;

  @override
  void initState() {
    super.initState();
    // Handle null safety
    hasLocationText = widget.controller?.text.isNotEmpty ?? false;
    widget.controller?.addListener(_updateState);
  }

  void _updateState() {
    if (mounted) {
      setState(() {
        hasLocationText = widget.controller?.text.isNotEmpty ?? false;
      });
    }
  }

  @override
  void dispose() {
    widget.controller?.removeListener(_updateState);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: hasLocationText ? () => widget.controller?.clear() : widget.onTap,
      child: CircleAvatar(
        backgroundColor: AppColor.kWhiteColor.withAlpha(153),
        child: Icon(
          hasLocationText ? Icons.clear : Icons.search,
        ),
      ),
    );
  }
}
