import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/controllers/number_format_controller.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_locale.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/features/settings/presentation/widgets/is_selected_item_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsLanguageScreen extends StatefulWidget {
  const SettingsLanguageScreen({super.key});

  @override
  State<SettingsLanguageScreen> createState() => _SettingsLanguageScreenState();
}

class _SettingsLanguageScreenState extends State<SettingsLanguageScreen> {
  Future<bool?> showLanguageChangeWarning(
      BuildContext context, String newLocale) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColor.kScaffoldColor,
          title: CustomText(
            'Language Change Warning',
            textAlign: TextAlign.center,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                'Please note that changing the language will not affect the display of Quranic verses (Quran Ayat), supplications (Daily Athkar), or prayers (Dua). These will remain in Arabic.',
                maxLines: 10,
                textScaleFactor: 0.9,
              ),
              CustomText(
                'Are you sure you want to change the language?',
                maxLines: 10,
                textScaleFactor: 0.9,
              ),
            ],
          ),
          actions: [
            TextButton(
              child: CustomText('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: CustomText('ok'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );
  }

  Future<void> changeLanguage(
      BuildContext context, String newLocale, AppLocale controller) async {
    final shouldChange = await showLanguageChangeWarning(context, newLocale);

    if (shouldChange ?? false) {
      controller.changeLang(newLocale);

      if (!context.mounted) return;
      final cubit = BlocProvider.of<QuranCubit>(context);
      await cubit.getReciters();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Settings', subtitle: 'Languages'),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: GetBuilder<AppLocale>(builder: (controller) {
                            return Column(
                              children: [
                                16.verticalSpace,
                                SmoothEdgesContainer(
                                  borderRadius: BorderRadius.circular(60.r),
                                  padding: EdgeInsets.all(24.w),
                                  color: AppColor.kRectangleColor,
                                  child: Column(
                                    children: [
                                      InkWell(
                                        onTap: () => changeLanguage(
                                            context, 'ar', controller),
                                        child: IsSelectedItemBuilder(
                                          title: 'Arabic',
                                          isSelected:
                                              controller.langCode == 'ar',
                                        ),
                                      ),
                                      const SettingsDivider(verticalMargin: 16),
                                      InkWell(
                                        onTap: () => changeLanguage(
                                            context, 'en', controller),
                                        child: IsSelectedItemBuilder(
                                          title: 'English',
                                          isSelected:
                                              controller.langCode == 'en',
                                        ),
                                      ),
                                      const SettingsDivider(verticalMargin: 16),
                                      InkWell(
                                        onTap: () => changeLanguage(
                                            context, 'fr', controller),
                                        child: IsSelectedItemBuilder(
                                          title: 'France',
                                          isSelected:
                                              controller.langCode == 'fr',
                                        ),
                                      ),
                                      const SettingsDivider(verticalMargin: 16),
                                      InkWell(
                                        onTap: () => changeLanguage(
                                            context, 'ur', controller),
                                        child: IsSelectedItemBuilder(
                                          title: 'Urdo',
                                          isSelected:
                                              controller.langCode == 'ur',
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                16.verticalSpace,
                                GetBuilder<NumberFormatController>(
                                  builder: (numberController) {
                                    return SmoothEdgesContainer(
                                      borderRadius: BorderRadius.circular(60.r),
                                      padding: EdgeInsets.all(24.w),
                                      color: AppColor.kRectangleColor,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          CustomText(
                                            'Number Format',
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          16.verticalSpace,
                                          InkWell(
                                            onTap: () => numberController
                                                .setNumberFormat(
                                                    NumberFormat.western),
                                            child: IsSelectedItemBuilder(
                                              title: 'Western Numerals',
                                              subTitle: '0 1 2 3 4 5 6',
                                              subtitleSize: 30,
                                              isSelected: numberController
                                                      .numberFormat ==
                                                  NumberFormat.western,
                                            ),
                                          ),
                                          const SettingsDivider(
                                              verticalMargin: 16),
                                          InkWell(
                                            onTap: () => numberController
                                                .setNumberFormat(
                                                    NumberFormat.arabicIndic),
                                            child: IsSelectedItemBuilder(
                                              title: 'Arabic-Indic Numerals',
                                              subTitle: ' ٠ ١ ٢ ٣ ٤ ٥ ٦',
                                              subtitleSize: 30,
                                              isSelected: numberController
                                                      .numberFormat ==
                                                  NumberFormat.arabicIndic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                                16.verticalSpace,
                              ],
                            );
                          }),
                        ),
                        0.5.sh.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
