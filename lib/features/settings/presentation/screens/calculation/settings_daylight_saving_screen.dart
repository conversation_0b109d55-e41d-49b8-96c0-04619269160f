import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/is_selected_item_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsDaylightSavingScreen extends StatelessWidget {
  const SettingsDaylightSavingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Settings', subtitle: 'Daylight Saving'),
                        32.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: GetBuilder<SettingsController>(
                              builder: (controller) {
                            return Column(
                              children: [
                                SmoothEdgesContainer(
                                  borderRadius: BorderRadius.circular(60.r),
                                  padding: EdgeInsets.all(24.w),
                                  decoration: BoxDecoration(
                                    color: AppColor.kRectangleColor,
                                    borderRadius: BorderRadius.circular(21.r),
                                  ),
                                  child: SettingsSwitchItemBuilder(
                                      title: 'Automatic',
                                      switchValue:
                                          controller.isSummerTimeAuto.value,
                                      onChanged: (value) => controller
                                          .changeSummerTimeAuto(value)),
                                ),
                                20.verticalSpace,
                                if (!controller.isSummerTimeAuto.value)
                                  SmoothEdgesContainer(
                                    borderRadius: BorderRadius.circular(60.r),
                                    padding: EdgeInsets.all(24.w),
                                    decoration: BoxDecoration(
                                      color: AppColor.kRectangleColor,
                                    ),
                                    child: Column(
                                      children: [
                                        InkWell(
                                          onTap: () =>
                                              controller.setSummerTime(1),
                                          child: IsSelectedItemBuilder(
                                            title: '+1',
                                            isSelected:
                                                controller.getSummerTime() == 1,
                                          ),
                                        ),
                                        const SettingsDivider(
                                            verticalMargin: 16),
                                        InkWell(
                                          onTap: () =>
                                              controller.setSummerTime(-1),
                                          child: IsSelectedItemBuilder(
                                            title: '-1',
                                            isSelected:
                                                controller.getSummerTime() ==
                                                    -1,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                0.5.sh.verticalSpace,
                              ],
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
