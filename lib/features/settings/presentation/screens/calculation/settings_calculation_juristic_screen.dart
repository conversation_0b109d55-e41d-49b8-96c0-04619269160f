import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/is_selected_item_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsCalculationJuristicScreen extends StatelessWidget {
  const SettingsCalculationJuristicScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Settings', subtitle: 'Juristic'),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            children: [
                              16.verticalSpace,
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.all(24.w),
                                decoration: BoxDecoration(
                                  color: AppColor.kRectangleColor,
                                ),
                                child: GetBuilder<SettingsController>(
                                    builder: (controller) {
                                  return Column(
                                    children: [
                                      InkWell(
                                        onTap: () => controller
                                            .changeMathhabHanafi(true),
                                        child: IsSelectedItemBuilder(
                                          title: 'Hanafi',
                                          isSelected:
                                              controller.isMathhabHanafi.value,
                                        ),
                                      ),
                                      const SettingsDivider(verticalMargin: 16),
                                      InkWell(
                                        onTap: () => controller
                                            .changeMathhabHanafi(false),
                                        child: IsSelectedItemBuilder(
                                          title: 'Shafii Hanbali and Maliki',
                                          isSelected:
                                              !controller.isMathhabHanafi.value,
                                        ),
                                      ),
                                    ],
                                  );
                                }),
                              ),
                              16.verticalSpace,
                            ],
                          ),
                        ),
                        0.5.sh.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
