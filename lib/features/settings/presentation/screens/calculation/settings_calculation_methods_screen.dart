import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/data/models/calculation_method_model.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/calculation_method_item_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsCalculationMethodsScreen extends StatelessWidget {
  const SettingsCalculationMethodsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                          title: 'Settings',
                          subtitle: 'Methods',
                        ),
                        24.verticalSpace,
                        Obx(() {
                          final controller = SettingsController.instance;
                          return Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 16.w,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CustomText(
                                        'Current method',
                                        style: const TextStyle(
                                          color: AppColor.kOrangeColor,
                                        ),
                                      ),
                                      4.verticalSpace,
                                      CustomText(
                                        controller
                                            .currentCalculationMethodTitle
                                            .value,
                                      ),
                                      4.verticalSpace,
                                      CustomText(
                                        'The selected method will be now used to calculate the prayer times',
                                        style: TextStyle(
                                          color: AppColor.kWhiteColor
                                              .withOpacity(0.6),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                16.verticalSpace,
                                SmoothEdgesContainer(
                                  borderRadius: BorderRadius.circular(60.r),
                                  padding: EdgeInsets.all(24.w),
                                  decoration: BoxDecoration(
                                    color: AppColor.kRectangleColor,
                                  ),
                                  child: SingleChildScrollView(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SettingsSwitchItemBuilder(
                                          title: 'Automatic',
                                          switchValue:
                                              controller
                                                  .isCalculationMethodAuto
                                                  .value,
                                          onChanged:
                                              (value) async => await controller
                                                  .changeCalculationMethodAuto(
                                                    value,
                                                  ),
                                        ),
                                        if (!controller
                                            .isCalculationMethodAuto
                                            .value) ...[
                                          8.verticalSpace,
                                          const SettingsDivider(),
                                          8.verticalSpace,
                                          ListView.separated(
                                            shrinkWrap: true,
                                            physics:
                                                const NeverScrollableScrollPhysics(),
                                            itemBuilder:
                                                (
                                                  context,
                                                  index,
                                                ) => CalculationMethodItemBuilder(
                                                  calculationMethodModel:
                                                      calculationMethods[index],
                                                ),
                                            separatorBuilder:
                                                (context, index) => Column(
                                                  children: [
                                                    8.verticalSpace,
                                                    const SettingsDivider(),
                                                    8.verticalSpace,
                                                  ],
                                                ),
                                            itemCount:
                                                calculationMethods.length,
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ),
                                (controller.isCalculationMethodAuto.value
                                        ? 0.7
                                        : 0.25)
                                    .sh
                                    .verticalSpace,
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
