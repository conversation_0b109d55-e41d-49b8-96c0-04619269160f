import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_date_correction_section.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsCalculationScreen extends StatelessWidget {
  const SettingsCalculationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Settings', subtitle: 'Calculation'),
                        32.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            children: [
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 24.w, vertical: 8.h),
                                decoration: BoxDecoration(
                                  color: AppColor.kRectangleColor,
                                ),
                                child: Column(
                                  children: [
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kTimer2,
                                      title: 'Calculation Methods',
                                      route: AppRouter
                                          .kSettingsCalculationMethodsScreen,
                                    ),
                                    const SettingsDivider(),
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kFqhi,
                                      title: 'Juristic',
                                      route: AppRouter
                                          .kSettingsCalculationJuristicScreen,
                                    ),
                                    const SettingsDivider(),
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kWebsite,
                                      title: 'Higher Latitudes',
                                      route: AppRouter
                                          .kSettingsCalculationHigherLatitudesScreen,
                                    ),
                                    const SettingsDivider(),
                                    SettingsItemBuilder(
                                      svg: AppSvgs.kFilter,
                                      title: 'Manual Correction',
                                      route: AppRouter
                                          .kSettingsManualCalculationScreen,
                                      whenComplete: () {
                                        Get.find<PrayerController>()
                                            .getAdjustments();
                                        Get.find<PrayerController>()
                                            .justCalculateTimes();
                                      },
                                    ),
                                    const SettingsDivider(),
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kSun,
                                      title: 'Daylight Saving',
                                      route: AppRouter
                                          .kSettingsDaylightSavingScreen,
                                    ),
                                  ],
                                ),
                              ),
                              20.verticalSpace,
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 24.w, vertical: 24.h),
                                decoration: BoxDecoration(
                                  color: AppColor.kRectangleColor,
                                ),
                                child: const Column(
                                  children: [
                                    // SettingsSwitchItemBuilder(
                                    //     svg: AppSvgs.kEye,
                                    //     title: 'Date Display'),
                                    // SettingsDivider(),
                                    // SettingsItemBuilder(
                                    //     svg: AppSvgs.kCalender,
                                    //     title: 'Islamic Calendar'),
                                    // SettingsDivider(verticalMargin: 16),
                                    SettingsDateCorrectionSection(),
                                  ],
                                ),
                              ),
                              0.15.sh.verticalSpace,
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
