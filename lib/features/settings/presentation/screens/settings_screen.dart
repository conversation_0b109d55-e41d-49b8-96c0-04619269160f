// ignore_for_file: deprecated_member_use

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/prayer/presentation/screens/prayer_screen.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_profile_button.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(60.r)),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 35.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomText(
                                'Settings',
                                style: TextStyle(
                                  fontSize: 21.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              InkWell(
                                onTap: () => Get.back(),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: SvgPicture.asset(AppSvgs.kClose),
                                ),
                              ),
                            ],
                          ),
                        ),
                        24.verticalSpace,
                        const SettingsProfileButton(),
                        32.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            children: [
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 24.w, vertical: 8.h),
                                color: AppColor.kRectangleColor,
                                child: Column(
                                  children: [
                                    SettingsItemBuilder(
                                      svg: AppSvgs.kNotification,
                                      title: 'Notifications',
                                      route:
                                          AppRouter.kSettingsNotificationScreen,
                                      whenComplete: () => SettingsController
                                          .instance
                                          .athkarNotificationSetup(),
                                    ),
                                    const SettingsDivider(),
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kLocation,
                                      title: 'Locations',
                                      route: AppRouter.kSettingsLocationScreen,
                                    ),
                                  ],
                                ),
                              ),
                              20.verticalSpace,
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 24.w, vertical: 8.h),
                                color: AppColor.kRectangleColor,
                                child: Column(
                                  children: [
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kSun,
                                      title: 'Appearance',
                                      route:
                                          AppRouter.kSettingsAppearanceScreen,
                                    ),
                                    const SettingsDivider(),
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kWebsite,
                                      title: 'Languages',
                                      route: AppRouter.kSettingsLanguageScreen,
                                    ),
                                    const SettingsDivider(),
                                    SettingsItemBuilder(
                                      svg: AppSvgs.kTimer,
                                      title: 'Calculation',
                                      route:
                                          AppRouter.kSettingsCalculationScreen,
                                      whenComplete: () =>
                                          Get.find<LayoutController>()
                                              .changeScreenLayout(
                                                  const PrayerScreen()),
                                    ),
                                  ],
                                ),
                              ),
                              20.verticalSpace,
                              SmoothEdgesContainer(
                                borderRadius: BorderRadius.circular(60.r),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 24.w, vertical: 8.h),
                                color: AppColor.kRectangleColor,
                                child: Column(
                                  children: [
                                    SettingsItemBuilder(
                                      svg: AppSvgs.kInformationCircle,
                                      title: 'About Us',
                                      onTap: () =>
                                          Get.toNamed(AppRouter.kAboutUsScreen),
                                    ),
                                    const SettingsDivider(),
                                    SettingsItemBuilder(
                                        svg: AppSvgs.kFavorite,
                                        title: 'Support',
                                        onTap: () => AppFunctions.contact()),
                                    const SettingsDivider(),
                                    SettingsItemBuilder(
                                      svg: AppSvgs.kStar,
                                      title: 'Rate us',
                                      onTap: () => AppFunctions.rateApp(),
                                    ),
                                    const SettingsDivider(),
                                    Builder(builder: (context) {
                                      return SettingsItemBuilder(
                                        svg: AppSvgs.kShare,
                                        title: 'Share App',
                                        svgColor: AppColor.kOrangeColor,
                                        onTap: () =>
                                            AppFunctions.shareApp(context),
                                      );
                                    }),
                                    const SettingsDivider(),
                                    SettingsItemBuilder(
                                      svg: AppSvgs.kUserDelete,
                                      title: 'Delete My Account',
                                      svgColor: AppColor.kOrangeColor,
                                      onTap: () => launchUrl(
                                        Uri.parse(
                                            "https://salawati.smart-fingers.com/account/delete"),
                                      ),
                                    ),
                                    if(kDebugMode)
                                    ...[const SettingsDivider(),
                                    const SettingsItemBuilder(
                                      svg: AppSvgs.kTimer,
                                      title: 'Developer Settings',
                                      route: AppRouter.kDeveloperSettingsScreen,
                                    )],
                                  ],
                                ),
                              ),
                              32.verticalSpace,
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
