import 'dart:async';

import 'package:adhan/adhan.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart'; // For better logging
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/settings/data/models/adhan_model.dart';
import 'package:salawati/features/settings/data/models/calculation_method_model.dart';
import 'package:salawati/features/settings/data/models/manual_correction_model.dart';

class SettingsController extends GetxController {
  static SettingsController get instance => Get.find();

  final PrayerController prayerController;

  final Logger _logger = Logger();
  final LocationService locationService = Get.find();
  final HomeWidgetController homeWidgetController = Get.find();

  SettingsController({required this.prayerController});

  // Reactive variables
  final locationTextEditingController = TextEditingController().obs;
  final isLocationAuto = true.obs;
  final isCalculationMethodAuto = true.obs;
  final isMathhabHanafi = false.obs;
  final highLatitudeRule = HighLatitudeRule.middle_of_the_night.obs;
  final isSummerTimeAuto = true.obs;
  final manualCorrectionList = <ManualCorrectionModel>[].obs;
  final RxMap<String, AdhanModel> athanNotificationsMap =
      <String, AdhanModel>{}.obs;
  final isLoading = false.obs;
  final isDateCorrectionOn = false.obs;
  final dateCorrectionValue = 0.obs;
  final isHadeethNotificationOn = false.obs;
  final isEventsNotificationOn = false.obs;
  final isHasbAlNafsNotificationOn = true.obs;
  final isMorningNotificationOn = true.obs;
  final isEveningAthkarNotificationOn = true.obs;
  final isSoundAthkarNotificationOn = false.obs;
  final currentCalculationMethodTitle = ''.obs;

  // Time properties for Athkar notifications
  String morningAthkarTime = '07:30';
  String eveningAthkarTime = '17:30';

  @override
  Future<void> onInit() async {
    super.onInit();
    _setupLocationListeners();
    _loadInitialData();
    currentCalculationMethodTitle.value =
        cacheMemory.read(CALCULATION_METHOD_TITLE) ?? '';
    // Load saved times
    morningAthkarTime = cacheMemory.read('MORNING_ATHKAR_TIME') ?? '07:30';
    eveningAthkarTime = cacheMemory.read('EVENING_ATHKAR_TIME') ?? '17:30';
  }

  void _setupLocationListeners() {
    // Monitor location service and permission status
    ever(locationService.serviceEnabled, (_) => _checkLocationStatus());
    ever(locationService.permissionStatus, (_) => _checkLocationStatus());
  }

  void _checkLocationStatus() {
    if (locationService.hasLocationAccess) {
      _handleLocationServicesEnabled();
    }
  }

  Future<void> _handleLocationServicesEnabled() async {
    if (isLocationAuto.value) {
      await LocationService.instance.toggleAutoLocation(true);
      await Get.find<HomeWidgetController>().onInit();
    }
  }

  void _loadInitialData() {
    isLocationAuto.value = cacheMemory.read(IS_LOCATION_AUTO) ?? false;
    debugPrint('Loaded isLocationAuto: ${isLocationAuto.value}');
    isCalculationMethodAuto.value =
        cacheMemory.read(IS_CALCULATION_METHOD_AUTO) ?? true;
    isMathhabHanafi.value = cacheMemory.read(IS_MATHHAB_HANAFI) ?? false;
    highLatitudeRule.value =
        HighLatitudeRule.values[cacheMemory.read(HIGH_LATITUDE) ?? 0];
    isSummerTimeAuto.value = cacheMemory.read(IS_SUMMER_TIME_AUTO) ?? true;
    isDateCorrectionOn.value = cacheMemory.read(DATE_CORRECTION_ON) ?? false;
    dateCorrectionValue.value = cacheMemory.read(DATE_CORRECTION_VALUE) ?? 0;
    isHadeethNotificationOn.value =
        cacheMemory.read(IS_HADEETH_NOTIFICATION_ON) ?? false;
    isEventsNotificationOn.value =
        cacheMemory.read(IS_EVENTS_NOTIFICATION_ON) ?? false;
    isHasbAlNafsNotificationOn.value =
        cacheMemory.read(IS_HASIB_ALNAFS_NOTIFICATION_ON) ?? true;
    isMorningNotificationOn.value =
        cacheMemory.read(IS_MORNING_ATHKAR_NOTIFICATION_ON) ?? true;
    isEveningAthkarNotificationOn.value =
        cacheMemory.read(IS_EVENING_ATHKAR_NOTIFICATION_ON) ?? true;
    isSoundAthkarNotificationOn.value =
        cacheMemory.read(IS_SOUND_ATHKAR_NOTIFICATION_ON) ?? false;

    _loadManualCorrections();
    _loadAthanNotifications();
  }

  Future<void> changeLocationAuto(bool value) async {
    if (isLoading.value) return;
    isLoading.value = true;
    final initialValue = isLocationAuto.value;

    try {
      if (value) {
        await _enableAutoLocation();
      } else {
        await _disableAutoLocation();
      }
    } catch (e) {
      Get.log("error in changeLocationAuto: $e");
      _handleLocationError(e, initialValue);
    } finally {
      isLoading.value = false;
    }
    update();
  }

  Future<void> _enableAutoLocation() async {
    final permissionResult = await locationService.handlePermissionFlow();

    if (permissionResult != LocationPermissionResult.granted) {
      await Geolocator.openLocationSettings();

      throw PermissionDeniedException('Location permission denied');
    }

    await cacheMemory.write(USER_SET_LOCATION, false);
    await cacheMemory.write(IS_LOCATION_AUTO, true);
    isLocationAuto.value = true;
    await _refreshLocationData();
    await homeWidgetController.onInit();
    update();
  }

  Future<void> _disableAutoLocation() async {
    isLocationAuto.value = false;
    await cacheMemory.write(IS_LOCATION_AUTO, false);
    await prayerController.fallbackToDefaultLocation();
    update();
  }

  void _handleLocationError(dynamic error, bool initialValue) {
    _logger.e('Location error: $error');
    isLocationAuto.value = initialValue;

    if (error is LocationServiceDisabledException) {
      Get.snackbar(
        'location_disabled'.tr,
        'enable_location_services'.tr,
        duration: const Duration(seconds: 3),
      );
    }

    update();
  }

  Future<void> _refreshLocationData() async {
    try {
      await LocationService.instance.updateCurrentLocation();
    } catch (e) {
      _logger.e('Location refresh failed: $e');
      await prayerController.fallbackToDefaultLocation();
    }
  }

  void _loadManualCorrections() {
    manualCorrectionList.value = [
      ManualCorrectionModel(
          title: FAJR, minutes: getManualCorrectionMinutes(FAJR)),
      ManualCorrectionModel(
          title: DHUHR, minutes: getManualCorrectionMinutes(DHUHR)),
      ManualCorrectionModel(
          title: ASR, minutes: getManualCorrectionMinutes(ASR)),
      ManualCorrectionModel(
          title: MAGHRIB, minutes: getManualCorrectionMinutes(MAGHRIB)),
      ManualCorrectionModel(
          title: ISHA, minutes: getManualCorrectionMinutes(ISHA)),
    ];
  }

  void _loadAthanNotifications() {
    athanNotificationsMap.value = {
      FAJR: getAdhanNotification(FAJR),
      SUNRISE: getAdhanNotification(SUNRISE),
      DHUHR: getAdhanNotification(DHUHR),
      ASR: getAdhanNotification(ASR),
      MAGHRIB: getAdhanNotification(MAGHRIB),
      ISHA: getAdhanNotification(ISHA),
      MIDDLEOFTHENIGHT: getAdhanNotification(MIDDLEOFTHENIGHT),
      LASTTHIRDOFTHENIGHT: getAdhanNotification(LASTTHIRDOFTHENIGHT),
      for (var prayer in prayers)
        prayer: getAdhanNotification(prayer)..soundIndex = _loadSound(prayer),
    };
  }

  bool get hasLocationText =>
      locationTextEditingController.value.text.isNotEmpty;

  @override
  void onClose() {
    locationTextEditingController.value.dispose();
    super.onClose();
  }

  Future<void> setAdhanSound(int index) async {
    await cacheMemory.write(ADHAN_SOUND, index);
    await prayerController.athanNotificationSetup();
    update();
  }

  Future<void> setAdhanSoundToDefaultNotification(bool value) async {
    await cacheMemory.write(DEFAULT_NOTIFICATION_SOUND, value);
    await prayerController.athanNotificationSetup();
    update();
  }

  int getAdhanSound() => cacheMemory.read(ADHAN_SOUND) ?? 0;

  bool isAdhanDefaultNotification() =>
      cacheMemory.read<bool>(DEFAULT_NOTIFICATION_SOUND) ?? false;

  // Future<void> setLocation(String city, String placeId) async {
  //   final dataState =
  //       await SettingsSearchRepo().getCityLocation(placeId: placeId);

  //   if (dataState is DataSuccess) {
  //     LocationModel locationModel = dataState.data!;

  //     // Add coordinate validation
  //     if (locationModel.lat == 0.0 || locationModel.lng == 0.0) {
  //       // _logger.e('Invalid coordinates from repo: ${locationModel.toJson()}');
  //       Get.snackbar('Error'.tr, 'invalid_location_data'.tr);
  //       return;
  //     }

  //     await cacheMemory.write(IS_LOCATION_AUTO, false);
  //     await prayerController.updateLocation(
  //         locationModel.lat, locationModel.lng);
  //   }
  // }
  Future<void> setLocalLocation(
    String city,
    String country,
    double latitude,
    double longitude,
  ) async {
    try {
      // Validate coordinates before saving
      if (!prayerController.isValidCoordinate(latitude, longitude)) {
        throw InvalidCoordinatesException(latitude, longitude);
      }

      await cacheMemory.write(USER_SET_LOCATION, true);
      await cacheMemory.write(IS_LOCATION_AUTO, false);

      // Store with validation
      await _storeManualLocation(city, country, latitude, longitude);

      await _updatePrayerControllerLocation(latitude, longitude);
      await homeWidgetController.onInit();
    } catch (e) {
      _logger.e('Manual location error: $e');
      Get.snackbar(
        'error'.tr,
        'invalid_location_data'.tr,
        duration: const Duration(seconds: 2),
      );
    }
  }

  Future<void> _storeManualLocation(
    String city,
    String country,
    double lat,
    double lng,
  ) async {
    await cacheMemory.write(CITY, city);
    await cacheMemory.write(COUNTRY, country);
    await cacheMemory.write(LATITUDE, lat);
    await cacheMemory.write(LONGITUDE, lng);
  }

  Future<void> _updatePrayerControllerLocation(
    double lat,
    double lng,
  ) async {
    prayerController.latitude = lat;
    prayerController.longitude = lng;
    prayerController.currentCity.value = cacheMemory.read(CITY) ?? 'Makkah'.tr;
    prayerController.currentCountry.value =
        cacheMemory.read(COUNTRY) ?? 'Saudi Arabia'.tr;

    prayerController.coordinates = Coordinates(lat, lng);
    await prayerController.calculatePrayerTimes();
    update();
  }

  void clearLocationText() {
    locationTextEditingController.value.clear();
    locationTextEditingController.refresh(); // Force UI update
  }

  Future<void> changeCalculationMethodAuto(bool value) async {
    try {
      isCalculationMethodAuto.value = value;

      if (value) {
        await cacheMemory.write(IS_CALCULATION_METHOD_AUTO, true);
        update();

        await prayerController.getCalculationMethodParams();
        await prayerController.justCalculateTimes();
      }
    } catch (e, stackTrace) {
      _logger.e('Error changing calculation method',
          error: e, stackTrace: stackTrace);
      // Optionally revert the value on error
      isCalculationMethodAuto.value = !value;
    }
    update();
  }

  Future<void> setCalculationMethod(
      CalculationMethodModel calculationMethodModel) async {
    await cacheMemory.write(IS_CALCULATION_METHOD_AUTO, false);
    await cacheMemory.write(
        CALCULATION_METHOD, calculationMethodModel.toJson());
    await cacheMemory.write(
        CALCULATION_METHOD_TITLE, calculationMethodModel.title);
    currentCalculationMethodTitle.value = calculationMethodModel.title;

    await prayerController.getCalculationMethodParams();
    await prayerController.justCalculateTimes();
  }

  void changeMathhabHanafi(bool value) {
    isMathhabHanafi.value = value;
    cacheMemory.write(IS_MATHHAB_HANAFI, value);

    prayerController.getMadhab();
    prayerController.justCalculateTimes();
    update();
  }

  void changeHighLatitudeRule(int index) {
    highLatitudeRule.value = HighLatitudeRule.values[index];
    cacheMemory.write(HIGH_LATITUDE, index);
    update();

    prayerController.getHighLatitudeRule();
    prayerController.justCalculateTimes();
    update();
  }

  Future<void> changeSummerTimeAuto(bool value) async {
    isSummerTimeAuto.value = value;
    if (value) {
      await cacheMemory.write(IS_SUMMER_TIME_AUTO, true);
      await cacheMemory.write(SUMMER_TIME_HOUR, 0);
      update();

      await prayerController.getAdjustments();
      await prayerController.justCalculateTimes();
    }
    update();
  }

  void setSummerTime(int hour) {
    cacheMemory.write(IS_SUMMER_TIME_AUTO, false);
    cacheMemory.write(SUMMER_TIME_HOUR, hour);
    update();

    prayerController.getAdjustments();
    prayerController.justCalculateTimes();
    update();
  }

  int getSummerTime() => cacheMemory.read(SUMMER_TIME_HOUR) ?? 0;

  static int getManualCorrectionMinutes(String title) =>
      cacheMemory.read(MANUAL_CORRECTION(title)) ?? 0;

  void changeManualCorrection(int index, int value) {
    manualCorrectionList[index].minutes += value;
    cacheMemory.write(
      MANUAL_CORRECTION(manualCorrectionList[index].title),
      manualCorrectionList[index].minutes,
    );
    update();
  }

  static AdhanModel getAdhanNotification(String title) =>
      AdhanModel.fromMap(title, cacheMemory.read(ADHAN_NOTIFCATIONS(title)));

  int _loadSound(String prayer) => cacheMemory.read<int>('sound_$prayer') ?? 0;

  Future<void> setNotification(AdhanModel model, String prayer) async {
    athanNotificationsMap[model.title] = model;
    await cacheMemory.write(ADHAN_NOTIFCATIONS(model.title), model.toMap());
    update();
    // write full model
    await cacheMemory.write('notif_$prayer', model.toJson());
    await cacheMemory.write('sound_$prayer', model.soundIndex);
    await prayerController.athanNotificationSetup();
    update(); // redraw any listeners
  }

  void toggleNotification(String prayer) {
    if (athanNotificationsMap[prayer] != null) {
      athanNotificationsMap[prayer]!.isNotified =
          !(athanNotificationsMap[prayer]!.isNotified);
      cacheMemory.write(
          ADHAN_NOTIFCATIONS(prayer), athanNotificationsMap[prayer]!.toMap());
      PrayerController.instance.athanNotificationSetup();
    }
    update();
  }

  Future<void> changeDateCorrection(bool value) async {
    isDateCorrectionOn.value = value;
    await cacheMemory.write(DATE_CORRECTION_ON, isDateCorrectionOn.value);
    if (!isDateCorrectionOn.value) {
      dateCorrectionValue.value = 0;
      await cacheMemory.write(DATE_CORRECTION_VALUE, 0);
    }
    update();
  }

  void changeDateCorrectionValue(int value) {
    dateCorrectionValue.value += value;
    cacheMemory.write(DATE_CORRECTION_VALUE, dateCorrectionValue.value);
    update();
  }

  void changeHadeethNotificationValue(bool value) {
    isHadeethNotificationOn.value = value;
    cacheMemory.write(
        IS_HADEETH_NOTIFICATION_ON, isHadeethNotificationOn.value);
    update();
  }

  void changeEventsNotificationValue(bool value) {
    isEventsNotificationOn.value = value;
    cacheMemory.write(IS_EVENTS_NOTIFICATION_ON, isEventsNotificationOn.value);
    update();
  }

  Future<void> changeHasbAlNafsNotificationValue(bool value) async {
    isHasbAlNafsNotificationOn.value = value;
    await cacheMemory.write(
        IS_HASIB_ALNAFS_NOTIFICATION_ON, isHasbAlNafsNotificationOn.value);
    if (isHasbAlNafsNotificationOn.value) {
      await _sendTestSoundNotification(
        'Hasib nafis'.tr,
        '${'You will be notified at'.tr} 10:00 pm'.tr,
        null,
      );
    }
    update();
  }

  Future<void> setMorningAthkarTime(String time) async {
    morningAthkarTime = time;
    await cacheMemory.write('MORNING_ATHKAR_TIME', time);
    if (isMorningNotificationOn.value) {
      await _sendTestSoundNotification(
        'Morning athkar'.tr,
        '${'You will be notified at'.tr} $morningAthkarTime'.tr,
        null,
      );
      await athkarNotificationSetup();
    }
    update();
  }

  Future<void> setEveningAthkarTime(String time) async {
    eveningAthkarTime = time;
    await cacheMemory.write('EVENING_ATHKAR_TIME', time);
    if (isEveningAthkarNotificationOn.value) {
      await _sendTestSoundNotification(
        'Evening athkar'.tr,
        '${'You will be notified at'.tr} $eveningAthkarTime'.tr,
        null,
      );
      await athkarNotificationSetup();
    }
    update();
  }

  Future<void> changeMorningAthkarNotificationValue(bool value) async {
    isMorningNotificationOn.value = value;
    await cacheMemory.write(
        IS_MORNING_ATHKAR_NOTIFICATION_ON, isMorningNotificationOn.value);
    if (value) {
      await _sendTestSoundNotification(
        'Morning athkar'.tr,
        '${'You will be notified at'.tr} $morningAthkarTime'.tr,
        null,
      );
      await athkarNotificationSetup(); // Re-schedule notifications
    } else {
      await AwesomeNotificationsService.cancelAthkarNotifications(
          AthkarType.morning);
    }
    update();
  }

  Future<void> changeEveningAthkarNotificationValue(bool value) async {
    isEveningAthkarNotificationOn.value = value;
    await cacheMemory.write(
        IS_EVENING_ATHKAR_NOTIFICATION_ON, isEveningAthkarNotificationOn.value);
    if (value) {
      await _sendTestSoundNotification(
        'Evening athkar'.tr,
        '${'You will be notified at'.tr} $eveningAthkarTime'.tr,
        null,
      );
      await athkarNotificationSetup(); // Re-schedule notifications
    } else {
      await AwesomeNotificationsService.cancelAthkarNotifications(
          AthkarType.evening);
    }
    update();
  }

  // Future<void> changeSoundAthkarNotificationValue(bool value) async {
  //   try {
  //     isSoundAthkarNotificationOn.value = value;
  //     await cacheMemory.write(
  //         IS_SOUND_ATHKAR_NOTIFICATION_ON, isSoundAthkarNotificationOn.value);
  //     update();
  //     if (value) {
  //       await _enableSoundAthkarNotifications();
  //     } else {
  //       await _disableSoundAthkarNotifications();
  //     }
  //     update();
  //   } catch (e) {
  //     debugPrint('Error: $e');
  //   }
  // }
  Future<void> changeSoundAthkarNotificationValue(bool value) async {
    try {
      isSoundAthkarNotificationOn.value = value;
      await cacheMemory.write(
          IS_SOUND_ATHKAR_NOTIFICATION_ON, isSoundAthkarNotificationOn.value);
      if (value) {
        await _enableSoundAthkarNotifications();
      } else {
        await AwesomeNotificationsService.cancelAthkarNotifications(
            AthkarType.dhikr);
      }
      update();
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  Future<void> _enableSoundAthkarNotifications() async {
    await soundsAthkarNotificationSetup();
    await _sendTestSoundNotification(
      'Audio Athkar'.tr,
      'Listen to blessed audio Athkar every hour to help you remember Allah.'
          .tr,
      AppSounds.soundbaqyat,
    );
  }

  // Future<void> _disableSoundAthkarNotifications() async {
  //   await AppNotifications.cancelOldNotificationsAndScheduleNewOnes(
  //     SOUND_NOTIFICATIONS_PAYLOAD,
  //     _scheduleSoundDhikrNotifications,
  //   );
  // }

  Future<void> _sendTestSoundNotification(
      String title, String subtitle, String? sound) async {
    try {
      // Send test notification using awesome_notifications
      await AwesomeNotificationsService.scheduleTestNotification(
        title: title,
        body: subtitle,
        scheduledTime: DateTime.now().add(const Duration(seconds: 1)),
      );
    } catch (e, stackTrace) {
      _logger.e('Test notification failed', error: e, stackTrace: stackTrace);
    }
  }

  // Future<void> _scheduleSoundDhikrNotifications() async {
  //   final notificationUtility = NotificationUtility();
  //   final notifications = notificationUtility.prepareDhikrNotifications();
  //   await notificationUtility.scheduleNotificationsBatch(
  //     notifications,
  //     SOUND_NOTIFICATIONS_PAYLOAD,
  //   );
  // }

  Future<void> athkarNotificationSetup() async {
    // Legacy athkar notification setup is now handled by awesome_notifications
    // This method is kept for compatibility but uses awesome_notifications internally
    _logger.d('Legacy athkar notification setup - using awesome_notifications');

    if (isMorningNotificationOn.value) {
      final List<String> timeParts = morningAthkarTime.split(':');
      final int hour = int.parse(timeParts[0]);
      final int minute = int.parse(timeParts[1]);

      // Schedule morning athkar using awesome_notifications
      final config = AthkarNotificationConfig(
        type: AthkarType.morning,
        isEnabled: true,
        time:
            '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}',
      );

      await AwesomeNotificationsService.scheduleMorningAthkar(
        config: config,
        daysAhead: 7,
        locale: Get.locale?.languageCode ?? 'ar',
      );
    }

    if (isEveningAthkarNotificationOn.value) {
      final List<String> timeParts = eveningAthkarTime.split(':');
      final int hour = int.parse(timeParts[0]);
      final int minute = int.parse(timeParts[1]);

      // Schedule evening athkar using awesome_notifications
      final config = AthkarNotificationConfig(
        type: AthkarType.evening,
        isEnabled: true,
        time:
            '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}',
      );

      await AwesomeNotificationsService.scheduleEveningAthkar(
        config: config,
        daysAhead: 7,
        locale: Get.locale?.languageCode ?? 'ar',
      );
    }
  }

  Future<void> soundsAthkarNotificationSetup() async {
    try {
      // Legacy sound athkar notification setup is now handled by awesome_notifications
      // This method is kept for compatibility but uses awesome_notifications internally
      _logger.d(
          'Legacy sound athkar notification setup - using awesome_notifications');

      // Schedule dhikr reminders using awesome_notifications
      final config = AthkarNotificationConfig(
        type: AthkarType.dhikr,
        isEnabled: true,
        time: '09:00', // Default start time for dhikr reminders
        intervalMinutes: 60, // Every hour
      );

      await AwesomeNotificationsService.scheduleDhikrReminders(
        config: config,
        daysAhead: 7,
        locale: Get.locale?.languageCode ?? 'ar',
      );

      update(); // Add UI refresh
    } catch (e, stackTrace) {
      _logger.e('Sound athkar setup failed', error: e, stackTrace: stackTrace);
    }
  }
}
