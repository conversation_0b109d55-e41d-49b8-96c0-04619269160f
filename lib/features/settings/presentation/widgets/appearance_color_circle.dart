import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_theme.dart';

// ignore: must_be_immutable
// class AppearanceColorCircle extends StatelessWidget {
//   AppearanceColorCircle({
//     super.key,
//     required this.colorIndex,
//   });
//   final int colorIndex;
//   bool isSelected = false;
//   @override
//   Widget build(BuildContext context) {
//          final currentThemeIndex = Get.find<AppTheme>().themeIndex.value;
//     final isSelected = currentThemeIndex == colorIndex;
//     return GetBuilder<AppTheme>(builder: (controller) {
//       return InkWell(
//         onTap: () => controller.setTheme(colorIndex),
//         child: Container(
//           height: 40.h,
//           width: 40.h,
//           decoration: BoxDecoration(
//             color: colors[colorIndex],
//             border: Border.all(width: 2, color: AppColor.kWhiteColor),
//             shape: BoxShape.circle,
//           ),
//           child: (cacheMemory.read(AppTheme.key) ?? 0) == colorIndex
//               ? const Icon(
//                   Icons.check,
//                   color: AppColor.kWhiteColor,
//                 )
//               : null,
//         ),
//       );
//     });
//   }
// }

class AppearanceColorCircle extends StatefulWidget {
  const AppearanceColorCircle({super.key, required this.colorIndex});

  final int colorIndex;

  @override
  State<AppearanceColorCircle> createState() => _AppearanceColorCircleState();
}

class _AppearanceColorCircleState extends State<AppearanceColorCircle> {
  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<AppTheme>(); // Get controller instance

    final currentThemeIndex = themeController.themeIndex.value;
    final isSelected = currentThemeIndex == widget.colorIndex;
    return InkWell(
      onTap: () {
        setState(() {
          themeController.setTheme(widget.colorIndex);
        });
      },
      child: Container(
        height: 40.h,
        width: 40.h,
        decoration: BoxDecoration(
          color: colors[widget.colorIndex],
          border: Border.all(width: 2, color: AppColor.kWhiteColor),
          shape: BoxShape.circle,
        ),
        child: isSelected
            ? const Icon(Icons.check, color: AppColor.kWhiteColor)
            : null,
      ),
    );
  }
}
