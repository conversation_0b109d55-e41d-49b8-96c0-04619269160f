import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';

class SettingsAppBar extends StatelessWidget {
  const SettingsAppBar({
    super.key,
    required this.title,
    required this.subtitle,
  });
  final String title;
  final String subtitle;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 35.w),
      child: Row(
        children: [
          InkWell(
            onTap: () => Get.back(),
            child: Row(
              children: [
                const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CustomHorizontalArrow(
                    isBack: true,
                    color: AppColor.kOrangeColor,
                  ),
                ),
                CustomText(
                  title,
                  style: TextStyle(
                    fontSize: 21.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          CustomText(
            subtitle,
            style: TextStyle(color: AppColor.kWhiteColor.withOpacity(0.6)),
          ),
        ],
      ),
    );
  }
}
