import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_color.dart';

class SettingsDivider extends StatelessWidget {
  final double verticalMargin;
  final Color? color;

  const SettingsDivider({
    super.key,
    this.verticalMargin = 0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: verticalMargin.h),
      color: color ?? AppColor.kWhiteColor.withOpacity(0.075),
      height: 1,
      width: double.infinity,
    );
  }
}
