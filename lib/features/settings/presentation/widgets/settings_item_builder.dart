import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';

class SettingsItemBuilder extends StatelessWidget {
  final String? svg;
  final String title;
  final String? route;
  final void Function()? onTap;
  final Color? svgColor;
  final void Function()? whenComplete;
  final dynamic args;
  const SettingsItemBuilder({
    super.key,
    this.svg,
    required this.title,
    this.route,
    this.whenComplete,
    this.svgColor,
    this.onTap,
    this.args,
  }) : assert(!(onTap != null && route != null),
            'Either onTap or route must be provided, not both.');

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (route != null) {
          Get.toNamed(route!, arguments: args)!
              .whenComplete(whenComplete ?? () {});
        } else {
          if (onTap != null) {
            onTap!();
          }
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Row(
          children: [
            if (svg != null)
              SvgPicture.asset(
                svg!,
                // ignore: deprecated_member_use
                color: svgColor,
              ),
            8.horizontalSpace,
            CustomText(title),
            const Spacer(),
            const CustomHorizontalArrow(),
          ],
        ),
      ),
    );
  }
}
