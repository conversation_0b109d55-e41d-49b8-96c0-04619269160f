import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/features/auth/data/models/get_auth_model.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_state.dart';

class SettingsProfileButton extends StatelessWidget {
  const SettingsProfileButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(builder: (context, state) {
      final bool isLoggedIn = cacheMemory.read(TOKEN) != null;
      GetAuthModel? profile;
      if (isLoggedIn) {
        profile = GetAuthModel.fromJson(cacheMemory.read(PROFILE));
      }
      return InkWell(
        onTap: () {
          if (isLoggedIn) {
            Get.toNamed(AppRouter.kProfileScreen);
          } else {
            Get.toNamed(AppRouter.kAuthScreen, arguments: true);
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 40.w),
          child: Row(
            children: [
              isLoggedIn
                  ? Container(
                      height: 50.h,
                      width: 50.h,
                      padding: EdgeInsets.all(1.w),
                      decoration: BoxDecoration(
                        color: AppColor.kRectangleColor,
                        shape: BoxShape.circle,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: CachedNetworkImage(
                          imageUrl: profile!.userImage,
                          placeholder: (context, url) =>
                              const CustomShimmer(radius: 100),
                          errorWidget: (context, url, error) =>
                              SvgPicture.asset(AppSvgs.kProfile),
                          fit: BoxFit.cover,
                        ),
                      ),
                    )
                  : SvgPicture.asset(AppSvgs.kProfile),
              16.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          profile?.userName ?? 'Sign In',
                          style: TextStyle(fontSize: 18.sp),
                        ),
                        const CustomHorizontalArrow(),
                      ],
                    ),
                    4.verticalSpace,
                    // FittedBox(
                    //   fit: BoxFit.scaleDown,
                    //   alignment: AlignmentDirectional.centerStart,
                    //   child: CustomText(
                    //     'Do and get even more with an account',
                    //     style: TextStyle(
                    //         fontSize: 12.sp,
                    //         color: AppColor.kWhiteColor.withOpacity(0.6)),
                    //   ),
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
