import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';

// ignore: must_be_immutable
class IsSelectedItemBuilder extends StatelessWidget {
  IsSelectedItemBuilder({
    super.key,
    required this.title,
    this.subTitle,
    this.isSelected = false,
  });
  final String title;
  final String? subTitle;

  bool isSelected;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 7,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                title,
                textAlign: TextAlign.start,
                style: TextStyle(
                    color: isSelected ? AppColor.kOrangeColor : null,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold),
              ),
              if (subTitle != null && subTitle != '')
                Padding(
                  padding: const EdgeInsets.only(top: 7),
                  child: CustomText(
                    subTitle!,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                        // color: isSelected ? AppColor.kOrangeColor : null,
                        fontSize: 10.sp),
                  ),
                ),
            ],
          ),
        ),
        if (isSelected)
          const Icon(Icons.check, color: AppColor.kOrangeColor)
        else
          const SizedBox(
            width: 30,
          ),
      ],
    );
  }
}
