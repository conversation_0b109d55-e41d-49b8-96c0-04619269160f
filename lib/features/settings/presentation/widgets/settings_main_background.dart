// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';

class SettingsMainBackground extends StatelessWidget {
  const SettingsMainBackground({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Get.back(),
      child: SizedBox(
          child: Image.asset(
        AppImages.kMainbg,
        height: 0.09.sh,
        width: double.infinity,
        fit: BoxFit.cover,
        alignment: Alignment.topCenter,
      )),
    );
  }
}
