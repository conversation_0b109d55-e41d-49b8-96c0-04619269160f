import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/data/models/adhan_model.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

// ignore: must_be_immutable
class IqamahDialog extends StatelessWidget {
  TextEditingController textEditingController = TextEditingController();

  IqamahDialog({
    super.key,
    required this.currentAdhan,
    required this.controller,
    required this.prayerKey,
  });

  final AdhanModel currentAdhan;
  final SettingsController controller;
  final String prayerKey;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).primaryColor,
      content: SingleChildScrollView(
        child: SmoothEdgesContainer(
          height: 200,
          borderRadius: BorderRadius.circular(60),
          padding: EdgeInsets.all(8.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Iqamah time in minutes :'),
              24.verticalSpace,
              CustomTextField(
                keyboardType: TextInputType.number,
                fillColor: AppColor.kRectangleColor,
                controller: textEditingController,
                hintText: '15 ${'Minutes'.tr}',
              ),
              16.verticalSpace,
              Align(
                alignment: AlignmentDirectional.centerEnd,
                child: IconButton(
                  onPressed: () {
                    if (textEditingController.text.isNotEmpty) {
                      try {
                        currentAdhan.iqamah =
                            double.parse(textEditingController.text);
                        controller.setNotification(currentAdhan, prayerKey);
                        Get.back();
                      } catch (e) {
                        AppFunctions.showErrorMessage(
                            'Please Enter a valid Number');
                      }
                    } else {
                      AppFunctions.showErrorMessage(
                          'Please Enter the Iqamah time');
                    }
                  },
                  icon: CustomText('ok'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
