import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';

class SettingsDateCorrectionSection extends StatelessWidget {
  const SettingsDateCorrectionSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingsController>(builder: (controller) {
      return Column(
        children: [
          SettingsSwitchItemBuilder(
            svg: AppSvgs.kCorrectCalender,
            title: 'Date Correction',
            switchValue: controller.isDateCorrectionOn.value,
            onChanged: (p0) => controller.changeDateCorrection(p0),
          ),
          16.verticalSpace,
          if (controller.isDateCorrectionOn.value) ...[
            Row(
              children: [
                Container(
                  alignment: AlignmentDirectional.centerStart,
                  height: 40.h,
                  child: AspectRatio(
                    aspectRatio: 77 / (33 / 2),
                    child: Row(
                      children: [
                        InkWell(
                          onTap: () {
                            controller.changeDateCorrectionValue(1);
                          },
                          child: Container(
                            height: double.infinity,
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                                color: AppColor.kRectangleColor,
                                borderRadius:
                                    const BorderRadiusDirectional.horizontal(
                                  start: Radius.circular(5),
                                )),
                            child: SvgPicture.asset(
                              AppSvgs.kPlus,
                            ),
                          ),
                        ),
                        2.horizontalSpace,
                        InkWell(
                          onTap: () {
                            controller.changeDateCorrectionValue(-1);
                          },
                          child: Container(
                            height: double.infinity,
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                                color: AppColor.kRectangleColor,
                                borderRadius:
                                    const BorderRadiusDirectional.horizontal(
                                  end: Radius.circular(5),
                                )),
                            child: SvgPicture.asset(AppSvgs.kMinus),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                CustomText('${controller.dateCorrectionValue} ${'DAYS'.tr}')
              ],
            ),
          ],
        ],
      );
    });
  }
}
