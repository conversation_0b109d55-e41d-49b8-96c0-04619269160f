import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

class ManualCorrectionWidget extends StatelessWidget {
  const ManualCorrectionWidget({
    super.key,
    required this.index,
  });

  final int index;

  @override
  Widget build(BuildContext context) {
    SettingsController controller = SettingsController.instance;
    return SmoothEdgesContainer(
      borderRadius: BorderRadius.circular(60.r),
      padding: EdgeInsets.all(24.w),
      color: AppColor.kRectangleColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
              '${controller.manualCorrectionList[index].title.tr} ${'prayer'.tr}'),
          Row(
            children: [
              GetBuilder<SettingsController>(builder: (_) {
                return CustomText(
                  '${controller.manualCorrectionList[index].minutes} ${'minutes'.tr}',
                  style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColor.kWhiteColor.withOpacity(0.6)),
                );
              }),
              const Spacer(),
              InkWell(
                onTap: () => controller.changeManualCorrection(index, 1),
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                      color: AppColor.kRectangleColor,
                      borderRadius: const BorderRadiusDirectional.horizontal(
                        start: Radius.circular(10),
                      )),
                  child: const Icon(
                    Icons.add,
                    color: AppColor.kWhiteColor,
                  ),
                ),
              ),
              2.horizontalSpace,
              InkWell(
                onTap: () => controller.changeManualCorrection(index, -1),
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                      color: AppColor.kRectangleColor,
                      borderRadius: const BorderRadiusDirectional.horizontal(
                        end: Radius.circular(10),
                      )),
                  child: const Icon(
                    Icons.remove,
                    color: AppColor.kWhiteColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
