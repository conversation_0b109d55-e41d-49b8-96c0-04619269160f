import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';

class SettingsSwitchItemBuilder extends StatelessWidget {
  final String title;
  final void Function(bool)? onChanged;
  final bool switchValue;
  final String? subtitle;
  final String? svg;
  final CrossAxisAlignment rowCrossAxisAlignment;
  final bool isEnabled;
  final VoidCallback? onPermissionRequested;

  const SettingsSwitchItemBuilder({
    super.key,
    required this.title,
    this.onChanged,
    this.switchValue = true,
    this.subtitle,
    this.svg,
    this.rowCrossAxisAlignment = CrossAxisAlignment.center,
    this.isEnabled = true,
    this.onPermissionRequested,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: rowCrossAxisAlignment,
      children: [
        if (svg != null) ...[
          SvgPicture.asset(svg!),
          8.horizontalSpace,
        ],
        Expanded(
          flex: 4,
          child: subtitle == null
              ? CustomText(title)
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(title),
                    4.verticalSpace,
                    CustomText(
                      subtitle!,
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: AppColor.kWhiteColor.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
        ),
        18.horizontalSpace,
        Expanded(
          child: Switch(
            value: switchValue,
            onChanged: (value) {
              if (isEnabled) {
                onChanged?.call(value);
              } else {
                onPermissionRequested?.call();
              }
            },
            activeTrackColor: isEnabled
                ? AppColor.kGreenColor
                : AppColor.kGreenColor.withOpacity(0.5),
            thumbColor: WidgetStateProperty.resolveWith<Color?>(
              (states) => isEnabled
                  ? AppColor.kWhiteColor
                  : AppColor.kWhiteColor.withOpacity(0.5),
            ),
          ),
        ),
      ],
    );
  }
}
