import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/settings/data/models/calculation_method_model.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

class CalculationMethodItemBuilder extends StatelessWidget {
  final CalculationMethodModel calculationMethodModel;

  const CalculationMethodItemBuilder({
    super.key,
    required this.calculationMethodModel,
  });

  @override
  Widget build(BuildContext context) {
    bool isSelected = cacheMemory.read(CALCULATION_METHOD_TITLE) ==
        calculationMethodModel.title;
    return InkWell(
      onTap: () async => await SettingsController.instance
          .setCalculationMethod(calculationMethodModel),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  calculationMethodModel.title,
                  style: TextStyle(
                      color: isSelected ? AppColor.kOrangeColor : null),
                ),
                8.verticalSpace,
                CustomText(
                  calculationMethodModel.subtitle,
                  style:
                      TextStyle(color: AppColor.kWhiteColor.withOpacity(0.6)),
                ),
              ],
            ),
          ),
          if (isSelected) const Icon(Icons.check, color: AppColor.kOrangeColor),
        ],
      ),
    );
  }
}
