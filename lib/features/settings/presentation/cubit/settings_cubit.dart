import 'package:get/get.dart';
import 'package:salawati/core/data/data_state.dart';
import 'package:salawati/features/settings/data/models/cities_model.dart';
import 'package:salawati/features/settings/data/repo/settings_repo.dart';

class SettingsSearchController extends GetxController {
  final SettingsSearchRepo _settingsSearchRepo = Get.find<SettingsSearchRepo>();

  // Reactive variables
  RxList<City> searchedCities = <City>[].obs;
  Rx<CitiesModel?> citiesModel = Rx<CitiesModel?>(null);
  Rx<LocalCitiesModel?> localCitiesModel = Rx<LocalCitiesModel?>(null);

  // Reactive variables for local cities search
  RxList<LocalCity> searchedLocalCities = <LocalCity>[].obs;
  RxBool isLoading = false.obs;
  RxString errorMessage = ''.obs;

  // Fetch cities by name
  Future<void> getCities({required String input}) async {
    isLoading.value = true;
    errorMessage.value = '';

    final dataState = await _settingsSearchRepo.getCities(input: input);

    if (dataState is DataSuccess) {
      citiesModel.value = dataState.data;
      searchedCities.assignAll(citiesModel.value!.cties);
    } else if (dataState is DataFailed) {
      errorMessage.value = dataState.error!.statusMessage.toString();
    }

    isLoading.value = false;
  }

  // Fetch local cities by latitude and longitude
  Future<void> getLocalCitiesByLatLong({
    required double latitude,
    required double longitude,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';

    final dataState = await _settingsSearchRepo.getLocalCitiesByLatLong(
      latitude: latitude,
      longitude: longitude,
    );

    if (dataState is DataSuccess) {
      searchedLocalCities.assignAll(dataState.data?.cities ?? []);
    } else if (dataState is DataFailed) {
      errorMessage.value = dataState.error!.statusMessage.toString();
    }

    isLoading.value = false;
  }

  // Fetch local cities by name
  Future<void> getLocalCitiesByName({required String cityName}) async {
    isLoading.value = true;
    errorMessage.value = '';

    final dataState =
        await _settingsSearchRepo.getLocalCitiesByName(cityName: cityName);

    if (dataState is DataSuccess) {
      searchedLocalCities.assignAll(dataState.data?.cities ?? []);
    } else if (dataState is DataFailed) {
      errorMessage.value = dataState.error!.statusMessage.toString();
    }

    isLoading.value = false;
  }

  // Clear search results
  void clearSearchResults() {
    searchedLocalCities.clear();
  }
}
