abstract class SettingsState {}

class SettingsInitial extends SettingsState {}

class CitiesLoading extends SettingsState {}

class CitiesSuccess extends SettingsState {}

class CitiesFailure extends SettingsState {
  final String error;

  CitiesFailure(this.error);
}

class LocationLoading extends SettingsState {}

class LocationSuccess extends SettingsState {}

class LocationFailure extends SettingsState {
  final String error;

  LocationFailure(this.error);
}

