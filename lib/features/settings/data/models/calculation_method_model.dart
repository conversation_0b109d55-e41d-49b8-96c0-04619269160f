import 'package:adhan/adhan.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

class CalculationMethodModel {
  final String title;
  final String subtitle;
  final CalculationMethod calculationMethod;
  final double latitude; // Add latitude field
  final double longitude; // Add longitude field

  CalculationMethodModel({
    required this.title,
    required this.subtitle,
    required this.calculationMethod,
    required this.latitude, // Add latitude parameter
    required this.longitude, // Add longitude parameter
  });

  factory CalculationMethodModel.fromJson(Map<String, dynamic> json) {
    return CalculationMethodModel(
      title: json['title'],
      subtitle: json['subtitle'],
      calculationMethod: CalculationMethod.values
          .firstWhere((e) => e.toString() == json['calculationMethod']),
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subtitle': subtitle,
      'calculationMethod': calculationMethod.toString(),
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  static Future<CalculationMethodModel?> findNearestLocation(
      double currentLat, double currentLng) async {
    double? smallestDistance;
    CalculationMethodModel? nearestLocation;

    for (var method in calculationMethods) {
      double distanceInMeters = Geolocator.distanceBetween(
          currentLat, currentLng, method.latitude, method.longitude);

      if (smallestDistance == null || distanceInMeters < smallestDistance) {
        smallestDistance = distanceInMeters;
        nearestLocation = method;
      }
    }

    return nearestLocation;
  }
}

List<CalculationMethodModel> calculationMethods = [
  CalculationMethodModel(
    title: 'Umm al-Qura University, Makkah',
    subtitle: '${'Fajr Angle'.tr} 18.5°',
    calculationMethod: CalculationMethod.umm_al_qura,
    latitude: 21.3890824, // Manually added latitude
    longitude: 39.8579118, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Muslim World League',
    subtitle: '${'Fajr Angle'.tr} 18°',
    calculationMethod: CalculationMethod.muslim_world_league,
    latitude: 51.5194682, // Manually added latitude
    longitude: -0.1360365, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Egyptian General Authority of Survey',
    subtitle: '${'Fajr Angle'.tr} 19.5°',
    calculationMethod: CalculationMethod.egyptian,
    latitude: 30.0444196, // Manually added latitude
    longitude: 31.2357116, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'University of Islamic Sciences, Karachi',
    subtitle: '${'Fajr Angle'.tr} 18°',
    calculationMethod: CalculationMethod.karachi,
    latitude: 24.8614622, // Manually added latitude
    longitude: 67.0099388, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'The Gulf Region',
    subtitle: '${'Fajr Angle'.tr} 18.2°',
    calculationMethod: CalculationMethod.dubai,
    latitude: 24.1323638, // Manually added latitude
    longitude: 53.3199527, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Moon Sighting Committee',
    subtitle: '${'Fajr Angle'.tr} 18°',
    calculationMethod: CalculationMethod.moon_sighting_committee,
    latitude: 34.6415764, // Manually added latitude
    longitude: 50.8746035, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Referred to as the ISNA method',
    subtitle: '${'Fajr Angle'.tr} 15°',
    calculationMethod: CalculationMethod.north_america,
    latitude: 39.7042123, // Manually added latitude
    longitude: -86.3994387, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Kuwait',
    subtitle: '${'Fajr Angle'.tr} 18°',
    calculationMethod: CalculationMethod.kuwait,
    latitude: 29.375859, // Manually added latitude
    longitude: 47.9774052, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Qatar',
    subtitle: '${'Fajr Angle'.tr} 18°',
    calculationMethod: CalculationMethod.qatar,
    latitude: 25.2854473, // Manually added latitude
    longitude: 51.5310398, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Singapore',
    subtitle: '${'Fajr Angle'.tr} 20°',
    calculationMethod: CalculationMethod.singapore,
    latitude: 1.352083, // Manually added latitude
    longitude: 103.819836, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Institute of Geophysics, University of Tehran',
    subtitle: '${'Fajr Angle'.tr} 17.7°',
    calculationMethod: CalculationMethod.tehran,
    latitude: 35.6891975, // Manually added latitude
    longitude: 51.3889736, // Manually added longitude
  ),
  CalculationMethodModel(
    title: 'Presidency of Religious Affairs, Türkiye',
    subtitle: '${'Fajr Angle'.tr} 18°',
    calculationMethod: CalculationMethod.turkey,
    latitude: 37.05,
    longitude: 29.7,
  ),
  CalculationMethodModel(
    title: 'The default Calculation Method',
    subtitle: '${'Fajr Angle'.tr} 0°',
    calculationMethod: CalculationMethod.other,
    latitude: 0, // Manually added latitude
    longitude: 0, // Manually added longitude
  ),
];
