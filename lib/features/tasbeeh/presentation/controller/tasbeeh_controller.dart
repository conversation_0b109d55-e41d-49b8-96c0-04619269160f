import 'package:get/get.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/features/tasbeeh/data/models/tasbeeh_model.dart';
import 'package:salawati/features/tasbeeh/presentation/screens/add_edit_tasbeeh_controllers.dart';

class TasabeehController extends GetxController {
  static RxList<TasbeehModel> tasbeehat = [
    TasbeehModel(
      id: 1,
      title: 'Subhanallah',
    ),
    TasbeehModel(
      id: 2,
      title: 'Alham<PERSON><PERSON><PERSON>',
    ),
    TasbeehModel(
      id: 3,
      title: '<PERSON><PERSON>',
    ),
    TasbeehModel(
      id: 4,
      title: '<PERSON> ilaha illa <PERSON>',
      totalCount: 100,
    ),
    TasbeehModel(
      id: 5,
      title: 'Astagh<PERSON>rullah',
      totalCount: 100,
    ),
  ].obs;

  @override
  onInit() {
    super.onInit();
    List<TasbeehModel> tasbeehList = getTasbeehList();
    for (TasbeehModel tasbeehModel in tasbeehList) {
      tasbeehModel.canBeDeleted = true;
      tasbeehat.add(tasbeehModel);
    }
  }

  static RxInt selectedTasbeehIndex = (0).obs;

  void changeSelectedTasbeehIndex(int index) {
    selectedTasbeehIndex.value = index;
    update();
  }

  void increaseTasbeehCounter() {
    if (selectedTasbeehIndex.value != -1) {
      if (tasbeehat[selectedTasbeehIndex.value].count <
          tasbeehat[selectedTasbeehIndex.value].totalCount) {
        tasbeehat[selectedTasbeehIndex.value].count++;
        update();
        if (tasbeehat[selectedTasbeehIndex.value].count ==
            tasbeehat[selectedTasbeehIndex.value].totalCount) {
          AppFunctions.vibrate(false);
          _moveToNextTasbeeh();
        } else if (tasbeehat[selectedTasbeehIndex.value].count <
            tasbeehat[selectedTasbeehIndex.value].totalCount) {
          AppFunctions.vibrate(true);
        }
      }
    } else {
      AppFunctions.showErrorMessage('Please select tasbeeh first');
    }
  }

  void _moveToNextTasbeeh() {
    if (selectedTasbeehIndex.value != -1 && tasbeehat.length > 1) {
      int nextIndex = (selectedTasbeehIndex.value + 1) % tasbeehat.length;
      changeSelectedTasbeehIndex(nextIndex);
    }
  }

  void resetTasbeehCounter() {
    if (selectedTasbeehIndex.value != -1) {
      tasbeehat[selectedTasbeehIndex.value].count = 0;
      update();
    }
  }

  void addNewTasbeeh(TasbeehModel tasbeehModel) {
    List<TasbeehModel> tasbeehList = getTasbeehList();
    tasbeehList.add(tasbeehModel);
    tasbeehat.add(tasbeehModel);
    cacheMemory.write(
        TASBEEH_LIST, tasbeehList.map((e) => e.toJson()).toList());
    Get.back();
    AppFunctions.showSuccessMessage('Successfully added'.tr);
    clearTasbeehControllers();
  }

  List<TasbeehModel> getTasbeehList() {
    Object? data = cacheMemory.read(TASBEEH_LIST);
    List<TasbeehModel> tasbeehList = data == null
        ? []
        : (data as List)
            .map((e) => TasbeehModel.fromJson(e as Map<String, dynamic>))
            .toList();
    return tasbeehList;
  }

  void addTasbeehHandleControllers() {
    if (tasbeehNameController.text.isEmpty) {
      AppFunctions.showErrorMessage('Tasbeeh name is required'.tr);
    } else if (tasbeehCountController.text.isEmpty) {
      AppFunctions.showErrorMessage('Tasbeeh Count is required'.tr);
    } else {
      int id = lastInsertedId() + 1;
      addNewTasbeeh(TasbeehModel(
        id: id,
        count: 0,
        title: tasbeehNameController.text,
        totalCount: int.parse(tasbeehCountController.text),
        canBeDeleted: true,
      ));
    }
  }

  int lastInsertedId() {
    int max = tasbeehat.first.id;
    for (var element in tasbeehat) {
      if (element.id > max) {
        max = element.id;
      }
    }
    return max;
  }

  void deleteTasbeeh(int index) {
    selectedTasbeehIndex.value = 0;
    var id = tasbeehat[index].id;
    var tasbeehatList = getTasbeehList();
    tasbeehatList.removeWhere((element) => element.id == id);
    tasbeehat.removeWhere((element) => element.id == id);
    cacheMemory.write(
        TASBEEH_LIST, tasbeehatList.map((e) => e.toJson()).toList());
    update();
    AppFunctions.showSuccessMessage('Successfully deleted'.tr);
  }
}
