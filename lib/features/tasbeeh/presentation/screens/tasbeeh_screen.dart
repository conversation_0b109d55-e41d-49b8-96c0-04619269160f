import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/tasbeeh/presentation/controller/tasbeeh_controller.dart';
import 'package:salawati/features/tasbeeh/presentation/widgets/tasbeeh_drop_down.dart';

// ignore: must_be_immutable
class TasbeehScreen extends StatelessWidget {
  TasbeehScreen({super.key});

  TasabeehController controller = Get.put(TasabeehController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          InkWell(
            onTap: () =>
                Get.find<TasabeehController>().increaseTasbeehCounter(),
            overlayColor: WidgetStateColor.resolveWith(
              (states) => Colors.transparent,
            ),
            child: Align(
              alignment: Alignment.topCenter,
              child: SingleChildScrollView(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.1.sh),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SettingsShareRow(),
                      10.verticalSpace,
                      InkWell(
                        borderRadius: BorderRadius.circular(100),
                        onTap: () {
                          Get.toNamed(AppRouter.kAddTasbeehScreen);
                        },
                        child: Container(
                          margin: const EdgeInsets.only(
                              left: 16, right: 16, top: 16, bottom: 4),
                          child: CustomText(
                            'Add new',
                            style: const TextStyle(
                              height: 0,
                              color: AppColor.kOrangeColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      20.verticalSpace,
                      const Row(
                        children: [
                          Expanded(child: TasbeehDropDown()),
                        ],
                      ),
                      Padding(
                        padding: EdgeInsets.all(16.w),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            AspectRatio(
                              aspectRatio: 1,
                              child: SizedBox(
                                width: double.infinity,
                                child: CircularProgressIndicator(
                                  strokeCap: StrokeCap.round,
                                  strokeWidth: 10.w,
                                  value: 1,
                                  color:
                                      const Color(0xfff2f2f2).withOpacity(0.2),
                                ),
                              ),
                            ),
                            Transform.rotate(
                              angle: math.pi,
                              child: AspectRatio(
                                aspectRatio: 1,
                                child: SizedBox(
                                  width: double.infinity,
                                  child: GetBuilder<TasabeehController>(
                                    builder: (controller) {
                                      return CircularProgressIndicator(
                                        strokeCap: StrokeCap.round,
                                        strokeWidth: 10.w,
                                        value: TasabeehController
                                                    .selectedTasbeehIndex
                                                    .value ==
                                                -1
                                            ? 0
                                            : TasabeehController
                                                    .tasbeehat[TasabeehController
                                                        .selectedTasbeehIndex
                                                        .value]
                                                    .count /
                                                TasabeehController
                                                    .tasbeehat[TasabeehController
                                                        .selectedTasbeehIndex
                                                        .value]
                                                    .totalCount,
                                        color: AppColor.kOrangeColor,
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                            GetBuilder<TasabeehController>(
                              builder: (_) {
                                return Container(
                                  height: 100.w,
                                  width: 100.w,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: TasabeehController
                                                    .selectedTasbeehIndex
                                                    .value !=
                                                -1 &&
                                            TasabeehController
                                                    .tasbeehat[TasabeehController
                                                        .selectedTasbeehIndex
                                                        .value]
                                                    .count ==
                                                TasabeehController
                                                    .tasbeehat[TasabeehController
                                                        .selectedTasbeehIndex
                                                        .value]
                                                    .totalCount
                                        ? AppColor.kOrangeColor
                                        : AppColor.kRectangleColor,
                                  ),
                                  child: GetBuilder<TasabeehController>(
                                    builder: (controller) {
                                      if (TasabeehController
                                                  .selectedTasbeehIndex.value !=
                                              -1 &&
                                          TasabeehController
                                                  .tasbeehat[TasabeehController
                                                      .selectedTasbeehIndex
                                                      .value]
                                                  .count ==
                                              TasabeehController
                                                  .tasbeehat[TasabeehController
                                                      .selectedTasbeehIndex
                                                      .value]
                                                  .totalCount) {
                                        return Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 35.w,
                                        );
                                      }
                                      return CustomText(
                                        TasabeehController.selectedTasbeehIndex
                                                    .value ==
                                                -1
                                            ? '0'
                                            : '${TasabeehController.tasbeehat[TasabeehController.selectedTasbeehIndex.value].count} / ${TasabeehController.tasbeehat[TasabeehController.selectedTasbeehIndex.value].totalCount}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 25.sp,
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      8.verticalSpace,
                      Center(
                        child: IntrinsicWidth(
                          child: InkWell(
                            onTap: () => Get.find<TasabeehController>()
                                .resetTasbeehCounter(),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                  child: SvgPicture.asset(AppSvgs.kRefresh)),
                            ),
                          ),
                        ),
                      ),
                      0.15.sh.verticalSpace,
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
