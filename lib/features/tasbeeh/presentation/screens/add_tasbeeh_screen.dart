import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_button.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/tasbeeh/presentation/controller/tasbeeh_controller.dart';
import 'package:salawati/features/tasbeeh/presentation/screens/add_edit_tasbeeh_controllers.dart';

class AddTasbeehScreen extends StatelessWidget {
  const AddTasbeehScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Container(color: AppColor.kWhiteColor.withOpacity(0.035)),
          Container(
            padding: EdgeInsets.only(top: 0.15.sh),
            child: SmoothEdgesContainer(
              borderRadius: BorderRadius.circular(60.r),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 20,
                    spreadRadius: 20,
                    color: AppColor.kWhiteColor.withOpacity(0.045),
                  ),
                ],
              ),
              child: Align(
                alignment: Alignment.topCenter,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(28.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              'Add New Tasbeeh',
                              style: TextStyle(
                                fontSize: 21.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            InkWell(
                                onTap: () => Get.back(),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: SvgPicture.asset(AppSvgs.kClose),
                                )),
                          ],
                        ),
                        0.08.sh.verticalSpace,
                        CustomText('Tasbeeh Name'),
                        8.verticalSpace,
                        Container(
                          decoration: BoxDecoration(
                            color: AppColor.kRectangleColor,
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          child: CustomTextField(
                            hintText: 'TasbeehHintText'.tr,
                            controller: tasbeehNameController,
                            keyboardType: TextInputType.text,
                          ),
                        ),
                        32.verticalSpace,
                        CustomText('Tasbeeh Count'),
                        8.verticalSpace,
                        Container(
                          decoration: BoxDecoration(
                            color: AppColor.kRectangleColor,
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          child: CustomTextField(
                            hintText: 'How many times ?'.tr,
                            controller: tasbeehCountController,
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        24.verticalSpace,
                        CustomButton(
                          onTap: () {
                            Get.find<TasabeehController>()
                                .addTasbeehHandleControllers();
                          },
                          label: 'Add',
                          color: AppColor.kOrangeColor,
                        ),
                        0.1.sh.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
