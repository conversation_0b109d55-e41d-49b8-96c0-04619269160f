class TasbeehModel {
  int id;
  String title;
  int count;
  int totalCount;
  bool canBeDeleted;
  TasbeehModel({
    required this.id,
    required this.title,
    this.count = 0,
    this.totalCount = 33,
    this.canBeDeleted = false,
  });
  factory TasbeehModel.fromJson(Map<String, dynamic> json) {
    return TasbeehModel(
      id: json['id'],
      title: json['title'],
      count: json['count'],
      totalCount: json['totalCount'],
      canBeDeleted: json['canBeDeleted'] ?? false,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'count': count,
      'totalCount': totalCount,
      'canBeDeleted': canBeDeleted
    };
  }
}
