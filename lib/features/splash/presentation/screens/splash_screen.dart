// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/splash/presentation/controller/splash_controller.dart';

class SplashScreen extends GetView<SplashController> {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // SplashController splashController = Get.put(SplashController());

    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(AppImages.kBg),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(AppSvgs.kLogo),
              Padding(
                padding:
                    EdgeInsets.symmetric(vertical: 24.h, horizontal: 0.15.sw),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  alignment: Alignment.center,
                  child: CustomText(
                    'ALL IN ONE MUSLIM APP',
                    style: TextStyle(
                      fontSize: 18.sp,
                    ),
                  ),
                ),
              ),
              Padding(
                padding:
                    EdgeInsets.symmetric(vertical: 8.h, horizontal: 0.22.sw),
                child: LayoutBuilder(builder: (context, constraints) {
                  return Stack(
                    children: [
                      Container(
                        height: 5.h,
                        width: constraints.maxWidth,
                        decoration: BoxDecoration(
                          color: Color.fromARGB(
                              (255 * 0.07).toInt(), 242, 242, 242),
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                      ),
                      Obx(() {
                        return Container(
                          height: 5.h,
                          width: controller.sliderWidth.value,
                          decoration: BoxDecoration(
                            color: AppColor.kGreyColor,
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                        );
                      }),
                    ],
                  );
                }),
              ),
            ],
          ),
          SvgPicture.asset(
            height: double.infinity,
            width: double.infinity,
            AppSvgs.kSplashShadow,
            fit: BoxFit.fill,
            color: Colors.black.withOpacity(0.5),
          ),
        ],
      ),
    );
  }
}
