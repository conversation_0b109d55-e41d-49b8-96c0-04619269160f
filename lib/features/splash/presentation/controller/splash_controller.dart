import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:salawati/core/data/data_service.dart';
import 'package:salawati/core/services/permission_manager.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/bloc_observer.dart';

class SplashController extends GetxController {
  static const int splashDuration = 4;

  RxDouble sliderWidth = 0.0.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await DataService.init();

    Bloc.observer = SimpleBlocObserver();

    updatesliderWidth(150);
  }

  void updatesliderWidth(double value) {
    //todo amin check if 1 mili is ok ! becouse it was 30
    Timer.periodic(const Duration(milliseconds: 1), (timer) {
      if (sliderWidth.value <= value) {
        sliderWidth.value += 1;
      } else {
        timer.cancel();
        _navigateToNextScreen();
      }
    });
  }

  Future<void> _navigateToNextScreen() async {
    try {
      final permissionManager = Get.find<PermissionManager>();
      final shouldShowPermissions = await permissionManager.shouldShowPermissionFlow();

      if (shouldShowPermissions) {
        Get.offNamed(AppRouter.kPermissionFlowScreen);
      } else {
        Get.offNamed(AppRouter.kLayoutScreen);
      }
    } catch (e) {
      // If there's an error with permission manager, just go to layout screen
      Get.offNamed(AppRouter.kLayoutScreen);
    }
  }
}
