import 'package:flutter/material.dart';

class NavbarPainter extends CustomPainter {
  final Color color;

  NavbarPainter({required this.color});
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint();
    Path path = Path();

    paint.color = color;
    path = Path();
    path.lineTo(size.width * 0.42, size.height * 0.26);
    path.cubicTo(size.width * 0.39, size.height * 0.11, size.width * 0.38,
        size.height * 0.03, size.width * 0.37, size.height * 0.02);
    path.cubicTo(size.width * 0.36, 0, size.width * 0.35, 0, size.width / 3, 0);
    path.cubicTo(size.width / 3, 0, size.width * 0.15, 0, size.width * 0.15, 0);
    path.cubicTo(size.width * 0.08, 0, size.width * 0.04, 0, size.width * 0.02,
        size.height * 0.1);
    path.cubicTo(
        0, size.height / 5, 0, size.height * 0.35, 0, size.height * 0.67);
    path.cubicTo(
        0, size.height * 0.67, 0, size.height * 0.69, 0, size.height * 0.69);
    path.cubicTo(0, size.height * 0.84, 0, size.height * 0.91,
        size.width * 0.01, size.height * 0.95);
    path.cubicTo(size.width * 0.02, size.height, size.width * 0.04, size.height,
        size.width * 0.07, size.height);
    path.cubicTo(size.width * 0.07, size.height, size.width * 0.93, size.height,
        size.width * 0.93, size.height);
    path.cubicTo(size.width * 0.96, size.height, size.width * 0.98, size.height,
        size.width, size.height * 0.95);
    path.cubicTo(size.width, size.height * 0.91, size.width, size.height * 0.84,
        size.width, size.height * 0.69);
    path.cubicTo(size.width, size.height * 0.69, size.width, size.height * 0.67,
        size.width, size.height * 0.67);
    path.cubicTo(size.width, size.height * 0.35, size.width, size.height / 5,
        size.width * 0.98, size.height * 0.1);
    path.cubicTo(
        size.width * 0.96, 0, size.width * 0.92, 0, size.width * 0.85, 0);
    path.cubicTo(
        size.width * 0.85, 0, size.width * 0.67, 0, size.width * 0.67, 0);
    path.cubicTo(size.width * 0.65, 0, size.width * 0.64, 0, size.width * 0.63,
        size.height * 0.02);
    path.cubicTo(size.width * 0.62, size.height * 0.03, size.width * 0.61,
        size.height * 0.11, size.width * 0.58, size.height * 0.26);
    path.cubicTo(size.width * 0.56, size.height * 0.37, size.width * 0.53,
        size.height * 0.44, size.width / 2, size.height * 0.44);
    path.cubicTo(size.width * 0.47, size.height * 0.44, size.width * 0.44,
        size.height * 0.37, size.width * 0.42, size.height * 0.26);
    path.cubicTo(size.width * 0.42, size.height * 0.26, size.width * 0.42,
        size.height * 0.26, size.width * 0.42, size.height * 0.26);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
