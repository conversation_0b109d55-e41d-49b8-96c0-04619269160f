import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/layout/presentation/widgets/navbar_painter.dart';

class LayoutNavigationBar extends StatelessWidget {
  const LayoutNavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LayoutController>(
      builder: (controller) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(kRadius),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: AppColor.kWhiteColor.withOpacity(0.1),
                      spreadRadius: 10,
                      blurRadius: 50,
                    ),
                  ],
                ),
                width: double.infinity,
                child: CustomPaint(
                  painter: Navbar<PERSON><PERSON><PERSON>(color: Theme.of(context).primaryColor),
                  size: const Size.fromWidth(double.maxFinite),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          0.05.sw.horizontalSpace,
                          for (int i = 0; i < 4; i++) ...[
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  controller.changeScreenLayout(
                                      controller.screens[i].screen,
                                      index: i);
                                },
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    AnimatedCrossFade(
                                      duration:
                                          const Duration(milliseconds: 350),
                                      firstChild: SvgPicture.asset(
                                        controller.screens[i].svg,
                                        key: ValueKey<int>(i),
                                        colorFilter: const ColorFilter.mode(
                                            AppColor.kOrangeColor,
                                            BlendMode.srcIn),
                                        height: 18.h,
                                      ),
                                      secondChild: SvgPicture.asset(
                                        controller.screens[i].svg,
                                        key: ValueKey<int>(i),
                                        height: 18.h,
                                      ),
                                      crossFadeState: controller.currIndex == i
                                          ? CrossFadeState.showFirst
                                          : CrossFadeState.showSecond,
                                    ),
                                    4.verticalSpace,
                                    AnimatedCrossFade(
                                      duration:
                                          const Duration(milliseconds: 350),
                                      secondChild: CustomText(
                                        controller.screens[i].label
                                            .split(' ')
                                            .first,
                                        style: const TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      firstChild: CustomText(
                                        controller.screens[i].label
                                            .split(' ')
                                            .first,
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: AppColor.kOrangeColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      crossFadeState: controller.currIndex == i
                                          ? CrossFadeState.showFirst
                                          : CrossFadeState.showSecond,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            if (i == 1) ...[
                              //floating Button Space
                              (LayoutController.floatingButtonRaduis * 2)
                                  .horizontalSpace,
                            ],
                          ],
                          0.05.sw.horizontalSpace,
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
