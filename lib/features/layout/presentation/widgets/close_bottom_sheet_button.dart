import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';

class CloseBottomSheetButton extends StatelessWidget {
  const CloseBottomSheetButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: AppColor.kOrangeColor.withOpacity(0.3),
            spreadRadius: 5,
            blurRadius: 15,
          ),
        ],
        color: AppColor.kOrangeColor,
        shape: BoxShape.circle,
      ),
      height: LayoutController.floatingButtonRaduis * 2,
      width: LayoutController.floatingButtonRaduis * 2,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(100),
          onTap: () => Get.back(),
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: SvgPicture.asset(AppSvgs.kX),
          ),
        ),
      ),
    );
  }
}
