import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/layout/data/layout_screens_model.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/layout/presentation/widgets/close_bottom_sheet_button.dart';
import 'package:salawati/features/layout/presentation/widgets/layout_navigation_bar.dart';
import 'package:salawati/features/quran/data/models/surah_model.dart';
import 'package:salawati/features/quran/presentation/controller/quran_controller.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_state.dart';

class LayoutScreen extends StatelessWidget {
  const LayoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Future<bool> handleWillPopScopeRoot() async {
      /// Check pop root navigator
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
        return false;
      }

      // amin fix it to be like the old one.
      final isAllowExit = await Get.dialog(AlertDialog(
        contentPadding: EdgeInsets.all(16.w),
        backgroundColor: Theme.of(context).primaryColor,
        content: CustomText("Are you sure to close the app?"),
        actions: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CustomText(
                    'cancel',
                  ),
                ),
              ),
              8.horizontalSpace,
              InkWell(
                onTap: () => Navigator.of(context).pop(true),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CustomText(
                    'ok',
                  ),
                ),
              ),
            ],
          )
        ],
      ));

      if (isAllowExit) {
        /// Because Flutter does not exit app, just make the app goes to background
        Future.delayed(const Duration(milliseconds: 250), () {
          exit(0);
        });
      }

      /// It make the app goes to the app with the animation, not exit immediately
      /// After that, I will exit the app on background
      return Future.delayed(const Duration(milliseconds: 100), () {
        return isAllowExit;
      });
    }

    // ignore: deprecated_member_use
    return GetBuilder<LayoutController>(
      builder: (controller) {
        return WillPopScopeWidget(
          onWillPop: handleWillPopScopeRoot,
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            body: SafeArea(
              bottom: false,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 350),
                    transitionBuilder: (child, animation) {
                      return FadeTransition(
                        opacity: animation,
                        child: child,
                      );
                    },
                    child: controller.currScreen,
                  ),
                  const LayoutNavigationBar(),
                  Positioned(
                    bottom: (LayoutController.floatingButtonRaduis * 1.5) +
                        MediaQuery.of(context).padding.bottom,
                    child: InkWell(
                      onTap: () {
                        final List<String> labelsToExclude = [
                          'Athkar',
                          'Ibadh Watcher',
                          'Qibla',
                          'Prayer Times'
                        ];

                        final List<LayoutScreensModel> filteredScreens =
                            controller.screens
                                .where((screen) =>
                                    !labelsToExclude.contains(screen.label))
                                .toList();
                        showModalBottomSheet(
                          backgroundColor: Colors.transparent,
                          enableDrag: false,
                          scrollControlDisabledMaxHeightRatio: 0.8,
                          builder: (context) => Stack(
                            children: [
                              SizedBox(
                                width: double.maxFinite,
                                child: Column(
                                  children: [
                                    Stack(
                                      alignment: Alignment.bottomCenter,
                                      children: [
                                        Column(
                                          children: [
                                            LayoutController
                                                .floatingButtonRaduis
                                                .verticalSpace,
                                            SizedBox(
                                              width: double.infinity,
                                              child: SvgPicture.asset(
                                                AppSvgs.kNavbar,
                                                // ignore: deprecated_member_use
                                                color: Theme.of(context)
                                                    .primaryColor,
                                                fit: BoxFit.fill,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Container(
                                          width: double.infinity,
                                          // height: 25.h,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                        const Positioned(
                                            top: 0,
                                            child: CloseBottomSheetButton()),
                                      ],
                                    ),
                                    Expanded(
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 16.w),
                                        width: double.infinity,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 16.w,
                                    right: 16.w,
                                    top:
                                        (LayoutController.floatingButtonRaduis *
                                                2.5)
                                            .h),
                                child: ListView(
                                  children: [
                                    CustomText(
                                      'Services',
                                      style: TextStyle(
                                        fontSize: 22.sp,
                                      ),
                                    ),
                                    16.verticalSpace,
                                    GridView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: filteredScreens.length,
                                      gridDelegate:
                                          SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 3,
                                        crossAxisSpacing: 8.w,
                                        mainAxisSpacing: 8.w,
                                      ),
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return AppGridTile(
                                          label: filteredScreens[index].label,
                                          svgAsset: filteredScreens[index].svg,
                                          onTap: () {
                                            controller.changeScreenLayout(
                                                filteredScreens[index].screen);
                                            Get.back();
                                          },
                                        );
                                      },
                                    ),
                                    16.verticalSpace,
                                  ],
                                ),
                              ),
                            ],
                          ),
                          context: context,
                        );
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: AppColor.kOrangeColor.withOpacity(0.3),
                              spreadRadius: 5,
                              blurRadius: 15,
                            ),
                          ],
                          color: AppColor.kOrangeColor,
                          shape: BoxShape.circle,
                        ),
                        height: LayoutController.floatingButtonRaduis * 2,
                        width: LayoutController.floatingButtonRaduis * 2,
                        padding: EdgeInsets.all(14.w),
                        child: SvgPicture.asset(
                          AppSvgs.kCategory,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                  const AudioPlayerBar()
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class AppGridTile extends StatelessWidget {
  const AppGridTile({
    super.key,
    this.onTap,
    required this.label,
    required this.svgAsset,
  });
  final String label;
  final String svgAsset;
  final VoidCallback? onTap;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 90.h,
      width: 100.h,
      child: Center(
        child: SmoothEdgesContainer(
          borderRadius: BorderRadius.circular(60.r),
          color: AppColor.kRectangleColor,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              child: Padding(
                padding: EdgeInsets.symmetric(
                  vertical: 16.h,
                  horizontal: 14.w,
                ),
                child: Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        svgAsset,
                        // ignore: deprecated_member_use
                        color: AppColor.kWhiteColor,
                        height: 25.h,
                      ),
                      4.verticalSpace,
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        alignment: Alignment.center,
                        child: CustomText(
                          label,
                          style: TextStyle(fontSize: 13.sp),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class AudioPlayerBar extends StatelessWidget {
  const AudioPlayerBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QuranCubit, QuranState>(
      builder: (context, state) {
        QuranCubit quranCubit = BlocProvider.of<QuranCubit>(context);

        if (quranCubit.currentSurahNumber != -1) {
          return Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              color: AppColor.kOrangeColor,
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Obx(
                      () => CustomText(
                        "now playing".trParams({
                          'sura': surahMap[quranCubit.currentSurahNumber]
                                  ?.arabicName ??
                              "",
                          'reciter': QuranController
                                  .instance.currentReciter.value?.name ??
                              "",
                        }),
                        style: TextStyle(
                          fontSize: 13.sp,
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      quranCubit.closeAudioBox();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: SvgPicture.asset(
                        width: 16,
                        AppSvgs.kClose,
                        // ignore: deprecated_member_use
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }
}

class WillPopScopeWidget extends StatelessWidget {
  const WillPopScopeWidget({
    super.key,
    this.canPop = false,
    required this.onWillPop,
    required this.child,
    this.allowExitApp = true,
  });

  final bool canPop;
  final Future<bool?> Function() onWillPop;
  final Widget child;
  final bool allowExitApp;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: canPop,
      child: child,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }

        final navigator = Navigator.of(context);
        final modalRoute = ModalRoute.of(context);
        final canPopScreen = modalRoute != null && modalRoute.canPop;
        final result = await onWillPop();

        if (true == result) {
          if (allowExitApp || canPopScreen) {
            navigator.pop();
          }
        }
      },
    );
  }
}
