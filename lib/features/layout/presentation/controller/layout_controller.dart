import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home_widget/home_widget.dart';
import 'package:logger/logger.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_consts.dart';

// import 'package:salawati/core/utils/location_controller.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_screen.dart';
import 'package:salawati/features/hijri_calander/presentation/screens/hijri_calander_screen.dart';
import 'package:salawati/features/ibadah/presentation/screens/ibadah_screen.dart';
import 'package:salawati/features/layout/data/layout_screens_model.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/mosque/presentation/cubit/mosque_cubit.dart';
import 'package:salawati/features/mosque/presentation/screens/mosque_screen.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/prayer/presentation/screens/prayer_screen.dart';
import 'package:salawati/features/prayer/presentation/widgets/new_widget.dart';
import 'package:salawati/features/qiblah/presentation/widgets/qiblah_view.dart';
import 'package:salawati/features/quran/presentation/screens/reciters_screen.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/tasbeeh/presentation/screens/tasbeeh_screen.dart';
import 'package:salawati/features/zakah_calculator/presentation/screens/zakah_type_screen.dart';
import 'package:location/location.dart';

class LayoutController extends GetxController with WidgetsBindingObserver {
  static LayoutController get instance => Get.find();

  static double get floatingButtonRaduis => 0.08.sw.clamp(30, 60).toDouble();
  final PrayerController prayerController = Get.find();
  final SettingsController settingsController = Get.find();
  final _logger = Logger(
    filter: ProductionFilter(),
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      colors: false,
      printEmojis: false,
    ),
    output: ConsoleOutput(),
  );

  @override
  onInit() async {
    print("dsjwfwehfjwjhfhewhjfh");
    // Split initialization tasks
    await (_initializeNotificationsAndLocation());

    unawaited(_handleCriticalInitializations());
    WidgetsBinding.instance.addObserver(this);
    super.onInit();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      _handleAppResume();
      if (Platform.isIOS) {
        updateSalatWidget();
      }
    }
  }

  Future<void> updateSalatWidget() async {
    if (Get.isRegistered<PrayerDataService>()) {
      try {
        await PrayerDataService.instance.refreshPrayerData();
        await PrayerDataService.instance.sendToWidget();
        debugPrint('Widget, is Synced in background on callbackDispatcher ');
      } catch (e) {
        debugPrint('Widget on callbackDispatcher group amin $e');
      }
    }
  }

  void _handleAppResume() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final prayerController = Get.find<PrayerController>();
      final userSetLocation = cacheMemory.read(USER_SET_LOCATION) == true;

      if (!userSetLocation) {
        LocationService.instance.updateCurrentLocation();
      }

      // PrayerTimesHomeWidget.updatePrayerTimeWidget();
      // AppFunctions.scheduleAthansWarning();
      prayerController.athanNotificationSetup();
    });
  }

  Future<void> _initializeNotificationsAndLocation() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final userSetLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;

      Get.log("Fursan:: user set notfication  $userSetLocation");
      // Only handle location initialization for non-manual locations
      if (!userSetLocation) {
        await _handleLocationInitialization();
        // Initialize location services
        await LocationService.instance.initializeLocationServices();
      } else {
        // Directly load manual location without services
        debugPrint('amin Directly load manual location without services');
        await _handleManualLocationInitialization();
      }

      // Legacy notification initialization removed - now handled by awesome_notifications
      // All notifications are now managed by AwesomeNotificationsService
    });
  }

  Future<void> _handleManualLocationInitialization() async {
    debugPrint('amin _handleManualLocationInitialization');
    try {
      final lat = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
      final lng = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
      final city = cacheMemory.read(CITY) ?? 'Makkah'.tr;
      final country = cacheMemory.read(COUNTRY) ?? 'Saudi Arabia'.tr;

      if (prayerController.isValidCoordinate(lat, lng)) {
        prayerController.latitude = lat;
        prayerController.longitude = lng;
        prayerController.currentCity.value = city;
        prayerController.currentCountry.value = country;

        await prayerController.calculatePrayerTimes();

        // Update home widget directly
        await HomeWidget.saveWidgetData<String>('city_name', city);
        await HomeWidget.saveWidgetData<double>('latitude', lat);
        await HomeWidget.saveWidgetData<double>('longitude', lng);
      }
    } catch (e) {
      debugPrint('amin fallbackToDefaultLocation 1');
      await prayerController.fallbackToDefaultLocation();
    }
  }

  Future<void> _handleCriticalInitializations() async {
    debugPrint('amin _handleCriticalInitializations');

    try {
      await Future.wait([
        _safeAsyncCall(() => settingsController.athkarNotificationSetup()),
        _safeAsyncCall(() => prayerController.athanNotificationSetup()),
        _safeAsyncCall(() => prayerController.scheduleAthansWarning()),
      ]);
      print("complated.......");
    } catch (e, stack) {
      _logger.e('Critical init failed', error: e, stackTrace: stack);
    }
  }

  Future<void> _safeAsyncCall(Future<void> Function() callback) async {
    try {
      await callback();
    } catch (e, stack) {
      _logger.e('Async operation failed', error: e, stackTrace: stack);
      rethrow;
    }
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    // Workmanager().cancelByTag("salawati.iOSBackgroundAppRefresh");
    // _clearReferences();
    super.onClose();
  }

  void _clearReferences() {
    // Avoid clearing screens list if it's static configuration
    currScreen = const SizedBox.shrink();
    // Consider adding disposers for any stream subscriptions
  }

  Future<void> _handleLocationInitialization() async {
    debugPrint('amin _handleLocationInitialization');
    try {
      final bool isManualLocation =
          cacheMemory.read(USER_SET_LOCATION) ?? false;
      final bool isLocationAuto = cacheMemory.read(IS_LOCATION_AUTO) ?? true;
      debugPrint('aminamin55 $isManualLocation $isLocationAuto');
      if (isManualLocation) {
        await _handleManualLocationInitialization();
        return;
      }

      Location location = Location();

      bool serviceEnabled;

      serviceEnabled = await location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await location.requestService();
        if (!serviceEnabled) {
          return;
        }
      }
      // Only proceed with location services if auto-location enabled
      // final locationController = Get.find<LocationController>();

      final permissionResult =
          await LocationService.instance.requestLocationPermission();
      if (permissionResult != LocationPermissionResult.granted) {
        debugPrint('amin LocationPermissionResult.granted  error');
        // await LocationService.instance.updateCurrentLocation();
        await prayerController.fallbackToDefaultLocation();
        return;
      }
      await LocationService.instance.updateCurrentLocation();
    } catch (e) {
      debugPrint('amin LocationPermissionResult.granted  error:$e');

      await prayerController.fallbackToDefaultLocation();
    }
  }

  List<LayoutScreensModel> screens = [
    LayoutScreensModel(
      label: 'Prayer Times',
      screen: const PrayerScreen(),
      svg: AppSvgs.kMasjid,
    ),
    LayoutScreensModel(
      label: 'Qibla',
      screen: const QiblahScreen(),
      svg: AppSvgs.kQibla,
    ),
    LayoutScreensModel(
      label: 'Athkar',
      screen: const AthkarScreen(),
      svg: AppSvgs.kAthkar,
    ),
    LayoutScreensModel(
      label: 'Ibadh Watcher',
      screen: const IbadahScreen(),
      svg: AppSvgs.kEbadah,
    ),
    LayoutScreensModel(
      label: 'Tasbeeh',
      screen: TasbeehScreen(),
      svg: AppSvgs.kTasbeeh,
    ),
    LayoutScreensModel(
      label: 'Masjid near me',
      screen: BlocProvider(
        create: (context) => MosqueCubit(),
        child: const MosqueScreen(),
      ),
      svg: AppSvgs.kMousq,
    ),
    LayoutScreensModel(
      label: 'Quran Audio',
      screen: const RecitersScreen(),
      svg: AppSvgs.kQuran,
    ),
    // LayoutScreensModel(
    //   label: 'Hadith Today',
    //   screen: const HadeethScreen(),
    //   svg: AppSvgs.kHadeeth,
    // ),
    LayoutScreensModel(
      label: 'Zakah calculator',
      screen: const ZakahTypeScreen(),
      svg: AppSvgs.kZakaah,
    ),
    LayoutScreensModel(
      label: 'Taqweem Hijri',
      screen: const DateConverterPage(),
      svg: AppSvgs.kTaqweem,
    ),
  ];

  Widget currScreen = const PrayerScreen();
  int currIndex = 0;

  void changeScreenLayout(Widget screen, {int? index}) {
    if (index == 0 && currIndex == 0) {
      prayerController.resetPrayerPage();
    }
    currScreen = screen;
    currIndex = index ?? 5;

    if (index == 1 || index == 0) {
      // Request location permission for Qiblah or Prayer screens
      LocationService.instance.requestLocationPermission();
    }
    update();
  }
}
