import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/location/data/models/city_info.dart';
import 'package:salawati/features/location/data/models/location_model.dart';
import 'package:salawati/features/location/data/providers/locations_database_provider.dart';

enum LocationPermissionResult {
  granted,
  denied,
  permanentlyDenied,
  serviceDisabled,
  unknownError,
  pending,
}

class LocationService extends GetxService {
  static LocationService get instance => Get.find<LocationService>();

  // Dependencies
  final Logger _logger = Logger();

  // Reactive state
  final Rx<LocationModel?> currentLocation = Rx<LocationModel?>(null);
  final RxBool serviceEnabled = false.obs;
  final Rx<LocationPermission> permissionStatus = LocationPermission.denied.obs;
  final RxBool isPermissionRequestActive = false.obs;
  final RxBool isCurrentLocationUpdating = false.obs;
  final RxBool isPermissionRequestLocked = false.obs;
  final RxBool _isCheckingPermissions = false.obs;
  final RxBool _isUpdatingPosition = false.obs;
  final RxBool locationDialogShown = false.obs;

  // No stream subscription needed

  // Constants
  final _permissionCooldown = const Duration(seconds: 2);
  bool userCancelled = false;

  // Getters
  bool get hasValidPermission {
    return permissionStatus.value == LocationPermission.whileInUse ||
        permissionStatus.value == LocationPermission.always;
  }

  bool get hasLocationAccess =>
      serviceEnabled.value &&
      (permissionStatus.value == LocationPermission.whileInUse ||
          permissionStatus.value == LocationPermission.always);

  @override
  void onInit() {
    super.onInit();
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    debugPrint('LocationService: Initializing location service');

    // Load saved location data from shared preferences
    await _loadSavedLocationData();

    // Check if we need to initialize location services
    final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
    final bool isLocationAuto = cacheMemory.read(IS_LOCATION_AUTO) ?? false;

    if (isManualLocation || !isLocationAuto) {
      debugPrint(
          'LocationService: Using manual location or auto location disabled');
      return;
    }

    // Check location status
    await checkLocationStatus();
  }

  Future<void> _loadSavedLocationData() async {
    try {
      final double? latitude = cacheMemory.read(LATITUDE);
      final double? longitude = cacheMemory.read(LONGITUDE);
      final String? cityName = cacheMemory.read(CITY);
      final String? countryName = cacheMemory.read(COUNTRY);
      final bool isLocationAuto = cacheMemory.read(IS_LOCATION_AUTO) ?? false;
      final bool isManualLocation =
          cacheMemory.read(USER_SET_LOCATION) ?? false;

      // If we have saved location data, create a LocationModel
      if (latitude != null &&
          longitude != null &&
          cityName != null &&
          countryName != null) {
        currentLocation.value = LocationModel(
          latitude: latitude,
          longitude: longitude,
          cityName: cityName,
          countryName: countryName,
          isLocationAuto: isLocationAuto,
          isManualLocation: isManualLocation,
        );
        debugPrint(
            'LocationService: Loaded saved location data: ${currentLocation.value}');
      } else {
        debugPrint('LocationService: No saved location data found');
      }
    } catch (e) {
      _logger.e('LocationService: Error loading saved location data: $e');
    }
  }

  Future<void> checkLocationStatus() async {
    debugPrint('LocationService: Checking location status');
    if (_isCheckingPermissions.value ||
        isPermissionRequestActive.value ||
        isPermissionRequestLocked.value) {
      return;
    }

    _isCheckingPermissions.value = true;
    try {
      serviceEnabled.value = await Geolocator.isLocationServiceEnabled();
      permissionStatus.value = await Geolocator.checkPermission();
      debugPrint(
        'LocationService: Location service enabled: ${serviceEnabled.value}, Permission status: ${permissionStatus.value}',
      );
    } finally {
      _isCheckingPermissions.value = false;
    }
  }

  Future<LocationPermissionResult> requestLocationPermission() async {
    if (isPermissionRequestLocked.value) {
      _logger.w('LocationService: Permission request already in progress');
      return LocationPermissionResult.pending;
    }

    isPermissionRequestLocked.value = true;
    try {
      final status = await Geolocator.requestPermission();
      return _handlePermissionResult(status);
    } finally {
      isPermissionRequestLocked.value = false;
    }
  }

  LocationPermissionResult _handlePermissionResult(LocationPermission status) {
    switch (status) {
      case LocationPermission.deniedForever:
        return LocationPermissionResult.permanentlyDenied;
      case LocationPermission.denied:
        return LocationPermissionResult.denied;
      case LocationPermission.whileInUse:
      case LocationPermission.always:
        return LocationPermissionResult.granted;
      default:
        return LocationPermissionResult.unknownError;
    }
  }

  Future<LocationPermissionResult> handlePermissionFlow() async {
    debugPrint('LocationService: Entering handlePermissionFlow');
    if (isPermissionRequestLocked.value || isPermissionRequestActive.value) {
      return LocationPermissionResult.pending;
    }

    isPermissionRequestLocked.value = true;
    isPermissionRequestActive.value = true;

    try {
      final status = await Geolocator.requestPermission();
      permissionStatus.value = status;

      if (status == LocationPermission.denied) {
        userCancelled = true;
        Future.delayed(_permissionCooldown, () => userCancelled = false);
        return LocationPermissionResult.denied;
      }

      if (status == LocationPermission.whileInUse ||
          status == LocationPermission.always) {
        final servicesEnabled = await checkServiceStatus();

        if (!servicesEnabled) {
          return LocationPermissionResult.serviceDisabled;
        }

        return LocationPermissionResult.granted;
      }

      return _handlePermissionResult(status);
    } finally {
      isPermissionRequestLocked.value = false;
      isPermissionRequestActive.value = false;
    }
  }

  Future<bool> checkServiceStatus() async {
    serviceEnabled.value = await Geolocator.isLocationServiceEnabled();
    debugPrint(
        'LocationService: Location service enabled: ${serviceEnabled.value}');
    return serviceEnabled.value;
  }

  Future<void> updateCurrentLocation() async {
    isCurrentLocationUpdating.value = true;
    var position = await getCurrentPosition();
    if (position != null) {
      await updateLocation(position);
    }
    isCurrentLocationUpdating.value = false;
  }

  Future<Position?> getCurrentPosition() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationServiceDisabledException();
      }

      LocationSettings locationSettings;
      if (defaultTargetPlatform == TargetPlatform.android) {
        locationSettings = AndroidSettings(
          accuracy: LocationAccuracy.high,
        );
      } else if (defaultTargetPlatform == TargetPlatform.iOS ||
          defaultTargetPlatform == TargetPlatform.macOS) {
        locationSettings = AppleSettings(
          accuracy: LocationAccuracy.high,
          activityType: ActivityType.fitness,
          pauseLocationUpdatesAutomatically: true,
          showBackgroundLocationIndicator: false,
        );
      } else {
        locationSettings = const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 100,
        );
      }

      final position = await Geolocator.getCurrentPosition(
        locationSettings: locationSettings,
      );

      debugPrint('LocationService: Current position: $position');
      return position;
    } catch (e) {
      _logger.e('LocationService: Error getting current position: $e');
      return null;
    }
  }

  Future<void> updateLocation(Position position) async {
    if (_isUpdatingPosition.value) return;
    _isUpdatingPosition.value = true;

    try {
      // Get city and country name
      final cityInfo =
          await getCityAndCountryName(position.latitude, position.longitude);
      debugPrint("LocationService: cityInfo = $cityInfo");
      // Create location model
      final locationModel = LocationModel.fromPosition(
        position,
        cityName: cityInfo.cityName,
        countryName: cityInfo.countryName,
        isLocationAuto: true,
        isManualLocation: false,
      );

      // Update current location
      currentLocation.value = locationModel;

      // Save to shared preferences
      await saveLocationData(locationModel);

      debugPrint('LocationService: Location updated: $locationModel');
    } catch (e) {
      _logger.e('LocationService: Location updated:  $e');
    } finally {
      _isUpdatingPosition.value = false;
    }
  }

  Future<void> saveLocationData(LocationModel locationModel) async {
    try {
      await cacheMemory.write(LATITUDE, locationModel.latitude);
      await cacheMemory.write(LONGITUDE, locationModel.longitude);
      await cacheMemory.write(CITY, locationModel.cityName);
      await cacheMemory.write(COUNTRY, locationModel.countryName);
      await cacheMemory.write(IS_LOCATION_AUTO, locationModel.isLocationAuto);
      await cacheMemory.write(
          USER_SET_LOCATION, locationModel.isManualLocation);

      debugPrint('LocationService: Location data saved to shared preferences');
    } catch (e) {
      _logger.e('LocationService: Error saving location data: $e');
    }
  }

  Future<({String cityName, String countryName})> getCityAndCountryName(
      double latitude, double longitude) async {
    String cityName = 'Unknown';
    String countryName = 'Unknown';

    try {
      // First try using geocoding package
      if (!(await Connectivity().checkConnectivity())
          .contains(ConnectivityResult.none)) {
        List<Placemark> placemarks =
            await placemarkFromCoordinates(latitude, longitude);

        if (placemarks.isNotEmpty) {
          Placemark place = placemarks.first;

          if (Platform.isIOS) {
            cityName = place.name ??
                place.subLocality ??
                place.locality ??
                place.subAdministrativeArea ??
                place.administrativeArea ??
                'Unknown';
            countryName = place.country ?? 'Unknown';
          } else {
            cityName = place.administrativeArea ??
                place.locality ??
                place.subLocality ??
                place.subAdministrativeArea ??
                'Unknown';
            countryName = place.country ?? 'Unknown';
          }

          debugPrint(
              'LocationService: City and country from geocoding: $cityName, $countryName');
          return (cityName: cityName, countryName: countryName);
        }
      }

      // If geocoding fails, try using offline database
      debugPrint('LocationService: Using offline database for city lookup');
      CityInfo? cityInfo =
          await LocationsDatabaseProvider.getNearestCity(longitude, latitude);

      if (cityInfo != null) {
        cityName = cityInfo.name;
        countryName = cityInfo.countryName;
        debugPrint(
            'LocationService: City and country from offline database: $cityName, $countryName');
      } else {
        debugPrint('LocationService: Error getting city and country name:');

        // Fallback to default values
        cityName = cacheMemory.read(CITY) ?? 'Makkah';
        countryName = cacheMemory.read(COUNTRY) ?? 'Saudi Arabia';
        debugPrint(
            'LocationService: Using fallback city and country: $cityName, $countryName');
      }
    } catch (e) {
      debugPrint('LocationService: Error getting city and country name: $e');
      // Fallback to default values
      cityName = cacheMemory.read(CITY) ?? 'Makkah';
      countryName = cacheMemory.read(COUNTRY) ?? 'Saudi Arabia';
    }

    return (cityName: cityName, countryName: countryName);
  }

  Future<void> setManualLocation({
    required double latitude,
    required double longitude,
    required String cityName,
    required String countryName,
  }) async {
    try {
      final locationModel = LocationModel(
        latitude: latitude,
        longitude: longitude,
        cityName: cityName,
        countryName: countryName,
        isLocationAuto: false,
        isManualLocation: true,
      );

      // Update current location
      currentLocation.value = locationModel;

      // Save to shared preferences
      await saveLocationData(locationModel);

      debugPrint('LocationService: Manual location set: $locationModel');
    } catch (e) {
      _logger.e('LocationService: Error setting manual location: $e');
    }
  }

  Future<void> toggleAutoLocation(bool enabled) async {
    try {
      if (!enabled) {
        // If disabling auto location, keep the current location but mark it as manual
        if (currentLocation.value != null) {
          final updatedLocation = currentLocation.value!.copyWith(
            isLocationAuto: false,
            isManualLocation: true,
          );

          currentLocation.value = updatedLocation;
          await saveLocationData(updatedLocation);
        }

        debugPrint('LocationService: Auto location disabled');
        return;
      }

      // If enabling auto location
      await cacheMemory.write(IS_LOCATION_AUTO, true);
      await cacheMemory.write(USER_SET_LOCATION, false);

      // Check location permission and service status
      await checkLocationStatus();

      if (!hasValidPermission) {
        final result = await handlePermissionFlow();

        if (result != LocationPermissionResult.granted) {
          debugPrint('LocationService: Failed to get location permission');
          return;
        }
      }

      if (!serviceEnabled.value) {
        debugPrint('LocationService: Location service is disabled');
        return;
      }

      // Get current position
      final position = await getCurrentPosition();
      if (position != null) {
        await updateLocation(position);
      }

      debugPrint('LocationService: Auto location enabled');
    } catch (e) {
      _logger.e('LocationService: Error toggling auto location: $e');
    }
  }

  // Location updates are now handled manually instead of through a stream

  Future<void> initializeLocationServices() async {
    debugPrint('LocationService: Starting location services initialization');
    await Future.delayed(const Duration(seconds: 1));

    final serviceEnabled = await checkServiceStatus();
    debugPrint('LocationService: Location service enabled: $serviceEnabled');

    await checkLocationStatus();

    if (!hasValidPermission) {
      debugPrint(
          'LocationService: No valid permission, starting permission flow');
      final result = await handlePermissionFlow();
      debugPrint('LocationService: Permission flow result: $result');

      if (result == LocationPermissionResult.granted) {
        debugPrint('LocationService: Permission granted, fetching location');
        await getCurrentPosition();
      }
    } else {
      debugPrint('LocationService: Already has valid location permissions');
      await getCurrentPosition();
    }
  }
}
