import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/utils.dart';
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

import '../models/city_info.dart';

class LocationsDatabaseProvider {
  static String databaseName = 'locations.sqlite';
  static String citiesTable = 'cities';
  static String countriesTable = 'countries';
  static Database? _db;
  static Database get db {
    if (_db == null) {
      throw StateError('Field \'db\' has not been initialized. Call init() first.');
    }
    return _db!;
  }

  static bool get isInitialized => _db != null && _db!.isOpen;

  static Future<void> init() async {
    if (isInitialized) return; // Already initialized
    await ensureDatabaseExists();
    await openDB();
  }

  static Future<void> ensureDatabaseExists() async {
    try {
      var version = 0;
      final prefs = await SharedPreferencesWithCache.create(
        cacheOptions: const SharedPreferencesWithCacheOptions(),
      );

      var currentVersion = prefs.getInt('locationsDatabaseVersion') ?? -1;
      if (version == currentVersion) {
        var path = join(await getDatabasesPath(), databaseName);
        var exists = await databaseExists(path);
        if (exists) {
          if (kDebugMode) {
            debugPrint('Using existing database for locations');
          }
          return;
        }
      }

      unawaited(prefs.setInt('locationsDatabaseVersion', version));
      var path = join(await getDatabasesPath(), databaseName);
      var exists = await databaseExists(path);
      if (exists) {
        await File(path).delete();
        try {
          await Directory(await getDatabasesPath()).create(recursive: true);
        } catch (_) {}
      }

      var data = await rootBundle.load(
        join('assets/dbs', databaseName),
      );
      List<int> bytes = data.buffer.asUint8List(
        data.offsetInBytes,
        data.lengthInBytes,
      );
      await File(path).writeAsBytes(bytes, flush: true);

      if (kDebugMode) {
        debugPrint('Locations database created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error ensuring locations database exists: $e');
      }
      rethrow;
    }
  }

  static Future<void> openDB() async {
    try {
      final path = join(await getDatabasesPath(), databaseName);
      _db = await openDatabase(path);

      if (kDebugMode) {
        debugPrint('Locations database opened successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error opening locations database: $e');
      }
      rethrow;
    }
  }

  static Future<void> closeDB() async {
    if (_db != null && _db!.isOpen) {
      await _db!.close();
      _db = null;
    }
  }

  static double haversineDistance(
      double lon1, double lat1, double lon2, double lat2) {
    var R = 6371; // Radius of the earth in km
    var dLat = toRadians(lat2 - lat1);
    var dLon = toRadians(lon2 - lon1);
    lat1 = toRadians(lat1);
    lat2 = toRadians(lat2);

    var a =
        pow(sin(dLat / 2), 2) + pow(sin(dLon / 2), 2) * cos(lat1) * cos(lat2);
    var c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c; // Distance in km
  }

  // Helper to convert degrees to radians
  static double toRadians(double degree) {
    return degree * pi / 180;
  }

  // --- Helper to calculate a bounding box ---
  /// Calculates the bounding coordinates for a rectangle around a center point
  /// for a given radial distance. Handles latitude/longitude ranges correctly.
  static Map<String, double> calculateBoundingBox(
      double centerLat, double centerLon, double radiusKm) {
    const earthRadiusKm = 6371.0; // Use a consistent radius

    // Angular distance in radians on a great circle
    var radDist = radiusKm / earthRadiusKm;

    // Center point coordinates in radians
    var radLat = toRadians(centerLat);
    var radLon = toRadians(centerLon);

    var minRadLat = radLat - radDist;
    var maxRadLat = radLat + radDist;

    double minRadLon, maxRadLon;

    // Calculate longitude bounds, handling wrap-around near poles and date line
    if (minRadLat > -pi / 2 && maxRadLat < pi / 2) {
      // Not near a pole
      var deltaLon = asin(sin(radDist) / cos(radLat));
      minRadLon = radLon - deltaLon;
      if (minRadLon < -pi) minRadLon += 2 * pi; // Adjust for wrap past -180

      maxRadLon = radLon + deltaLon;
      if (maxRadLon > pi) maxRadLon -= 2 * pi; // Adjust for wrap past +180
    } else {
      // A pole is within the distance radius, longitude spans the entire range
      minRadLat = max(minRadLat, -pi / 2); // Clamp latitude to poles
      maxRadLat = min(maxRadLat, pi / 2);
      minRadLon = -pi; // Longitude extends fully around
      maxRadLon = pi;
    }

    // Convert radians back to degrees for SQL query
    return {
      'minLat': toDegrees(minRadLat),
      'maxLat': toDegrees(maxRadLat),
      'minLon': toDegrees(minRadLon),
      'maxLon': toDegrees(maxRadLon),
    };
  }

  static double toDegrees(double radians) {
    return radians * 180 / pi;
  }

  static Future<CityInfo?> getNearestCity(
      double userLongitude, double userLatitude,
      {
      // Optional: Control the initial search radius in Km
      double initialRadiusKm = 5.0}) async {

    // Ensure database is initialized
    if (!isInitialized) {
      Get.log('Database not initialized, attempting to initialize...');
      try {
        await init();
      } catch (e) {
        Get.log('Failed to initialize database: $e');
        return null;
      }
    }

    var minDistance = double.infinity;
    CityInfo? nearestCityInfo;
    var candidateCities = <Map<String, dynamic>>[]; // Store candidates here

    try {
      // 1. Calculate the Bounding Box coordinates
      final bounds =
          calculateBoundingBox(userLatitude, userLongitude, initialRadiusKm);
      final minLat = bounds['minLat']!;
      final maxLat = bounds['maxLat']!;
      final minLon = bounds['minLon']!;
      final maxLon = bounds['maxLon']!;

      if (kDebugMode) {
        debugPrint(
            'aminBounding Box: Lat($minLat to $maxLat), Lon($minLon to $maxLon)');
      }

      // 2. Construct the SQL Query with Bounding Box Filter
      // Handle the case where the longitude range wraps around the +/-180 meridian
      String longitudeWhereClause;
      List<dynamic> queryParams;

      if (minLon <= maxLon) {
        // Normal case: Longitude range does not cross the date line
        longitudeWhereClause = 'c.longitude BETWEEN ? AND ?';
        // Parameters MUST be in the order of the '?' placeholders
        queryParams = [minLat, maxLat, minLon, maxLon];
      } else {
        // Wrap-around case: e.g., minLon = 170, maxLon = -170
        // Query becomes (longitude >= 170 OR longitude <= -170)
        longitudeWhereClause = '(c.longitude >= ? OR c.longitude <= ?)';
        // Parameters MUST still be in the order of the '?' placeholders
        queryParams = [minLat, maxLat, minLon, maxLon];
      }

      // Select necessary columns, join, and apply the bounding box WHERE clause
      // NOTE: The coordinate check (IS NOT NULL) is technically redundant if
      // BETWEEN is used, but doesn't hurt. It was removed from the JOIN condition.
      final sql = '''
        SELECT
          c.arabic_name AS name,
          co.arabic_name AS country_name,
          c.longitude,
          c.latitude
        FROM
          $citiesTable c
        JOIN
          $countriesTable co ON c.country_id = co.country_id
        WHERE
          c.latitude BETWEEN ? AND ?   -- Latitude check
          AND $longitudeWhereClause       -- Longitude check (handles wrap-around)
          AND c.longitude IS NOT NULL     -- Still good practice to ensure non-null
          AND c.latitude IS NOT NULL
        -- Optional: LIMIT the number of candidates further if needed, though
        -- the bounding box should be the primary filter.
        -- LIMIT 1000
      ''';

      // 3. Execute the Query to get candidate cities within the box
      candidateCities = await db.rawQuery(sql, queryParams);

      if (candidateCities.isEmpty) {
        Get.log(
            'No cities found within initial radius ($initialRadiusKm km). Coordinates might be in an empty area, or consider increasing the radius.');
        // Optional: Implement logic here to retry with a larger radius if desired
        return null;
      }

      Get.log(
          'Found ${candidateCities.length} candidate cities within $initialRadiusKm km bounding box. Calculating precise distances...');

      // 4. Calculate Precise Distance ONLY for Candidates
      // This loop now runs on a significantly smaller list (e.g., tens or hundreds
      // instead of hundreds of thousands)
      for (var cityMap in candidateCities) {
        var name = cityMap['name'] as String?; // From c.arabic_name
        var countryName = cityMap['country_name'] as String?;
        dynamic rawLon = cityMap['longitude'];
        dynamic rawLat = cityMap['latitude'];

        var cityLon = parseCoordinate(rawLon);
        var cityLat = parseCoordinate(rawLat);

        // Ensure essential data is valid after parsing
        // Use the primary identifier (arabic_name) for the check
        if (name == null || cityLon == null || cityLat == null) {
          if (kDebugMode) {
            debugPrint(
                'Skipping candidate due to null values after parsing: $cityMap');
          }
          continue;
        }

        // Calculate PRECISE Haversine distance
        var distance =
            haversineDistance(userLongitude, userLatitude, cityLon, cityLat);

        // Update nearest city if this one is closer
        if (distance < minDistance) {
          minDistance = distance;
          // Ensure your CityInfo model constructor matches these parameters
          nearestCityInfo = CityInfo(
            name: name, // This is c.arabic_name aliased as 'name'
            // name: null, // Fetch c.name if you need English city name too
            countryName: countryName ?? '',
            longitude: cityLon,
            latitude: cityLat,
            distance: distance,
          );
        }
      } // End of loop through candidates

      // 5. Log and Return Result
      if (nearestCityInfo != null) {
        Get.log(
            'Nearest city identified: ${nearestCityInfo.name} / ${nearestCityInfo.countryName} (Distance: ${minDistance.toStringAsFixed(2)} km)');
      } else {
        Get.log(
          'Could not determine nearest city from candidates within the bounding box (all candidates skipped or list was empty after query).',
        );
      }

      return nearestCityInfo;
    } catch (e, stacktrace) {
      // Catch and log stacktrace for better debugging
      Get.log('Error querying nearest city: $e\n$stacktrace');
      return null; // Return null on error
    }
  }

  static double? parseCoordinate(dynamic rawCoord) {
    if (rawCoord == null) return null;
    if (rawCoord is double) return rawCoord;
    if (rawCoord is int) return rawCoord.toDouble();
    if (rawCoord is String) return double.tryParse(rawCoord);
    return null;
  }

  static Future<List<CityInfo>> searchCitiesByName(String query,
      {int limit = 50}) async {

    // Ensure database is initialized
    if (!isInitialized) {
      Get.log('Database not initialized, attempting to initialize...');
      try {
        await init();
      } catch (e) {
        Get.log('Failed to initialize database: $e');
        return [];
      }
    }

    var results = <CityInfo>[];
    final trimmedQuery = query.trim();

    // Return empty list if query is empty
    if (trimmedQuery.isEmpty) {
      return results;
    }

    // Prepare the search term for SQL LIKE clause
    final searchTerm = '%$trimmedQuery%';

    try {
      // Construct the SQL query
      // Selects arabic names primarily, joins with countries
      // Searches both c.arabic_name and c.name (assuming English name column exists)
      final sql = """
        SELECT
          c.arabic_name AS name,           -- Primary name (Arabic)
          co.arabic_name AS country_name,  -- Country name (Arabic)
          c.name AS english_name,          -- English city name (optional)
          co.name AS english_country_name,-- English country name (optional)
          c.longitude,
          c.latitude
        FROM
          $citiesTable c
        JOIN
          $countriesTable co ON c.country_id = co.country_id
        WHERE
          (c.arabic_name LIKE ? OR c.name LIKE ?) -- Search Arabic OR English city name
          AND c.longitude IS NOT NULL
          AND c.latitude IS NOT NULL
          -- AND c.iscities15000 = 1
        ORDER BY
          -- Optional: Order by name, or add relevance scoring if needed
          c.iscities15000 desc ,
          c.is_parent Desc ,
          c.arabic_name ASC
        LIMIT ? -- Use placeholder for limit
      """;

      // Execute the query with parameters for search terms and limit
      final List<Map<String, dynamic>> maps =
          await db.rawQuery(sql, [searchTerm, searchTerm, limit]);

      Get.log(
          "[searchCitiesByName] Found ${maps.length} cities matching query '$trimmedQuery'.");

      // Process the results
      for (var cityMap in maps) {
        var arabicName = cityMap['name'] as String?; // This is c.arabic_name
        var arabicCountryName =
            cityMap['country_name'] as String?; // This is co.arabic_name
        // Optionally get English names if needed by CityInfo model:
        var englishName = cityMap['english_name'] as String?;
        // var englishCountryName = cityMap['english_country_name'] as String?;

        dynamic rawLon = cityMap['longitude'];
        dynamic rawLat = cityMap['latitude'];

        var cityLon = parseCoordinate(rawLon);
        var cityLat = parseCoordinate(rawLat);

        // Ensure essential data is valid
        if (arabicName == null ||
            cityLon == null ||
            cityLat == null ||
            englishName == null) {
          if (kDebugMode) {
            debugPrint("Skipping search result due to null values: $cityMap");
          }
          continue;
        }

        // Create CityInfo object
        // Ensure CityInfo constructor matches the data you want to store
        // This example primarily uses Arabic names based on previous methods
        results.add(CityInfo(
          name: arabicName, // Using arabic name as primary 'name'
          countryName: arabicCountryName ?? '', // Using arabic country name
          longitude: cityLon,
          latitude: cityLat,
          distance: 0, // Distance is not applicable for search results
          // Add other fields if your CityInfo model supports them:
          // englishName: englishName,
          // englishCountryName: englishCountryName,
        ));
      }

      return results;
    } catch (e, stacktrace) {
      Get.log("[searchCitiesByName] Error searching cities: $e\n$stacktrace");
      return []; // Return empty list on error
    }
  }
}
