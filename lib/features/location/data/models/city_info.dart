class CityInfo {
  final String name;
  final String countryName;
  final double longitude;
  final double latitude;
  final double distance; // Optional: include distance in result

  CityInfo({
    required this.name,
    required this.longitude,
    required this.latitude,
    required this.distance,
    required this.countryName,
  });

  @override
  String toString() {
    return 'CityInfo(name: $name, lon: $longitude, lat: $latitude, distance: ${distance.toStringAsFixed(2)}km)';
  }
}
