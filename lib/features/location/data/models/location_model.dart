import 'package:geolocator/geolocator.dart';

class LocationModel {
  final double latitude;
  final double longitude;
  final String cityName;
  final String countryName;
  final bool isLocationAuto;
  final bool isManualLocation;
  final DateTime timestamp;

  LocationModel({
    required this.latitude,
    required this.longitude,
    required this.cityName,
    required this.countryName,
    required this.isLocationAuto,
    required this.isManualLocation,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      cityName: json['cityName'] as String,
      countryName: json['countryName'] as String,
      isLocationAuto: json['isLocationAuto'] as bool,
      isManualLocation: json['isManualLocation'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'cityName': cityName,
      'countryName': countryName,
      'isLocationAuto': isLocationAuto,
      'isManualLocation': isManualLocation,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory LocationModel.fromPosition(
    Position position, {
    required String cityName,
    required String countryName,
    required bool isLocationAuto,
    required bool isManualLocation,
  }) {
    return LocationModel(
      latitude: position.latitude,
      longitude: position.longitude,
      cityName: cityName,
      countryName: countryName,
      isLocationAuto: isLocationAuto,
      isManualLocation: isManualLocation,
      timestamp: position.timestamp,
    );
  }

  LocationModel copyWith({
    double? latitude,
    double? longitude,
    String? cityName,
    String? countryName,
    bool? isLocationAuto,
    bool? isManualLocation,
    DateTime? timestamp,
  }) {
    return LocationModel(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cityName: cityName ?? this.cityName,
      countryName: countryName ?? this.countryName,
      isLocationAuto: isLocationAuto ?? this.isLocationAuto,
      isManualLocation: isManualLocation ?? this.isManualLocation,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  String toString() {
    return 'LocationModel(latitude: $latitude, longitude: $longitude, cityName: $cityName, countryName: $countryName, isLocationAuto: $isLocationAuto, isManualLocation: $isManualLocation, timestamp: $timestamp)';
  }
}
