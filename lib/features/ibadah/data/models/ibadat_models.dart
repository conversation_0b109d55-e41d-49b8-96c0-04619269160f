class IbadatModel {
  List<Ibadah> ibadat;
  IbadatModel({required this.ibadat});

  factory IbadatModel.fromJson(List ibadatList) => IbadatModel(
        ibadat: ibadatList.map((x) => Ibadah.fromJson(x)).toList(),
      );
}

class Ibadah {
  final int? id;
  final int? userId;
  final String ibadahName;
  final String type;
  final int? count;

  Ibadah({
    this.id,
    this.userId,
    required this.ibadahName,
    required this.type,
    required this.count,
  });

  factory Ibadah.fromJson(Map<String, dynamic> json) {
    return Ibadah(
      id: json['id'],
      userId: json['user_id'],
      ibadahName: json['ibadah_name'],
      type: json['type'],
      count: json['count'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'ibadah_name': ibadahName,
      'type': type,
      'count': count,
    };
  }
}
