import 'package:salawati/core/data/data_service.dart';
import 'package:salawati/core/models/message_model.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/ibadah/data/models/ibadat_models.dart';

import '../../../../core/data/data_state.dart';

class IbadahRepo {
  static Future<DataState> getUserIbadat() async {
    final response = await DataService.get(url: baseUrl + GET_USER_IBADAT_END_POINT);
    return DataService.dataRepoRequest(response: response, fromJson: IbadatModel.fromJson);
  }
  
  static Future<DataState> addUserIbadah({required Ibadah ibadah}) async {
    final response = await DataService.post(data: ibadah.toJson(), endPoint: ADD_USER_IBADAH_END_POINT);
    return DataService.dataRepoRequest(response: response, fromJson: MessageModel.fromJson);
  }
}
