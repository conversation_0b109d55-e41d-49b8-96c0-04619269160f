import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_animated_dropdown.dart';
import 'package:salawati/core/widgets/custom_button.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_cubit.dart';
import 'package:salawati/features/ibadah/presentation/screens/add_edit_ibadah_controllers.dart';

class AddIbadahScreen extends StatelessWidget {
  const AddIbadahScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Container(color: AppColor.kWhiteColor.withOpacity(0.035)),
          Container(
            padding: EdgeInsets.only(top: 0.15.sh),
            child: SmoothEdgesContainer(
              borderRadius: BorderRadius.circular(60.r),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 20,
                    spreadRadius: 20,
                    color: AppColor.kWhiteColor.withOpacity(0.045),
                  ),
                ],
              ),
              child: Align(
                alignment: Alignment.topCenter,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(28.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              'Track new Ibadh',
                              style: TextStyle(
                                fontSize: 21.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            InkWell(
                                onTap: () => Get.back(),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: SvgPicture.asset(AppSvgs.kClose),
                                )),
                          ],
                        ),
                        0.08.sh.verticalSpace,
                        CustomText('Ibadh name'),
                        8.verticalSpace,
                        Container(
                          decoration: BoxDecoration(
                            color: AppColor.kRectangleColor,
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          child: CustomTextField(
                            hintText: 'Enter Ibadh name e.g (Salah)',
                            controller: ibadahNameController,
                          ),
                        ),
                        16.verticalSpace,
                        CustomText('Watcher period'),
                        CustomAnimatedDropdown<String>(
                          expandedFillColor: AppColor.kScaffoldColor,
                          hintText: 'Watcher period'.tr,
                          headerBuilder: (p0, p1, p2) {
                            return CustomText(p1);
                          },
                          items: const [
                            'daily',
                            'weekly',
                            'monthly',
                            'annually'
                          ],
                          listItemBuilder:
                              (context, item, isSelected, onItemSelect) {
                            return CustomText(item);
                          },
                          onChanged: (p0) => ibadahTypeController.text = p0!,
                        ),
                        16.verticalSpace,
                        CustomText('Counter'),
                        8.verticalSpace,
                        Container(
                          decoration: BoxDecoration(
                            color: AppColor.kRectangleColor,
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          child: CustomTextField(
                            hintText: 'How many times ?',
                            controller: ibadahCountController,
                          ),
                        ),
                        // 16.verticalSpace,
                        // const SettingsSwitchItemBuilder(
                        //   title: 'Enable notification',
                        //   subtitle:
                        //       'You will be notified every time your reports is ready',
                        //   rowCrossAxisAlignment: CrossAxisAlignment.start,
                        // ),
                        24.verticalSpace,
                        CustomButton(
                          onTap: () {
                            BlocProvider.of<IbadahCubit>(context)
                                .addIbadahHandleControllers();
                          },
                          label: 'Add',
                          color: AppColor.kOrangeColor,
                        ),
                        0.1.sh.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
