import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:hijri/hijri_calendar.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_cubit.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_state.dart';
import 'package:salawati/features/ibadah/presentation/widgets/ibadat_list_view.dart';

class IbadahScreen extends StatelessWidget {
  const IbadahScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: const ValueKey<String>('IbadahScreen'),
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.1.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SettingsShareRow(),
                    16.verticalSpace,
                    if (cacheMemory.read(TOKEN) != null)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                'Ibadh Watcher',
                                style: TextStyle(
                                  color: AppColor.kWhiteColor.withOpacity(0.7),
                                ),
                              ),
                              Builder(builder: (_) {
                                var hijriToday = HijriCalendar.now();
                                return CustomText(
                                  '${hijriToday.hDay} ${hijriToday.longMonthName}, ${hijriToday.hYear}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              }),
                            ],
                          ),
                          BlocBuilder<IbadahCubit, IbadahState>(
                              builder: (context, state) {
                            if (BlocProvider.of<IbadahCubit>(context)
                                    .ibadatModel !=
                                null) {
                              if (BlocProvider.of<IbadahCubit>(context)
                                  .ibadatModel!
                                  .ibadat
                                  .isNotEmpty) {
                                return InkWell(
                                  onTap: navigateToAddOrAuth,
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 8.h),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        const Icon(
                                          Icons.add,
                                          color: AppColor.kOrangeColor,
                                        ),
                                        8.horizontalSpace,
                                        Container(
                                          margin: EdgeInsets.only(top: 4.h),
                                          child: CustomText(
                                            'Add new',
                                            style: const TextStyle(
                                              height: 0,
                                              color: AppColor.kOrangeColor,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }
                            }
                            return const SizedBox();
                          }),
                        ],
                      ),
                    16.verticalSpace,
                    if (cacheMemory.read(TOKEN) != null)
                      const IbadatListView()
                    else
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          0.1.sh.verticalSpace,
                          CustomText(
                            "Please login first so you can save and track your watcher",
                            textAlign: TextAlign.center,
                          ),
                          16.verticalSpace,
                          TextButton(
                            onPressed: () => Get.toNamed(AppRouter.kAuthScreen,
                                arguments: false),
                            child: CustomText(
                              'Sign In',
                              style: const TextStyle(
                                  color: AppColor.kOrangeColor,
                                  decoration: TextDecoration.underline),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    0.15.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void navigateToAddOrAuth() {
    if (cacheMemory.read(TOKEN) != null) {
      Get.toNamed(AppRouter.kAddIbadahScreen);
    } else {
      Get.toNamed(AppRouter.kAuthScreen, arguments: false);
    }
  }
}
