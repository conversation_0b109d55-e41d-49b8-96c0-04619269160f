import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/features/ibadah/data/models/ibadat_models.dart';
import 'package:salawati/features/ibadah/data/repo/ibadah_repo.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_state.dart';
import 'package:salawati/features/ibadah/presentation/screens/add_edit_ibadah_controllers.dart';

import '../../../../core/data/data_state.dart';

class IbadahCubit extends Cubit<IbadahState> {
  IbadahCubit() : super(IbadahInitial());

  IbadatModel? ibadatModel;
  Future<void> getUserIbadat() async {
    emit(IbadatLoading());
    final dataState = await IbadahRepo.getUserIbadat();
    if (dataState is DataSuccess) {
      ibadatModel = dataState.data;
      emit(IbadatSuccess());
    }
    if (dataState is DataFailed) {
      if (dataState.error!.statusCode == 401) {
        await Get.toNamed(AppRouter.kAuthScreen, arguments: false);
      }
      emit(IbadatFailure(dataState.error!.statusMessage.toString()));
    }
  }

  Future<void> addUserIbadah({required Ibadah ibadah}) async {
    emit(AddIbadahLoading());
    final dataState = await IbadahRepo.addUserIbadah(ibadah: ibadah);
    if (dataState is DataSuccess) {
      ibadatModel!.ibadat.add(ibadah);
      AppFunctions.customSnackbar('Done'.tr, 'Successfully added'.tr,
          textColor: AppColor.kBlackColor);
      clearIbadahControllers();
      emit(AddIbadahSuccess(dataState.data.toString()));
    }
    if (dataState is DataFailed) {
      emit(AddIbadahFailure(dataState.error!.statusMessage.toString()));
    }
  }

  void addIbadahHandleControllers() {
    if (ibadahNameController.text.isEmpty) {
      AppFunctions.showErrorMessage('Ibadah name is required'.tr);
    } else if (ibadahCountController.text.isEmpty) {
      AppFunctions.showErrorMessage('Counter is required'.tr);
    } else if (ibadahTypeController.text.isEmpty) {
      AppFunctions.showErrorMessage('Watcher period is required'.tr);
    } else {
      addUserIbadah(
        ibadah: Ibadah(
          count: int.tryParse(ibadahCountController.text),
          ibadahName: ibadahNameController.text,
          type: ibadahTypeController.text,
        ),
      );
    }
  }
}
