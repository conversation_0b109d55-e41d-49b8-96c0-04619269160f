abstract class IbadahState {}

class IbadahInitial extends IbadahState {}

class IbadatLoading extends IbadahState {}

class IbadatSuccess extends IbadahState {}

class IbadatFailure extends IbadahState {
  final String error;
  IbadatFailure(this.error);
}

class AddIbadahLoading extends IbadahState {}

class AddIbadahSuccess extends IbadahState {
  final String message;
  AddIbadahSuccess(this.message);
}

class AddIbadahFailure extends IbadahState {
  final String error;
  AddIbadahFailure(this.error);
}
