import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';

class IbadahEmptyWidget extends StatelessWidget {
  const IbadahEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        0.03.sh.verticalSpace,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 0.1.sw),
          child: Column(
            children: [
              InkWell(
                borderRadius: BorderRadius.circular(1000),
                onTap: navigateToAddOrAuth,
                child: AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColor.kOrangeColor.withOpacity(0.1),
                    ),
                    child: LayoutBuilder(builder: (context, constraint) {
                      return Center(
                          child: SvgPicture.asset(
                        AppSvgs.kPlusRounded,
                        // ignore: deprecated_member_use
                        color: AppColor.kOrangeColor,
                        height: constraint.maxHeight / 4,
                        width: constraint.maxHeight / 4,
                      ));
                    }),
                  ),
                ),
              ),
              16.verticalSpace,
              CustomText(
                'Create your first Ibadh watcher',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              CustomText(
                'With Ibadh watcher, you will track and update your ibadh tracker',
                style: TextStyle(
                  color: AppColor.kWhiteColor.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void navigateToAddOrAuth() {
    if (cacheMemory.read(TOKEN) != null) {
      Get.toNamed(AppRouter.kAddIbadahScreen);
    } else {
      Get.toNamed(AppRouter.kAuthScreen, arguments: false);
    }
  }
}
