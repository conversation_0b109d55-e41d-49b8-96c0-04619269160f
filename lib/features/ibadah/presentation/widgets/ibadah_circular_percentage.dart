import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'dart:math' as math;

import 'package:salawati/features/ibadah/presentation/widgets/ibadah_item_builder.dart';

class IbadahCircularPercentage extends StatelessWidget {
  const IbadahCircularPercentage({
    super.key,
    required this.widget,
  });

  final IbadahItemBuilder widget;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0.125.sw),
      child: Stack(
        alignment: Alignment.center,
        children: [
          AspectRatio(
            aspectRatio: 1,
            child: SizedBox(
              width: double.infinity,
              child: CircularProgressIndicator(
                strokeCap: StrokeCap.round,
                strokeWidth: 7.w,
                value: 1,
                color: const Color(0xfff2f2f2).withOpacity(0.2),
              ),
            ),
          ),
          Transform.rotate(
            angle: math.pi,
            child: AspectRatio(
              aspectRatio: 1,
              child: SizedBox(
                width: double.infinity,
                child: CircularProgressIndicator(
                  strokeCap: StrokeCap.round,
                  strokeWidth: 7.w,
                  value: widget.counter / widget.total,
                  color: widget.color,
                ),
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColor.kRectangleColor,
            ),
            padding: EdgeInsets.all(35.w),
            child: CustomText(
              '${widget.counter}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
