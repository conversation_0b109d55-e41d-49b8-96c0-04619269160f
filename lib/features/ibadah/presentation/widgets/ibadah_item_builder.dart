import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/ibadah/presentation/widgets/ibadah_chart_percentage.dart';
import 'package:salawati/features/ibadah/presentation/widgets/ibadah_circular_percentage.dart';

class IbadahItemBuilder extends StatefulWidget {
  const IbadahItemBuilder({
    super.key,
    required this.counter,
    required this.color,
    required this.total,
    required this.title,
    required this.subtitle,
  });
  final String title;
  final String subtitle;
  final int counter;
  final Color color;
  final int total;

  @override
  State<IbadahItemBuilder> createState() => _IbadahItemBuilderState();
}

class _IbadahItemBuilderState extends State<IbadahItemBuilder> {
  bool isExpanded = false;
  int pageIndex = 0;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: SmoothEdgesContainer(
        borderRadius: BorderRadius.circular(40.r),
        decoration: BoxDecoration(
          color: AppColor.kRectangleColor,
          border: Border.all(
            width: 1.5,
            color: AppColor.kRectangleColor,
          ),
        ),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  height: 16.h,
                  width: 16.h,
                  padding: EdgeInsets.only(bottom: 4.h),
                  child: CircleAvatar(
                    backgroundColor: widget.color,
                  ),
                ),
                8.horizontalSpace,
                Expanded(
                  child: FittedBox(
                    alignment: AlignmentDirectional.centerStart,
                    fit: BoxFit.scaleDown,
                    child: CustomText(
                      widget.title,
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                CustomText(
                  '${widget.total} - ${widget.counter}',
                  style: TextStyle(
                    color: AppColor.kWhiteColor.withOpacity(0.6),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.h),
              child: CustomText(
                widget.subtitle,
                style: TextStyle(
                  color: AppColor.kWhiteColor.withOpacity(0.6),
                ),
              ),
            ),
            20.verticalSpace,
            if (isExpanded) ...[
              Column(
                children: [
                  SizedBox(
                    height: 0.4.sh,
                    child: PageView(
                      controller: PageController(initialPage: pageIndex),
                      onPageChanged: (value) => setState(() {
                        pageIndex = value;
                      }),
                      children: [
                        IbadahCircularPercentage(widget: widget),
                        IbadahChartPercentage(widget: widget),
                      ],
                    ),
                  ),
                  16.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircleAvatar(
                        backgroundColor: pageIndex == 0
                            ? AppColor.kWhiteColor
                            : AppColor.kRectangleColor,
                        radius: 4.r,
                      ),
                      8.horizontalSpace,
                      CircleAvatar(
                        backgroundColor: pageIndex == 1
                            ? AppColor.kWhiteColor
                            : AppColor.kRectangleColor,
                        radius: 4.r,
                      ),
                    ],
                  ),
                ],
              ),
            ] else ...[
              // todo: amin
              // Center(child: IbadahLinearPercentage(widget: widget)),
            ],
          ],
        ),
      ),
    );
  }
}
