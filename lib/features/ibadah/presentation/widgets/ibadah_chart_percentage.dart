import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/widgets/custom_animated_dropdown.dart';
import 'package:salawati/core/widgets/custom_button.dart';
import 'package:salawati/features/ibadah/presentation/widgets/ibadah_item_builder.dart';

import 'package:syncfusion_flutter_charts/charts.dart';

class IbadahChartPercentage extends StatelessWidget {
  const IbadahChartPercentage({
    super.key,
    required this.widget,
  });

  final IbadahItemBuilder widget;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 0.3.sh,
          child: SfCartesianChart(
              primaryXAxis: const CategoryAxis(
                labelStyle: TextStyle(color: AppColor.kWhiteColor),
              ),
              primaryYAxis: const NumericAxis(
                labelStyle: TextStyle(color: AppColor.kWhiteColor),
              ),
              palette: [
                widget.color
              ],
              series: <AreaSeries<SalesData, String>>[
                AreaSeries<SalesData, String>(
                    dataSource: <SalesData>[
                      SalesData('Jan', 2),
                      SalesData('Feb', 1),
                      SalesData('Mar', 2.5),
                      SalesData('Apr', 4),
                      SalesData('Jun', 0.05),
                      SalesData('Jul', 0.05),
                    ],
                    xValueMapper: (SalesData sales, _) => sales.year,
                    yValueMapper: (SalesData sales, _) => sales.sales)
              ]),
        ),
        16.verticalSpace,
        SizedBox(
          height: 0.07.sh,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 5,
                child: Container(
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColor.kRectangleColor,
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  child: CustomAnimatedDropdown(
                    hintText: 'Past 7 months'.tr,
                    items: [
                      'Past 7 months'.tr,
                    ],
                  ),
                ),
              ),
              8.horizontalSpace,
              Expanded(
                flex: 3,
                child: SizedBox(
                  height: double.infinity,
                  child: CustomButton(
                    onTap: () {},
                    icon: Icons.share,
                    label: 'Share',
                    fontSize: 15.sp,
                    iconSize: 15.sp,
                    color: AppColor.kOrangeColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class SalesData {
  SalesData(this.year, this.sales);
  final String year;
  final double sales;
}
