import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/features/ibadah/presentation/widgets/ibadah_item_builder.dart';

class IbadahLinearPercentage extends StatelessWidget {
  const IbadahLinearPercentage({
    super.key,
    required this.widget,
  });

  final IbadahItemBuilder widget;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      height: 10.h,
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadiusDirectional.horizontal(
                  start: Radius.circular(15.r),
                  end: widget.total == 1 ? Radius.circular(15.r) : Radius.zero,
                ),
                color: widget.counter > 0
                    ? widget.color
                    : AppColor.kRectangleColor,
              ),
            ),
          ),
          for (int index = 1; index < widget.total - 1; index++) ...[
            Container(
              width: 1,
              color: Theme.of(context).primaryColor,
            ),
            Expanded(
                child: Container(
              color: widget.counter > index
                  ? widget.color
                  : AppColor.kRectangleColor,
            )),
          ],
          if (widget.total > 1) ...[
            Container(
              width: 1,
              color: Theme.of(context).primaryColor,
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadiusDirectional.horizontal(
                    end: Radius.circular(15.r),
                  ),
                  color: widget.counter > widget.total - 1
                      ? widget.color
                      : AppColor.kRectangleColor,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
