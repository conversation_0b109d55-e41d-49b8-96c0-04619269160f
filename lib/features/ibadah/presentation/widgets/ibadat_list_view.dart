import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/widgets/custom_lottie_api.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/features/ibadah/data/models/ibadat_models.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_cubit.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_state.dart';
import 'package:salawati/features/ibadah/presentation/widgets/ibadah_empty_widget.dart';
import 'package:salawati/features/ibadah/presentation/widgets/ibadah_item_builder.dart';

class IbadatListView extends StatelessWidget {
  const IbadatListView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IbadahCubit, IbadahState>(builder: (context, state) {
      if (state is IbadatLoading) {
        return ibadatLoadingList();
      }
      if (state is IbadatFailure) {
        return const CustomLottieApi(lottieApi: LottieApi.error);
      }
      if (BlocProvider.of<IbadahCubit>(context).ibadatModel != null) {
        List<Ibadah> ibadat =
            BlocProvider.of<IbadahCubit>(context).ibadatModel!.ibadat;
        if (ibadat.isEmpty) {
          return const IbadahEmptyWidget();
        }
        return ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) => IbadahItemBuilder(
            title: ibadat[index].ibadahName,
            subtitle:
                '${'Track'.tr} ${ibadat[index].type.tr}, ${ibadat[index].count} ${'time'.trPlural('times', ibadat[index].count).trParams(
              {'type': AppFunctions.ibadahTypeIntoNoun(ibadat[index].type).tr},
            )}',
            color: ibadahColors[index % ibadahColors.length],
            counter: ibadat[index].count! - (index % ibadahColors.length),
            total: ibadat[index].count!,
          ),
          separatorBuilder: (context, index) => 16.verticalSpace,
          itemCount: ibadat.length,
        );
      }
      return ibadatLoadingList();
    });
  }

  ListView ibadatLoadingList() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) => SizedBox(
        height: 110.h,
        child: const CustomShimmer(),
      ),
      separatorBuilder: (context, index) => 16.verticalSpace,
      itemCount: 5,
    );
  }
}

List<Color> ibadahColors = const [
  Color(0xff1DDFBC),
  Color(0xffDF1D6F),
  Color(0xffDFB41D),
  Color(0xff1D76DF),
];
