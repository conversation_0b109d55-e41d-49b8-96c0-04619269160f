import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:salawati/features/quran/data/models/reciters_model.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';

class QuranController extends GetxController {
  static QuranController get instance => Get.find();
  static RxInt isPlaying = (-1).obs;
  //-1 loading
  //1 playing
  //0 not playing
  static AudioPlayer player = AudioPlayer();
  Rx<Reciter?> currentReciter = Rx<Reciter?>(null);
  @override
  onInit() {
    super.onInit();
    player.processingStateStream.listen((state) {
      if (state == ProcessingState.loading) {
        isPlaying.value = -1;
      } else if (state == ProcessingState.ready) {
        isPlaying.value = 1;
      }
    });
  }

  static Future<void> playPreviousNext(context, reciter,
      {required bool isPrevious}) async {
    int index = reciter.moshaf.indexWhere((e) =>
        e ==
        BlocProvider.of<QuranCubit>(context).currentSurahNumber.toString());
    if (index == 0 && isPrevious) {
      index = reciter.moshaf.length - 1;
    } else if (index == reciter.moshaf.length - 1 && !isPrevious) {
      index = 0;
    } else {
      isPrevious ? index-- : index++;
    }
    BlocProvider.of<QuranCubit>(context)
        .changeAudioBoxShown(int.parse(reciter.moshaf[index]));
    String surahURL =
        "${reciter.server}${reciter.moshaf[index].toString().padLeft(3, '0')}.mp3";
    await player.stop();
    await player.setUrl(surahURL);
    await player.play();
    await player.stop();
    isPlaying.value = 0;
  }

  static Future<void> seekPreviousNext({required bool isPrevious}) async {
    await player
        .seek(player.position + Duration(seconds: (isPrevious ? -5 : 5)));
  }

  static void playQuran(
      BuildContext context, Reciter reciter, String surahNumber) async {
    BlocProvider.of<QuranCubit>(context)
        .changeAudioBoxShown(int.parse(surahNumber));
    String surahURL =
        "${reciter.server}${surahNumber.toString().padLeft(3, '0')}.mp3";
    await player.stop();
    await player.setUrl(surahURL);
    await player.play();
    await player.stop();
    isPlaying.value = 0;
  }

  static Future<void> changePausePlay() async {
    if (isPlaying.value == 1) {
      isPlaying.value = 0;
      await player.pause();
    } else {
      isPlaying.value = 1;
      await player.play();
    }
  }

  Future<void> stop() async {
    isPlaying.value = 0;
    await player.stop();
  }
}
