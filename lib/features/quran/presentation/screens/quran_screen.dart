// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/quran/data/models/reciters_model.dart';
import 'package:salawati/features/quran/presentation/controller/quran_controller.dart';
import 'package:salawati/features/quran/presentation/screens/reciters_screen.dart';
import 'package:salawati/features/quran/presentation/widgets/quran_audio_box.dart';
import 'package:salawati/features/quran/presentation/widgets/quran_list_view.dart';

var listHorizontalPadding = 22;

class QuranScreen extends StatelessWidget {
  const QuranScreen({super.key, required this.reciter});
  final Reciter reciter;
  @override
  Widget build(BuildContext context) {
    Get.put(QuranController());
    QuranController.instance.currentReciter.value = reciter;
    return WillPopScope(
      onWillPop: () {
        Get.find<LayoutController>().changeScreenLayout(const RecitersScreen());
        return Future(() => false);
      },
      child: Scaffold(
        body: Stack(
          alignment: Alignment.center,
          children: [
            Image.asset(
              AppImages.kBg,
              fit: BoxFit.fitWidth,
              width: double.infinity,
            ),
            Stack(
              children: [
                SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: listHorizontalPadding.w, vertical: 0.1.sh),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SettingsShareRow(),
                        16.verticalSpace,
                        InkWell(
                          onTap: () => Get.find<LayoutController>()
                              .changeScreenLayout(const RecitersScreen()),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              children: [
                                const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: CustomHorizontalArrow(
                                    isBack: true,
                                    color: AppColor.kOrangeColor,
                                  ),
                                ),
                                CustomText(
                                  'All Reciters',
                                  style: TextStyle(
                                    fontSize: 21.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        CustomText(
                          reciter.name,
                          style: TextStyle(
                              fontSize: 17.sp,
                              color: AppColor.kWhiteColor.withOpacity(0.6)),
                        ),
                        QuranListView(reciter: reciter),
                        .3.sh.verticalSpace,
                      ],
                    ),
                  ),
                ),
                QuranAudioBox(reciter: reciter),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
