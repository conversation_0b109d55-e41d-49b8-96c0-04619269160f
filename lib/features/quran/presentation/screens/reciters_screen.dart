// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/quran/presentation/controller/quran_controller.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/features/quran/presentation/widgets/reciters_grid_view.dart';

class RecitersScreen extends StatelessWidget {
  const RecitersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(QuranController());
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.1.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SettingsShareRow(),
                    16.verticalSpace,
                    CustomText(
                      'Al Quran kareem',
                      style: TextStyle(fontSize: 20.sp),
                    ),
                    CustomText(
                      'All Reciters',
                      style: TextStyle(
                          fontSize: 17.sp,
                          color: AppColor.kWhiteColor.withOpacity(0.6)),
                    ),
                    CustomTextField(
                      hintText: 'Reciter name ...'.tr,
                      prefixIconData: Icon(
                        Icons.search,
                        color: AppColor.kWhiteColor.withOpacity(0.6),
                        size: 20.h,
                      ),
                      onChanged: (p0) {
                        BlocProvider.of<QuranCubit>(context).searchReciters(p0);
                      },
                    ),
                    const RecitersGridView(),
                    0.15.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
