import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_lottie_api.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/quran/data/models/reciters_model.dart';
import 'package:salawati/features/quran/presentation/controller/quran_controller.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_state.dart';
import 'package:salawati/features/quran/presentation/screens/quran_screen.dart';

class RecitersGridView extends StatelessWidget {
  const RecitersGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QuranCubit, QuranState>(builder: (context, state) {
      if (state is RecitersLoading) {
        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 15,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 8.w,
            mainAxisSpacing: 8.w,
          ),
          itemBuilder: (BuildContext context, int index) {
            return SmoothEdgesContainer(
              borderRadius: BorderRadius.circular(40.r),
              color: AppColor.kRectangleColor,
              height: 90.h,
              width: 100.h,
              child: const CustomShimmer(),
            );
          },
        );
      }
      if (state is RecitersFailure) {
        return Padding(
            padding: EdgeInsets.only(top: 0.2.sh),
            child: const Center(
                child: CustomLottieApi(lottieApi: LottieApi.error)));
      }
      List<Reciter> reciters =
          BlocProvider.of<QuranCubit>(context).recitersSearch!;
      reciters.sort((a, b) => a.name.compareTo(b.name));
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: reciters.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8.w,
          mainAxisSpacing: 8.w,
        ),
        itemBuilder: (BuildContext context, int index) {
          return InkWell(
            onTap: () {
              Get.find<LayoutController>().changeScreenLayout(
                QuranScreen(
                  reciter: reciters[index],
                ),
              );
              BlocProvider.of<QuranCubit>(context).searchReciters('');
              BlocProvider.of<QuranCubit>(context).changeAudioBoxShown(-1);
              QuranController.instance.stop();
            },
            child: SizedBox(
              height: 90.h,
              width: 100.h,
              child: SmoothEdgesContainer(
                borderRadius: BorderRadius.circular(40.r),
                color: AppColor.kRectangleColor,
                padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 14.w),
                child: Center(
                  child: CustomText(
                    reciters[index].name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ),
            ),
          );
        },
      );
    });
  }
}
