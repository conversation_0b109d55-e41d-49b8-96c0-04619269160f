// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_locale.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/quran/data/models/reciters_model.dart';
import 'package:salawati/features/quran/data/models/surah_model.dart';
import 'package:salawati/features/quran/presentation/controller/quran_controller.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_state.dart';
import 'package:salawati/features/quran/presentation/screens/quran_screen.dart';

class QuranAudioBox extends StatelessWidget {
  const QuranAudioBox({super.key, required this.reciter});
  final Reciter reciter;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QuranCubit, QuranState>(builder: (context, state) {
      QuranCubit quranCubit = BlocProvider.of<QuranCubit>(context);
      if (quranCubit.currentSurahNumber != -1) {
        return Positioned(
          bottom: LayoutController.floatingButtonRaduis * 4 + 8.h,
          left: listHorizontalPadding + 8.w,
          right: listHorizontalPadding + 8.w,
          child: SmoothEdgesContainer(
            borderRadius: BorderRadius.circular(60.r),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(
                            AppLocale().langCode != 'ar'
                                ? "${surahMap[quranCubit.currentSurahNumber]!.englishName} ${surahMap[quranCubit.currentSurahNumber]!.arabicName}"
                                : surahMap[quranCubit.currentSurahNumber]!
                                    .arabicName,
                            style: TextStyle(fontSize: 13.sp),
                          ),
                          8.verticalSpace,
                          CustomText(
                            reciter.name,
                            style: TextStyle(fontSize: 13.sp),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        quranCubit.closeAudioBox();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: SvgPicture.asset(AppSvgs.kClose),
                      ),
                    ),
                  ],
                ),
                8.verticalSpace,
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Center(
                    child: FittedBox(
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          InkWell(
                            onTap: () async {
                              await QuranController.playPreviousNext(
                                  context, reciter,
                                  isPrevious: true);
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: SvgPicture.asset(AppSvgs.kMp3Previous),
                            ),
                          ),
                          InkWell(
                            onTap: () async {
                              await QuranController.seekPreviousNext(
                                  isPrevious: true);
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: SvgPicture.asset(AppSvgs.kMp3Backward),
                            ),
                          ),
                          InkWell(
                            onTap: () async {
                              await QuranController.changePausePlay();
                            },
                            child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Obx(() {
                                  if (QuranController.isPlaying.value == -1) {
                                    return SizedBox(
                                      height: 25.h,
                                      width: 25.h,
                                      child: CircularProgressIndicator(
                                        color: AppColor.kWhiteColor
                                            .withOpacity(0.6),
                                        strokeWidth: 4,
                                      ),
                                    );
                                  }
                                  return SvgPicture.asset(
                                    QuranController.isPlaying.value == 1
                                        ? AppSvgs.kMp3Pause
                                        : AppSvgs.kMp3Play,
                                    height: 25.h,
                                    color:
                                        AppColor.kWhiteColor.withOpacity(0.7),
                                  );
                                })),
                          ),
                          InkWell(
                            onTap: () async {
                              await QuranController.seekPreviousNext(
                                  isPrevious: false);
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: SvgPicture.asset(AppSvgs.kMp3Forward),
                            ),
                          ),
                          InkWell(
                            onTap: () async {
                              await QuranController.playPreviousNext(
                                  context, reciter,
                                  isPrevious: false);
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: SvgPicture.asset(AppSvgs.kMp3Next),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
      return const SizedBox();
    });
  }
}
