import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_locale.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/quran/data/models/reciters_model.dart';
import 'package:salawati/features/quran/data/models/surah_model.dart';
import 'package:salawati/features/quran/presentation/controller/quran_controller.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_state.dart';

class QuranListView extends StatefulWidget {
  const QuranListView({
    super.key,
    required this.reciter,
  });

  final Reciter reciter;

  @override
  State<QuranListView> createState() => _QuranListViewState();
}

class _QuranListViewState extends State<QuranListView> {
  List<String> moshaf = [];
  @override
  void initState() {
    super.initState();
    moshaf = widget.reciter.moshaf;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomTextField(
          hintText: 'Surah ...'.tr,
          prefixIconData: Icon(
            Icons.search,
            color: AppColor.kWhiteColor.withOpacity(0.6),
            size: 20.h,
          ),
          onChanged: (p0) => searchReciters(p0),
        ),
        SmoothEdgesContainer(
          borderRadius: BorderRadius.circular(60.r),
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: AppColor.kRectangleColor,
          ),
          child: ListView.separated(
            itemCount: moshaf.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (context, index) => 8.verticalSpace,
            itemBuilder: (context, index) => InkWell(
              onTap: () => QuranController.playQuran(
                  context, widget.reciter, moshaf[index]),
              child: GetBuilder<AppLocale>(builder: (controller) {
                return BlocBuilder<QuranCubit, QuranState>(
                    builder: (context, state) {
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        '${index + 1}',
                        style: TextStyle(
                            color: AppColor.kWhiteColor.withOpacity(0.6)),
                      ),
                      8.horizontalSpace,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomText(
                              controller.langCode != 'ar'
                                  ? surahMap[int.parse(moshaf[index])]!
                                      .englishName
                                  : surahMap[int.parse(moshaf[index])]!
                                      .arabicName,
                              style: TextStyle(
                                color: BlocProvider.of<QuranCubit>(context)
                                            .currentSurahNumber
                                            .toString() ==
                                        moshaf[index]
                                    ? AppColor.kOrangeColor
                                    : null,
                              ),
                            ),
                            if (controller.langCode != 'ar')
                              CustomText(
                                surahMap[int.parse(moshaf[index])]!.arabicName,
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    color:
                                        AppColor.kWhiteColor.withOpacity(0.6)),
                              ),
                            if (index != moshaf.length - 1)
                              Container(
                                height: 0.4,
                                margin: EdgeInsets.symmetric(vertical: 5.h),
                                color: AppColor.kScaffoldColor,
                              ),
                          ],
                        ),
                      ),
                    ],
                  );
                });
              }),
            ),
          ),
        ),
      ],
    );
  }

  void searchReciters(String title) {
    if (title.isEmpty) {
      moshaf = widget.reciter.moshaf;
    } else {
      moshaf = widget.reciter.moshaf
          .where((e) =>
              surahMap[int.parse(e)]!
                  .arabicName
                  .toString()
                  .toLowerCase()
                  .contains(title) ||
              surahMap[int.parse(e)]!
                  .englishName
                  .toString()
                  .toLowerCase()
                  .contains(title))
          .toList();
    }
    setState(() {});
  }
}
