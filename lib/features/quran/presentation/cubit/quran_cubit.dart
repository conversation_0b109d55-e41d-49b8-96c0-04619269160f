import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:salawati/features/quran/data/models/reciters_model.dart';
import 'package:salawati/features/quran/data/repo/quran_repo.dart';
import 'package:salawati/features/quran/presentation/controller/quran_controller.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_state.dart';
import '../../../../core/data/data_state.dart';

class QuranCubit extends Cubit<QuranState> {
  QuranCubit() : super(QuranInitial());

  RecitersModel? recitersModel;
  List<Reciter>? recitersSearch;
  Future<void> getReciters() async {
    emit(RecitersLoading());
    final dataState = await QuranRepo.getReciters();
    if (dataState is DataSuccess) {
      recitersModel = dataState.data;
      recitersSearch = recitersModel!.reciters;
      emit(RecitersSuccess());
    }
    if (dataState is DataFailed) {
      emit(RecitersFailure(dataState.error!.statusMessage.toString()));
    }
  }

  void searchReciters(String title) {
    if (title.isEmpty) {
      recitersSearch = recitersModel!.reciters;
    } else {
      recitersSearch = recitersModel!.reciters.where((e) {
        return e.name.toString().toLowerCase().contains(title);
      }).toList();
    }
    emit(ChangeCurrentSurahState());
  }

  int currentSurahNumber = -1;
  void changeAudioBoxShown(int number) {
    currentSurahNumber = number;
    emit(ChangeCurrentSurahState());
  }

  void closeAudioBox() {
    currentSurahNumber = -1;
    QuranController.instance.stop();
    emit(ChangeCurrentSurahState());
  }
}
