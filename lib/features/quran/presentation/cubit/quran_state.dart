abstract class QuranState {}

class Quran<PERSON>ni<PERSON> extends QuranState {}

class RecitersLoading extends QuranState {}

class Recite<PERSON><PERSON><PERSON><PERSON> extends QuranState {}

class RecitersFailure extends QuranState {
  final String error;

  RecitersFailure(this.error);
}

class QuranLoading extends QuranState {}

class <PERSON><PERSON><PERSON><PERSON> extends Quran<PERSON><PERSON> {}

class QuranFailure extends QuranState {
  final String error;

  QuranFailure(this.error);
}

class ChangeCurrentSurahState extends QuranState {}
