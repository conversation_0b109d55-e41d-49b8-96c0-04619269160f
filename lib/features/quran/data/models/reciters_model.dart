class RecitersModel {
  List<Reciter> reciters;
  RecitersModel({required this.reciters});

  factory RecitersModel.fromJson(Map<String, dynamic> map) {
    if (map['reciters'] != null && map['reciters'] is List) {
      return RecitersModel(
        reciters: map['reciters']
            .map((x) => Reciter.fromJson(x))
            .toList()
            .cast<Reciter>(),
      );
    }
    return RecitersModel(reciters: []);
  }
}

class Reciter {
  final int id;
  final String name;
  final String letter;
  final String server;
  List<String> moshaf;

  Reciter(
      {required this.id,
      required this.name,
      required this.letter,
      required this.server,
      required this.moshaf});

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'letter': letter,
      'server': server,
      'moshaf': moshaf,
    };
  }

  static Reciter fromJson(Map<String, dynamic> json) {
    return Reciter(
      id: json['id'],
      name: json['name'],
      letter: json['letter'],
      server: json['moshaf'] != null && json['moshaf'] is List
          ? json['moshaf'][0]['server']
          : "",
      moshaf: json['moshaf'] != null && json['moshaf'] is List
          ? json['moshaf'][0]['surah_list'].toString().split(',').cast<String>()
          : [],
    );
  }
}
