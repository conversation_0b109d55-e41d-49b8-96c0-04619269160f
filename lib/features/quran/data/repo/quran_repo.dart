import 'package:salawati/core/data/data_service.dart';
import 'package:salawati/core/data/data_state.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/quran/data/models/reciters_model.dart';

class QuranRepo {
  static Future<DataState> getReciters() async {
    String language = (cacheMemory.read('lang') ?? 'ar');
    final response = await DataService.get(
        url: 'https://www.mp3quran.net/api/v3/reciters',
        queryParameters: {
          'language': language == 'en' ? 'eng' : language,
        });
    return DataService.dataRepoRequest(response: response, fromJson: RecitersModel.fromJson);
  }
}
