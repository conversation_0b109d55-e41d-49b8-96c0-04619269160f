import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/features/hijri_calander/data/comparable_hijri_date_time.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:syncfusion_flutter_core/core.dart';

class HijriController extends GetxController {
  @override
  onInit() {
    super.onInit();
    loadEvents();
  }

  List<String>? soonEvents;
  List<ComparableHijriDateTime> eventsDates = [];

  Future<void> loadEvents() async {
    Map<String, dynamic> events = {};
    String jsonString = await rootBundle.loadString(AppJsons.kEventsData);
    events = json.decode(jsonString);
    for (String event in events.keys) {
      HijriDateTime eventDate = HijriDateTime(
        int.parse(event.split('-')[2]),
        int.parse(event.split('-')[1]),
        int.parse(event.split('-')[0]),
      );
      if (!eventDate.isBefore(HijriDateTime.now().add(
        Duration(days: SettingsController.instance.dateCorrectionValue.value),
      ))) {
        eventsDates.add(ComparableHijriDateTime(eventDate));
      }
    }
    eventsDates.sort();
    String selectedDate =
        '${eventsDates.first.hijriDateTime.day}-${eventsDates.first.hijriDateTime.month}-${eventsDates.first.hijriDateTime.year}';
    soonEvents = [];
    for (Map map in events[selectedDate]) {
      soonEvents!.add(map['text']);
    }
    update();
  }
}
