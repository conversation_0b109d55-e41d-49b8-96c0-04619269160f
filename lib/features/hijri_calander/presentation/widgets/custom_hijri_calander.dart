import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/libraries/syncfusion_flutter_datepicker/datepicker.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/hijri_calander/presentation/widgets/date_dialog.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:syncfusion_flutter_core/core.dart';

class CustomHijriCalander extends StatelessWidget {
  final bool isHijri;

  const CustomHijriCalander({super.key, required this.isHijri});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 6.w, right: 6.w),
      child: Container(
        padding: EdgeInsets.only(left: 6.w, right: 6.w, top: 24.w),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          boxShadow: [
            BoxShadow(
              blurRadius: 15,
              spreadRadius: 15,
              color: AppColor.kWhiteColor.withOpacity(0.03),
            )
          ],
          borderRadius: BorderRadius.circular(21.r),
        ),
        child: Center(
          child: Directionality(
            textDirection: cacheMemory.read('lang') == 'ar'
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: isHijri
                ? SfHijriDateRangePicker(
                    onSelectionChanged:
                        (DateRangePickerSelectionChangedArgs args) {
                      List<String> selectedDate =
                          args.value.toString().split('-');

                      HijriDateTime selectedHijryDateTime = HijriDateTime(
                          int.parse(selectedDate[0]),
                          int.parse(selectedDate[1]),
                          int.parse(
                            selectedDate[2],
                          ));
                      Get.dialog(
                        DateDialog(
                          date: selectedHijryDateTime,
                        ),
                      );
                    },
                    initialSelectedDate: HijriDateTime.now().add(
                      Duration(
                          days: SettingsController
                              .instance.dateCorrectionValue.value),
                    ),
                    backgroundColor: Colors.transparent,
                    view: HijriDatePickerView.month,
                    showNavigationArrow: true,
                    headerStyle: DateRangePickerHeaderStyle(
                      backgroundColor: Colors.transparent,
                      textStyle: TextStyle(
                        color: AppColor.kWhiteColor,
                        fontSize: 10.sp,
                      ),
                    ),
                    monthCellStyle: HijriDatePickerMonthCellStyle(
                      textStyle: TextStyle(
                        color: AppColor.kWhiteColor,
                        fontSize: 15.sp,
                      ),
                      todayCellDecoration: BoxDecoration(
                        color: Colors.grey.shade600,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      todayTextStyle: TextStyle(
                        color: AppColor.kWhiteColor,
                        fontSize: 15.sp,
                      ),
                      weekendTextStyle: TextStyle(
                        color: AppColor.kOrangeColor,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    monthViewSettings: const HijriDatePickerMonthViewSettings(
                      firstDayOfWeek: 6,
                      dayFormat: 'E',
                      weekendDays: [5],
                    ),
                    selectionColor: AppColor.kOrangeColor,
                    selectionShape: DateRangePickerSelectionShape.rectangle,
                    selectionTextStyle: TextStyle(
                      color: AppColor.kWhiteColor,
                      fontSize: 15.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    headerHeight: 50,
                    allowViewNavigation: false,
                  )
                : SfDateRangePicker(
                    onSelectionChanged:
                        (DateRangePickerSelectionChangedArgs args) {
                      String dateString = args.value.toString();

                      // Split at space and take only the date part
                      List<String> selectedDateParts =
                          dateString.split(' ')[0].split('-');

                      DateTime selectedDateTime = DateTime(
                        int.parse(selectedDateParts[0]),
                        int.parse(selectedDateParts[1]),
                        int.parse(selectedDateParts[2]),
                      );

                      Get.dialog(
                        CustomDateDialog(
                          date: selectedDateTime,
                        ),
                      );
                    },
                    initialSelectedDate: DateTime.now().add(
                      Duration(
                          days: SettingsController
                              .instance.dateCorrectionValue.value),
                    ),
                    backgroundColor: Colors.transparent,
                    view: DateRangePickerView.month,
                    showNavigationArrow: true,
                    headerStyle: DateRangePickerHeaderStyle(
                      backgroundColor: Colors.transparent,
                      textStyle: TextStyle(
                        color: AppColor.kWhiteColor,
                        fontSize: 10.sp,
                      ),
                    ),
                    monthCellStyle: DateRangePickerMonthCellStyle(
                      textStyle: TextStyle(
                        color: AppColor.kWhiteColor,
                        fontSize: 15.sp,
                      ),
                      todayCellDecoration: BoxDecoration(
                        color: Colors.grey.shade600,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      todayTextStyle: TextStyle(
                        color: AppColor.kWhiteColor,
                        fontSize: 15.sp,
                      ),
                      weekendTextStyle: TextStyle(
                        color: AppColor.kOrangeColor,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    cellBuilder: (BuildContext context,
                        DateRangePickerCellDetails details) {
                      DateTime date = details.date;

                      if (date.month != DateTime.now().month) {
                        return Container(); // Return an empty container for other months
                      }

                      bool isWeekend =
                          [5].contains(date.weekday); // Friday is weekend day

                      return Container(
                        alignment: Alignment.center,
                        margin: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: isWeekend
                                  ? AppColor.kOrangeColor
                                  : Colors.transparent,
                              width: 1),
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: Text(
                          '${date.day}',
                          style: TextStyle(
                            color: isWeekend
                                ? AppColor.kOrangeColor
                                : AppColor.kWhiteColor,
                            fontSize: 15.sp,
                            fontWeight:
                                isWeekend ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      );
                    },
                    monthViewSettings: const DateRangePickerMonthViewSettings(
                      firstDayOfWeek: 6,
                      dayFormat: 'E',
                      weekendDays: [5],
                    ),
                    selectionColor: AppColor.kOrangeColor,
                    selectionShape: DateRangePickerSelectionShape.rectangle,
                    selectionTextStyle: TextStyle(
                      color: AppColor.kWhiteColor,
                      fontSize: 15.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    headerHeight: 50,
                    allowViewNavigation: false,
                  ),
          ),
        ),
      ),
    );
  }
}

class HijriCalendarStateManagement extends GetxController {
  RxBool isHijri = true.obs;

  void toggleIsHijri() async {
    isHijri.value = !isHijri.value;
    await Future.delayed(const Duration(milliseconds: 100));
    update();
    // debugPrint('Updated isHijri: ${isHijri.value}'); // Add this line
  }
}
