import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_date.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/prayer/presentation/screens/prayer_screen.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:syncfusion_flutter_core/core.dart';

class DateDialog extends StatelessWidget {
  const DateDialog({super.key, required this.date});
  final HijriDateTime date;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: const EdgeInsets.all(16),
      backgroundColor: Theme.of(context).primaryColor,
      content: IntrinsicHeight(
        child: SmoothEdgesContainer(
          borderRadius: BorderRadius.circular(60),
          padding: const EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Date:'),
              8.verticalSpace,
              CustomDate(
                  isHijry: false, withDay: false, dateTime: date.toDateTime()),
              16.verticalSpace,
              CustomText('Hijri Date:'),
              8.verticalSpace,
              CustomDate(isHijry: true, dateTime: date.toDateTime()),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            Get.find<PrayerController>().changePrayerDatePage(
              date
                  .difference(HijriDateTime.now().add(
                    Duration(
                        days: SettingsController
                            .instance.dateCorrectionValue.value),
                  ))
                  .inDays,
            );
            Get.find<LayoutController>()
                .changeScreenLayout(const PrayerScreen(), index: 0);
            Get.back();
          },
          icon: CustomText('Go to Prayer Time'),
        ),
      ],
    );
  }
}

class CustomDateDialog extends StatelessWidget {
  const CustomDateDialog({super.key, required this.date});
  final DateTime date;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: const EdgeInsets.all(16),
      backgroundColor: Theme.of(context).primaryColor,
      content: IntrinsicHeight(
        child: SmoothEdgesContainer(
          borderRadius: BorderRadius.circular(60),
          padding: const EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Date:'),
              8.verticalSpace,
              CustomDate(isHijry: false, withDay: false, dateTime: date),
              16.verticalSpace,
              CustomText('Hijri Date:'),
              8.verticalSpace,
              CustomDate(isHijry: true, dateTime: date),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            Get.find<PrayerController>().changePrayerDatePage(
              date.difference(DateTime.now()).inDays,
            );
            Get.find<LayoutController>()
                .changeScreenLayout(const PrayerScreen(), index: 0);
            Get.back();
          },
          icon: CustomText('Go to Prayer Time'),
        ),
      ],
    );
  }
}
