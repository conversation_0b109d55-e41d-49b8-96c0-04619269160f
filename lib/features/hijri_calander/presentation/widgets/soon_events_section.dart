import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_date.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/hijri_calander/presentation/controller/hijri_controller.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

class SoonEventsSection extends StatelessWidget {
  const SoonEventsSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HijriController>(builder: (controller) {
      if (controller.soonEvents == null) {
        return Column(children: [
          SizedBox(
            height: 30,
            width: 0.5.sw,
            child: const CustomShimmer(),
          ),
          16.verticalSpace,
          SmoothEdgesContainer(
            borderRadius: BorderRadius.circular(60.r),
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              color: AppColor.kRectangleColor,
            ),
            height: 200,
            child: const CustomShimmer(),
          ),
        ]);
      }
      if (controller.soonEvents!.isNotEmpty) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              'Up coming events',
              style: TextStyle(
                fontSize: 22.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            16.verticalSpace,
            ListView.separated(
              itemBuilder: (context, index) => SmoothEdgesContainer(
                borderRadius: BorderRadius.circular(60.r),
                padding: EdgeInsets.all(24.w),
                decoration: BoxDecoration(
                  color: AppColor.kRectangleColor,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(() {
                      final controller = SettingsController.instance;
                      return Align(
                        alignment: AlignmentDirectional.bottomEnd,
                        child: SvgPicture.asset(
                          AppSvgs.kNotification,
                          // ignore: deprecated_member_use
                          color: controller.isEventsNotificationOn.value
                              ? AppColor.kOrangeColor
                              : AppColor.kWhiteColor.withOpacity(0.7),
                        ),
                      );
                    }),
                    CustomDate(
                        isHijry: true,
                        dateTime: controller.eventsDates.first.hijriDateTime
                            .toDateTime()),
                    CustomText(controller.soonEvents![index]),
                  ],
                ),
              ),
              separatorBuilder: (context, index) => 16.verticalSpace,
              itemCount: controller.soonEvents!.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
            ),
          ],
        );
      }
      return const SizedBox();
    });
  }
}
