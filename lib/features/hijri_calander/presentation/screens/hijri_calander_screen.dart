import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:hijri_calendar/hijri_calendar.dart';
import 'package:intl/intl.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_date.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/hijri_calander/presentation/widgets/custom_hijri_calander.dart';

/// Controller for managing Gregorian and Hijri dates with GetX
class DateConverterController extends GetxController {
  final selectedGregorian = DateTime.now().obs;
  final selectedHijri = Rx<HijriCalendarConfig>(
    HijriCalendarConfig.fromGregorian(DateTime.now()),
  );
  Timer? _debounce;

  @override
  void onInit() {
    super.onInit();

    HijriCalendarConfig.language = Get.locale?.languageCode ?? "ar";
    selectedHijri.value =
        HijriCalendarConfig.fromGregorian(selectedGregorian.value);
  }

  /// Update Hijri from Gregorian (synchronous UI update + debounce heavy conversion)
  void updateHijri(DateTime date) {
    selectedGregorian.value = date;
    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      final safeYear = date.year.clamp(1937, 2076);
      final safe = DateTime(safeYear, date.month, date.day);
      try {
        selectedHijri.value = HijriCalendarConfig.fromGregorian(safe);
      } catch (_) {
        selectedHijri.value = HijriCalendarConfig.fromGregorian(DateTime.now());
      }
    });
  }

  /// Update Gregorian from Hijri (synchronous UI update + debounce conversion)
  void updateGregorian(int year, int month, int day) {
    final hy = year.clamp(1, 1500);
    try {
      selectedHijri.value = HijriCalendarConfig.fromHijri(hy, month, day);
    } catch (_) {
      selectedHijri.value = HijriCalendarConfig.fromGregorian(DateTime.now());
    }
    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      try {
        selectedGregorian.value =
            HijriCalendarConfig().hijriToGregorian(hy, month, day);
      } catch (_) {
        selectedGregorian.value = DateTime.now();
      }
    });
  }

  /// Reset both pickers to current date
  void reset() {
    final now = DateTime.now();
    selectedGregorian.value = now;
    selectedHijri.value = HijriCalendarConfig.fromGregorian(now);
  }
}

/// Main page displaying date converter
class DateConverterPage extends StatelessWidget {
  const DateConverterPage({super.key});

  // Hijri month names (for display only)
  static const _hijriMonths = [
    'محرم',
    'صفر',
    'ربيع الأول',
    'ربيع الآخر',
    'جمادى الأولى',
    'جمادى الآخرة',
    'رجب',
    'شعبان',
    'رمضان',
    'شوال',
    'ذو القعدة',
    'ذو الحجة',
  ];

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DateConverterController());
    var language = cacheMemory.read('lang') ?? 'ar';
    const minG = 1937, maxG = 2076;
    const minH = 1, maxH = 1500;
    final hController = Get.put(HijriCalendarStateManagement());
    // var language = NumberLocalization.getDeviceLocale();

    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Positioned.fill(
            child: Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: 14.w, vertical: 0.05.sh),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SettingsShareRow(),
                      IconButton(
                        onPressed: controller.reset,
                        icon: const Icon(
                          Icons.restore_rounded,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  16.verticalSpace,
                  DefaultTabController(
                    length: 2,
                    child: SmoothEdgesContainer(
                      borderRadius: BorderRadius.circular(30.r),
                      color: AppColor.kRectangleColor,
                      child: Column(
                        children: [
                          SmoothEdgesContainer(
                            borderRadius: BorderRadius.circular(30.r),
                            color: AppColor.kRectangleColor,
                            child: TabBar(
                              tabs: [
                                Tab(child: CustomText('Convert Date'.tr)),
                                Tab(child: CustomText('Islamic Calendar'.tr)),
                              ],
                              indicatorColor: AppColor.kDarkBlueColor,
                            ),
                          ),
                          SizedBox(
                            height: 500,
                            child: TabBarView(
                              children: [
                                // Conversion tab
                                SingleChildScrollView(
                                  child: Obx(() {
                                    final gDate =
                                        controller.selectedGregorian.value;
                                    final hDate =
                                        controller.selectedHijri.value;

                                    // Build safe lists and clamp indices:
                                    final years = [
                                      for (int y = minG; y <= maxG; y++) y
                                    ];
                                    final yearIndex = years
                                        .indexOf(gDate.year)
                                        .clamp(0, years.length - 1);
                                    final months =
                                        List.generate(12, (i) => i + 1);
                                    final monthIndex =
                                        (gDate.month - 1).clamp(0, 11);
                                    final daysCount = DateUtils.getDaysInMonth(
                                        gDate.year, gDate.month);
                                    final days =
                                        List.generate(daysCount, (i) => i + 1);
                                    final dayIndex =
                                        (gDate.day - 1).clamp(0, daysCount - 1);

                                    final hijriStr = '%s %s %s %s AH'.trArgs([
                                      hDate.getDayName().tr,
                                      hDate.hDay
                                          .toString(), // Also safe to change this
                                      hDate.getLongMonthName().tr,
                                      hDate.hYear
                                          .toString(), // <-- FIX APPLIED HERE
                                    ]);

                                    return Padding(
                                      padding: EdgeInsets.all(16.w),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          // Gregorian display
                                          CustomText(
                                            DateFormat(
                                                    'EEEE d MMMM y', language)
                                                .format(gDate),
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          8.verticalSpace,

                                          // Gregorian pickers
                                          _buildPicker(
                                            years: years,
                                            months: months,
                                            days: days,
                                            yearIndex: yearIndex,
                                            monthIndex: monthIndex,
                                            dayIndex: dayIndex,
                                            onSelected: (y, m, d) => controller
                                                .updateHijri(DateTime(y, m, d)),
                                            displayYear: (y) => Center(
                                              child: CustomText(
                                                  NumberLocalization
                                                      .localizeNumber(y)),
                                            ),
                                            displayMonth: (m) => Center(
                                              child: CustomText(
                                                DateFormat('MMMM', language)
                                                    .format(DateTime(2023, m)),
                                              ),
                                            ),
                                            displayDay: (d) => Center(
                                              child: CustomText(
                                                  NumberLocalization
                                                      .localizeNumber(d)),
                                            ),
                                          ),
                                          16.verticalSpace,

                                          // Hijri display
                                          CustomText(
                                            hijriStr,
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          8.verticalSpace,

                                          // Hijri pickers
                                          _buildPicker(
                                            years: [
                                              for (int y = minH; y <= maxH; y++)
                                                y
                                            ],
                                            months: months,
                                            days: List.generate(
                                              HijriCalendarConfig()
                                                  .getDaysInMonth(hDate.hYear,
                                                      hDate.hMonth),
                                              (i) => i + 1,
                                            ),
                                            yearIndex: [
                                              for (int y = minH; y <= maxH; y++)
                                                y
                                            ]
                                                .indexOf(hDate.hYear)
                                                .clamp(0, maxH - minH),
                                            monthIndex:
                                                (hDate.hMonth - 1).clamp(0, 11),
                                            dayIndex: (hDate.hDay - 1).clamp(
                                                0,
                                                HijriCalendarConfig()
                                                        .getDaysInMonth(
                                                            hDate.hYear,
                                                            hDate.hMonth) -
                                                    1),
                                            onSelected:
                                                controller.updateGregorian,
                                            displayYear: (y) => Center(
                                              child: CustomText(
                                                  '${y.toString()} هـ'), // <-- FIX APPLIED HERE
                                            ),
                                            displayMonth: (m) => Center(
                                              child: CustomText(
                                                  _hijriMonths[m - 1]),
                                            ),
                                            displayDay: (d) => Center(
                                              child: CustomText(d
                                                  .toString()), // Also safe to change this
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                                ),

                                // Calendar tab
                                SingleChildScrollView(
                                  child: Obx(
                                    () => Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        0.02.sh.verticalSpace,
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            TextButton.icon(
                                              onPressed:
                                                  hController.toggleIsHijri,
                                              icon: Icon(
                                                hController.isHijri.value
                                                    ? Icons.calendar_today
                                                    : Icons.date_range,
                                                color: AppColor.kOrangeColor,
                                              ),
                                              label: CustomText(
                                                hController.isHijri.value
                                                    ? 'Conversion to Gregorian calendar'
                                                        .tr
                                                    : 'Conversion to Hijri calendar'
                                                        .tr,
                                                textScaleFactor: 0.9,
                                                maxLines: 2,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 5.h),
                                          child: const Column(
                                            children: [
                                              CustomDate(isHijry: false),
                                              CustomDate(isHijry: true),
                                            ],
                                          ),
                                        ),
                                        16.verticalSpace,
                                        CustomHijriCalander(
                                          isHijri: hController.isHijri.value,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Generic picker builder combining year/month/day in one row
  Widget _buildPicker({
    required List<int> years,
    required List<int> months,
    required List<int> days,
    required int yearIndex,
    required int monthIndex,
    required int dayIndex,
    required void Function(int year, int month, int day) onSelected,
    required Widget Function(int) displayYear,
    required Widget Function(int) displayMonth,
    required Widget Function(int) displayDay,
  }) {
    return SizedBox(
      height: 180,
      child: Row(
        children: [
          SinglePicker<int>(
            items: years,
            initialIndex: yearIndex,
            onChanged: (i) =>
                onSelected(years[i], months[monthIndex], days[dayIndex]),
            builder: (y) => displayYear(y),
          ),
          SinglePicker<int>(
            items: months,
            initialIndex: monthIndex,
            onChanged: (i) =>
                onSelected(years[yearIndex], months[i], days[dayIndex]),
            builder: (m) => displayMonth(m),
          ),
          SinglePicker<int>(
            items: days,
            initialIndex: dayIndex,
            onChanged: (i) =>
                onSelected(years[yearIndex], months[monthIndex], days[i]),
            builder: (d) => displayDay(d),
          ),
        ],
      ),
    );
  }
}

/// State management for the Hijri calendar viewer
class HijriCalendarStateManagement extends GetxController {
  RxBool isHijri = true.obs;

  void toggleIsHijri() async {
    isHijri.value = !isHijri.value;
    await Future.delayed(const Duration(milliseconds: 100));
    update();
  }
}

class SinglePicker<T> extends StatefulWidget {
  final List<T> items;
  final int initialIndex;
  final ValueChanged<int> onChanged;
  final Widget Function(T) builder;

  const SinglePicker({
    super.key,
    required this.items,
    required this.initialIndex,
    required this.onChanged,
    required this.builder,
  });

  @override
  State<SinglePicker<T>> createState() => _SinglePickerState<T>();
}

class _SinglePickerState<T> extends State<SinglePicker<T>> {
  late FixedExtentScrollController _controller;

  @override
  void initState() {
    super.initState();
    _controller = FixedExtentScrollController(initialItem: widget.initialIndex);
    _controller.addListener(_onScroll);
  }

  void _onScroll() {
    widget.onChanged(_controller.selectedItem);
  }

  @override
  void didUpdateWidget(SinglePicker<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialIndex != widget.initialIndex &&
        _controller.selectedItem != widget.initialIndex) {
      _controller.animateToItem(
        widget.initialIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: CupertinoPicker(
        scrollController: _controller,
        magnification: 1.2,
        useMagnifier: true,
        squeeze: 1.1,
        backgroundColor: Colors.transparent,
        itemExtent: 40,
        onSelectedItemChanged: widget.onChanged,
        children: widget.items.map(widget.builder).toList(),
      ),
    );
  }
}
