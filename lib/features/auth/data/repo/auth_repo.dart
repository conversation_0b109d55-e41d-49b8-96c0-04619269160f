import 'package:salawati/core/models/message_model.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/auth/data/models/get_auth_model.dart';

import '../../../../core/data/data_service.dart';
import '../../../../core/data/data_state.dart';

class AuthRepo {
  static Future<DataState> googleLogIn(
      {required Map<String, dynamic> data}) async {
    final response =
        await DataService.post(endPoint: SIGN_IN_WITH_GOOGLE, data: data);
    return DataService.dataRepoRequest(
        response: response, fromJson: GetAuthModel.fromJson);
  }

  static Future<DataState> appleLogIn(
      {required Map<String, dynamic> data}) async {
    final response =
        await DataService.post(endPoint: SIGN_IN_WITH_APPLE, data: data);
    return DataService.dataRepoRequest(
        response: response, fromJson: GetAuthModel.fromJson);
  }

  static Future<DataState> logOut() async {
    final response = await DataService.get(url: baseUrl + LOGOUT_END_POINT);
    return DataService.dataRepoRequest(
        response: response, fromJson: MessageModel.fromJson);
  }
}
