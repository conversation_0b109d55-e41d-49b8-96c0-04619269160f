class GetAuthModel {
  final String message;
  final String token;
  final String userName;
  final String userEmail;
  final String userImage;

  GetAuthModel({
    required this.message,
    required this.token,
    required this.userName,
    required this.userEmail,
    required this.userImage,
  });

  factory GetAuthModel.fromJson(Map<String, dynamic> json) => GetAuthModel(
        message: json["msg"],
        token: json["token"],
        userName: json["user_name"],
        userEmail: json["user_email"],
        userImage: json["user_image"],
      );

  Map<String, dynamic> toJson() {
    return {
      'msg': message,
      'token': token,
      'user_name': userName,
      'user_email': userEmail,
      'user_image': userImage,
    };
  }
}
