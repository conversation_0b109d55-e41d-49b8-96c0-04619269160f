import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/auth/presentation/widgets/auth_button.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class AuthScreen extends StatelessWidget {
  const AuthScreen({super.key, required this.isSettings});

  final bool isSettings;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          alignment: Alignment.center,
          children: [
            const SettingsMainBackground(),
            Container(
              margin: EdgeInsets.only(top: 0.12.sh),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(30.r)),
                color: Theme.of(context).primaryColor,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 20,
                    spreadRadius: 20,
                    color: AppColor.kWhiteColor.withOpacity(0.045),
                  )
                ],
              ),
              child: Align(
                alignment: Alignment.topCenter,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(28.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if (isSettings)
                              CustomText(
                                'Settings',
                                style: TextStyle(
                                  fontSize: 21.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            else ...[
                              const SizedBox(),
                            ],
                            InkWell(
                                onTap: () => Get.back(),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: SvgPicture.asset(AppSvgs.kClose),
                                )),
                          ],
                        ),
                        16.verticalSpace,
                        if (!isSettings) ...[
                          Container(
                            decoration: BoxDecoration(
                              color: AppColor.kRectangleColor,
                              shape: BoxShape.circle,
                            ),
                            padding: EdgeInsets.all(24.w),
                            child: SvgPicture.asset(AppSvgs.kProfile),
                          ),
                          16.verticalSpace,
                          CustomText(
                            'Please login first so you can save and track your watcher',
                            textAlign: TextAlign.center,
                          ),
                        ],
                        28.verticalSpace,
                        AuthButton(isGoogle: false, isSettings: isSettings),
                        8.verticalSpace,
                        AuthButton(isGoogle: true, isSettings: isSettings),
                        0.7.sh.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
