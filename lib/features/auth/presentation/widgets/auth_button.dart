import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_state.dart';
import 'package:salawati/features/splash/presentation/screens/splash_screen.dart';

class AuthButton extends StatelessWidget {
  const AuthButton({
    super.key,
    required this.isGoogle,
    required this.isSettings,
  });
  final bool isGoogle;
  final bool isSettings;

  @override
  Widget build(BuildContext context) {
    if (Platform.isAndroid && !isGoogle) {
      return const SizedBox();
    }
    return BlocConsumer<AuthCubit, AuthState>(
      builder: (context, state) {
        if (state is AuthLoading && state.isGoogle == isGoogle) {
          return SizedBox(
            height: 75.h,
            child: const CustomShimmer(),
          );
        }
        return InkWell(
          onTap: () {
            if (isGoogle) {
              BlocProvider.of<AuthCubit>(context).getTokensThenGoogleLogin();
            } else {
              BlocProvider.of<AuthCubit>(context).getTokensThenAppleLogin();
            }
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(21.r),
              color: AppColor.kWhiteColor,
            ),
            padding: EdgeInsets.symmetric(vertical: 22.h, horizontal: 20.w),
            child: Row(
              children: [
                SvgPicture.asset(
                  isGoogle ? AppSvgs.kGoogle : AppSvgs.kApple,
                  height: 30.h,
                  width: 30.h,
                ),
                16.horizontalSpace,
                Expanded(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    alignment: AlignmentDirectional.centerStart,
                    child: Padding(
                      padding: EdgeInsets.only(top: 8.h),
                      child: CustomText(
                        isGoogle ? 'Sign in with Google' : 'Sign in with Apple',
                        style: TextStyle(
                          color: AppColor.kBlackColor,
                          fontSize: 17.sp,
                        ),
                      ),
                    ),
                  ),
                ),
                16.horizontalSpace,
                CustomHorizontalArrow(
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        );
      },
      listener: (context, state) {
        if (state is AuthSuccess) {
          if (isSettings) {
            Get.toNamed(AppRouter.kProfileScreen);
          } else {
            // todo: amin
            Get.offAll(() => const SplashScreen());
            // Get.offNamed(AppRouter.kAddIbadahScreen);
          }
        }
      },
    );
  }
}
