import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';

import 'package:salawati/features/auth/data/models/get_auth_model.dart';
import 'package:salawati/features/auth/data/models/post_google_athu_model.dart';
import 'package:salawati/features/auth/data/repo/auth_repo.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_state.dart';

import 'package:salawati/features/splash/presentation/screens/splash_screen.dart';

import '../../../../core/data/data_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(AuthInitial());

  Future<void> getTokensThenGoogleLogin() async {
    emit(AuthLoading(isGoogle: true));
    try {
      await AppFunctions.getDeviceToken().then((fcmToken) {
        AppFunctions.getAccessToken().then((accessToken) async {
          PostGoogleAthuModel postGoogleAthuModel =
              PostGoogleAthuModel(accessToken: accessToken, fcmToken: fcmToken);
          await googleLogIn(data: postGoogleAthuModel.toJson());
        });
      });
    } catch (e) {
      debugPrint(e.toString());
      emit(AuthFailure(e.toString()));
    }
  }

  Future<void> getTokensThenAppleLogin() async {
    emit(AuthLoading(isGoogle: false));
    try {
      await AppFunctions.getDeviceToken().then((fcmToken) {
        AppFunctions.getAppleAccessToken().then((userData) async {
          await appleLogIn(data: userData);
          emit(AuthFailure('success'));
        });
      });
    } catch (e) {
      emit(AuthFailure(e.toString()));
    }
  }

  GetAuthModel? getAuthModel;
  Future<void> googleLogIn({required Map<String, dynamic> data}) async {
    final dataState = await AuthRepo.googleLogIn(data: data);
    if (dataState is DataSuccess) {
      getAuthModel = dataState.data;
      emit(AuthSuccess(getAuthModel!.message));
      loginSuccessProcedures(dataState);
    }
    if (dataState is DataFailed) {
      emit(AuthFailure(dataState.error!.statusMessage.toString()));
    }
  }

  Future<void> appleLogIn({required Map<String, dynamic> data}) async {
    final dataState = await AuthRepo.appleLogIn(data: data);
    if (dataState is DataSuccess) {
      getAuthModel = dataState.data;
      emit(AuthSuccess(getAuthModel!.message));
      loginSuccessProcedures(dataState);
    }
    if (dataState is DataFailed) {
      emit(AuthFailure(dataState.error!.statusMessage.toString()));
    }
  }

  void loginSuccessProcedures(DataSuccess<dynamic> dataState) {
    cacheMemory.write(TOKEN, getAuthModel!.token);
    cacheMemory.write(PROFILE, getAuthModel!.toJson());
    AppFunctions.showSuccessMessage(getAuthModel!.message);
    // todo: amin
    Get.offAll(() => const SplashScreen());
  }

  Future<void> logOut() async {
    emit(LogOutLoading());
    final dataState = await AuthRepo.logOut();
    Get.back();
    await cacheMemory.remove(TOKEN);
    await cacheMemory.remove(PROFILE);
    AppFunctions.showSuccessMessage('logOutSuccess'.tr);
    if (dataState is DataSuccess) {
      emit(LogOutSuccess(dataState.data.toString()));
      logoutSuccesProcedures(dataState);
    }
    if (dataState is DataFailed) {
      emit(LogOutFailure(dataState.error!.statusMessage.toString()));
    }
  }

  void logoutSuccesProcedures(DataSuccess<dynamic> dataState) {
    // todo: amin
    Get.offAll(() => const SplashScreen());
  }
}
