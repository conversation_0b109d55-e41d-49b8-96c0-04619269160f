abstract class AuthState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {
  bool isGoogle;
  AuthLoading({required this.isGoogle});
}

class AuthSuccess extends AuthState {
  String message;
  AuthSuccess(this.message);
}

class AuthFailure extends AuthState {
  final String error;
  AuthFailure(this.error);
}

class LogOutLoading extends AuthState {}

class LogOutSuccess extends AuthState {
  final String message;
  LogOutSuccess(this.message);
}

class LogOutFailure extends AuthState {
  final String error;
  LogOutFailure(this.error);
}
