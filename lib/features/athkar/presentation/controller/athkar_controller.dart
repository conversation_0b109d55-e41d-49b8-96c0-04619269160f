import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:salawati/core/data/providers/salawati_database_provider.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/features/athkar/data/models/athkar_category_model.dart';
import 'package:salawati/features/athkar/presentation/screens/search_athkar_screen.dart';

import '../../../../core/utils/app_consts.dart';
import '../../data/models/athkar_model.dart';
import '../screens/athkar_details_screen.dart';

class AthkarController extends GetxController {
  static AthkarController get instance => Get.find();
  RxList<AthkarCategoryModel> athkar = RxList<AthkarCategoryModel>.empty();
  RxList<AthkarModel> searchResults = RxList<AthkarModel>.empty();
  final Debouncer debouncer = Debouncer(milliseconds: 500);
  final Map<String, List<AthkarModel>> _searchCache = {};

  Map<int, String> icons = {
    1: AppSvgs.kSun2,
    2: AppSvgs.kMoon,
    3: AppSvgs.kSleep,
    4: AppSvgs.kAlarm,
    5: AppSvgs.kPraying,
    14: AppSvgs.kAfterSalah,
    17: AppSvgs.kAthkarIcon,
    18: AppSvgs.kAthkarIcon,
    19: AppSvgs.kAthkarIcon,
    20: AppSvgs.kAthkarIcon,
    21: AppSvgs.kQuran,
    22: AppSvgs.kProphet,
    23: AppSvgs.kAllahNames,
    24: AppSvgs.kMosque,
    25: AppSvgs.kQibla,
    26: AppSvgs.kHajj,
    27: AppSvgs.kAthkarIcon,
    148: AppSvgs.kAthkarIcon,
    156: AppSvgs.kAthkarIcon,
  };

  @override
  onInit() {
    super.onInit();
    loadAthkar();
    fontSize.value = cacheMemory.read('athkar_font_size') ?? 18.0;
    deviceVibration.value = cacheMemory.read('athkar_vibration') ?? true;

    update();
  }

  RxDouble fontSize = 18.0.obs;
  RxBool deviceVibration = true.obs;

  void increaseFontSize() {
    fontSize.value = (fontSize.value < 30) ? fontSize.value + 2.0 : 30.0;
    cacheMemory.write('athkar_font_size', fontSize.value);
    update();
  }

  void decreaseFontSize() {
    fontSize.value = (fontSize.value > 16) ? fontSize.value - 2.0 : 18.0;
    cacheMemory.write('athkar_font_size', fontSize.value);
    update();
  }

  double get currentFontSize => fontSize.value;
  bool get currentVibration => deviceVibration.value;

  void addVibration() {
    deviceVibration.value = !deviceVibration.value;
    cacheMemory.write('athkar_vibration', deviceVibration.value);
    update();
  }

  Future<void> loadAthkar() async {
    // String jsonString = await rootBundle.loadString(AppJsons.kAthkarData);
    // List<dynamic> athkarTemp = json.decode(jsonString);
    // var athkarList =
    //     athkarTemp.map((item) => AthkarCategoryModel.fromJson(item)).toList();
    var athkarList = await SalawatiDatabaseProvider.getAllAthkarsData();
    athkarList.removeWhere(((element) => element.id == 25 || element.id == 26));
    athkar.value = athkarList;
    update();
  }

  // Future<void> searchAthkar(String query) async {
  //   debouncer.run(() async {
  //     if (query.isEmpty) {
  //       searchResults.clear();
  //       return;
  //     }

  //     var results = await SalawatiDatabaseProvider.searchAthkar(query);
  //     searchResults.value = results;
  //   });
  // }
  void onSearchTextChanged(String query) {
    debouncer.run(() async {
      if (query.isEmpty) {
        searchResults.clear();
        return;
      }

      // Check cache first
      if (_searchCache.containsKey(query)) {
        searchResults.value = _searchCache[query]!;
        return;
      }

      // Fetch from database
      try {
        var results = await SalawatiDatabaseProvider.searchAthkar(query);
        _searchCache[query] = results; // Cache results
        searchResults.value = results;
      } catch (e) {
        debugPrint('Error searching Athkar: $e');
      }
    });
  }

  void navigateToAthkarDetails(AthkarModel athkar,
      {bool isFromSearch = false}) {
    Get.to(
      () => AthkarDetailsScreen(
        category: AthkarCategoryModel(
          id: athkar.categoryId ?? 0,
          name: 'Search Result'.tr,
          subCategories: [],
          athkar: [athkar],
        ),
        isFromSearch: isFromSearch,
      ),
    );
  }
}
