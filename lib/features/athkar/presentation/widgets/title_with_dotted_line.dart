import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:get/get.dart';
import 'package:salawati/features/athkar/presentation/controller/athkar_controller.dart';

class TitleWithDottedLine extends GetView<AthkarController> {
  const TitleWithDottedLine({
    super.key,
    required this.title,
    this.withDottedLine = true,
  });

  final String? title;
  final bool withDottedLine;
  @override
  Widget build(BuildContext context) {
    if (title == null) {
      return const SizedBox();
    }
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Obx(
              () => Text(
                title!.replaceAll('،', ''),
                textAlign: TextAlign.justify,
                style: TextStyle(
                  fontFamily: 'Hafs',
                  fontSize: controller.fontSize.value.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
        if (withDottedLine) ...[
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: const DottedLine(
                dashLength: 4,
                lineLength: double.infinity,
                dashColor: Colors.grey),
          ),
        ],
      ],
    );
  }
}
