import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:horizontal_list_view/horizontal_list_view.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/athkar/data/models/athkar_category_model.dart';
import 'package:salawati/features/athkar/data/models/athkar_model.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_details_screen.dart';
import 'package:salawati/features/athkar/presentation/widgets/title_with_dotted_line.dart';

import '../../../../core/utils/app_functions.dart';

class AthkarItemBuilder extends StatefulWidget {
  const AthkarItemBuilder(
      {super.key,
      required this.athkarModel,
      required this.currentPage,
      required this.controller,
      required this.totalPages});
  final AthkarModel athkarModel;
  final HorizontalListViewController controller;
  final int currentPage;
  final int totalPages;

  @override
  State<AthkarItemBuilder> createState() => _AthkarItemBuilderState();
}

class _AthkarItemBuilderState extends State<AthkarItemBuilder> {
  int counter = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose(); // Clean up
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(21.r),
      onTap: widget.athkarModel.count == null
          ? null
          : () => setState(
                () {
                  if (widget.athkarModel.count != null) {
                    if (counter < (widget.athkarModel.count ?? 0) &&
                        counter != (widget.athkarModel.count ?? 0) - 1) {
                      counter++;
                    } else {
                      // if (counter == widget.athkarModel.count) {

                      AppFunctions.newVibrate();
                      AppFunctions.playSound();

                      widget.controller.animateToPage(
                        widget.controller.currentPage + 1,
                        duration: const Duration(milliseconds: 400),
                        curve: Curves.easeInOut,
                      );
                    }
                  }
                },
              ),

      child: Column(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: AppColor.kRectangleColor,
                borderRadius: BorderRadius.circular(21.r),
              ),
              child: CupertinoScrollbar(
                controller: _scrollController,
                thumbVisibility: true,
                thickness: 4,
                radius: const Radius.circular(8),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(21.r),
                  ),
                  padding: EdgeInsets.all(16.w),
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    physics: const BouncingScrollPhysics(),
                    child: CupertinoScrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      thickness: 4,
                      radius: const Radius.circular(8),
                      child: TitleWithDottedLine(
                        title: widget.athkarModel.text,
                        withDottedLine: false,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Column(
            children: [
              8.verticalSpace,
              if (widget.athkarModel.count != null)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: FittedBox(
                          alignment: AlignmentDirectional.centerStart,
                          fit: BoxFit.scaleDown,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              CustomText(
                                '${'Repetition'.tr} ( ${widget.athkarModel.count} )',
                                style: TextStyle(
                                  color: AppColor.kWhiteColor.withOpacity(0.6),
                                  fontWeight: FontWeight.bold,
                                  // fontFamily: 'Hafs',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      8.horizontalSpace,
                      Container(
                        height: 35.h,
                        width: 35.h,
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: counter == widget.athkarModel.count
                              ? AppColor.kOrangeColor
                              : AppColor.kRectangleColor,
                          border: Border.all(
                            color: counter == 0
                                ? AppColor.kWhiteColor.withOpacity(0.6)
                                : AppColor.kOrangeColor,
                            width: 1.5,
                          ),
                        ),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Center(
                            child: counter == widget.athkarModel.count
                                ? const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                  )
                                : counter == 0
                                    ? const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                      )
                                    : Text(
                                        counter == 0 ? '1' : '$counter',
                                        style: const TextStyle(
                                          color: AppColor.kOrangeColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              if (widget.athkarModel.count == null)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Row(
                    children: [
                      const Expanded(
                        child: FittedBox(),
                      ),
                      8.horizontalSpace,
                      Container(
                        height: 35.h,
                        width: 35.h,
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: counter == widget.athkarModel.count
                              ? AppColor.kOrangeColor
                              : AppColor.kRectangleColor,
                          border: Border.all(
                            color: counter == 0
                                ? AppColor.kWhiteColor.withOpacity(0.6)
                                : AppColor.kOrangeColor,
                            width: 1.5,
                          ),
                        ),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Center(
                            child: counter == widget.athkarModel.count
                                ? const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                  )
                                : counter == 0
                                    ? const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                      )
                                    : Text(
                                        counter == 0 ? '1' : '$counter',
                                        style: const TextStyle(
                                          color: AppColor.kOrangeColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton.filled(
                onPressed: () async {
                  await widget.controller.animateToPage(
                    widget.controller.currentPage - 1,
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeInOut,
                  );
                  await AppFunctions.newVibrate();
                  await AppFunctions.playSound();
                },
                icon: const Icon(Icons.chevron_left),
              ),
              FittedBox(
                alignment: AlignmentDirectional.centerStart,
                fit: BoxFit.scaleDown,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: CustomText(
                        '${widget.totalPages + 1}  /  ${widget.currentPage}',

                        // ',',
                        style: TextStyle(
                          color: AppColor.kWhiteColor.withOpacity(0.6),
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          // fontFamily: 'Hafs',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton.filled(
                onPressed: () async {
                  await widget.controller.animateToPage(
                    widget.controller.currentPage + 1,
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeInOut,
                  );
                  await AppFunctions.newVibrate();
                  await AppFunctions.playSound();
                },
                icon: const Icon(Icons.chevron_right),
              )
            ],
          )
        ],
      ),
      // 20.verticalSpace,
      // Padding(
      //   padding: EdgeInsets.symmetric(horizontal: 16.w),
      //   child: Row(
      //     children: [
      //       Expanded(
      //         child: FittedBox(
      //           alignment: AlignmentDirectional.centerStart,
      //           fit: BoxFit.scaleDown,
      //           child: Row(
      //             mainAxisAlignment: MainAxisAlignment.end,
      //             children: [
      //               CustomText(
      //                 '${'Repetition'.tr} ( ${widget.athkarModel.count} )',
      //                 style: TextStyle(
      //                   color: AppColor.kWhiteColor.withOpacity(0.6),
      //                   fontWeight: FontWeight.bold,
      //                   // fontFamily: 'Hafs',
      //                 ),
      //               ),
      //             ],
      //           ),
      //         ),
      //       ),
      // 8.horizontalSpace,
      // Container(
      //   height: 35.h,
      //   width: 35.h,
      //   padding: EdgeInsets.all(8.w),
      //   decoration: BoxDecoration(
      //     shape: BoxShape.circle,
      //     color: counter == widget.athkarModel.count
      //         ? AppColor.kOrangeColor
      //         : AppColor.kRectangleColor,
      //     border: Border.all(
      //       color: counter == 0
      //           ? AppColor.kWhiteColor.withOpacity(0.6)
      //           : AppColor.kOrangeColor,
      //       width: 1.5,
      //     ),
      //   ),
      //   child: FittedBox(
      //     fit: BoxFit.scaleDown,
      //     child: Center(
      //       child: counter == widget.athkarModel.count
      //           ? const Icon(
      //               Icons.check,
      //               color: Colors.white,
      //             )
      //           : Text(
      //               counter == 0 ? ' ' : '$counter',
      //               style: const TextStyle(
      //                 color: AppColor.kOrangeColor,
      //                 fontWeight: FontWeight.bold,
      //               ),
      //             ),
      //     ),
      //   ),
      // ),
      // ],
      // ),
      // ),
    );
  }
}

class AthkarItemBuilder2 extends StatefulWidget {
  const AthkarItemBuilder2(
      {super.key,
      required this.athkarModel,
      required this.athkarCategoryModel});
  final AthkarModel athkarModel;
  final AthkarCategoryModel athkarCategoryModel;

  @override
  State<AthkarItemBuilder2> createState() => _AthkarItemBuilder2State();
}

class _AthkarItemBuilder2State extends State<AthkarItemBuilder2> {
  int counter = 0;

  @override
  Widget build(BuildContext context) {
    return AthkarButtons(
      athkarModel: widget.athkarModel,
      athkarCategoryModel: widget.athkarCategoryModel,
      showShare: true,
      showVibration: true,
    );
  }
}
