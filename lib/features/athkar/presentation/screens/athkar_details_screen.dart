import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:horizontal_list_view/horizontal_list_view.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/athkar/data/models/athkar_category_model.dart';
import 'package:salawati/features/athkar/data/models/athkar_model.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_screen.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_sub_categories_screen.dart';
import 'package:salawati/features/athkar/presentation/widgets/athkar_item_builder.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';

import '../controller/athkar_controller.dart';

class AthkarDetailsScreen extends StatefulWidget {
  const AthkarDetailsScreen({
    super.key,
    required this.category,
    this.parentCategory,
    this.isFromSearch = false,
  });
  final AthkarCategoryModel? parentCategory;
  final AthkarCategoryModel category;
  final bool isFromSearch;

  @override
  State<AthkarDetailsScreen> createState() => _AthkarDetailsScreenState();
}

class _AthkarDetailsScreenState extends State<AthkarDetailsScreen>
    with WidgetsBindingObserver {
  RxInt currentPage = 1.obs;
  RxInt totalPages = 1.obs;

  AthkarModel? currentAthkar;

  final HorizontalListViewController controller =
      HorizontalListViewController();

  @override
  void initState() {
    super.initState();

    controller.addListener(() {
      currentPage.value = controller.currentPage + 1;
      totalPages.value = controller.pageLenght;
    });

    // Reset page when screen becomes visible
    WidgetsBinding.instance.addPostFrameCallback((_) {
      totalPages.value = controller.pageLenght > 0
          ? controller.pageLenght
          : widget.category.athkar.length;
    });

    // Add observer for visibility changes
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    controller.removeListener(() {});
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      currentPage.value = 1;
      totalPages.value = controller.pageLenght;
    }
  }

  @override
  Widget build(BuildContext context) {
    var screenSize = MediaQuery.sizeOf(context);

    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.05.sh),
              child: ListView(
                children: [
                  Row(
                    children: [
                      const SettingsShareRow(),
                      const Spacer(),
                      SizedBox(
                        width: 250,
                        height: 40,
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          key: const PageStorageKey<String>(
                              'athkarListShareKey'),
                          itemCount: 1,
                          itemBuilder: (context, index) {
                            return AthkarItemBuilder2(
                              key: ObjectKey(widget
                                  .category.athkar[currentPage.value - 1]),
                              athkarModel:
                                  widget.category.athkar[currentPage.value - 1],
                              athkarCategoryModel: widget.category,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  // 16.verticalSpace,
                  InkWell(
                    onTap: () {
                      if (widget.isFromSearch) {
                        Get.back();
                      } else {
                        if (widget.parentCategory != null) {
                          LayoutController.instance.changeScreenLayout(
                            AthkarSubCategoriesScreen(
                              category: widget.parentCategory!,
                            ),
                          );
                        } else {
                          Get.find<LayoutController>()
                              .changeScreenLayout(const AthkarScreen());
                        }
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: CustomHorizontalArrow(
                              isBack: true,
                              color: AppColor.kOrangeColor,
                            ),
                          ),
                          CustomText(
                            'Athkar',
                            style: TextStyle(
                              fontSize: 21.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  CustomText(
                    widget.category.name ?? "",
                    style: TextStyle(
                        fontSize: 17.sp,
                        color: AppColor.kWhiteColor.withOpacity(0.6)),
                  ),
                  16.verticalSpace,
                  HorizontalListView.builder(
                    controller: controller,
                    key: const PageStorageKey<String>('athkarListKey'),
                    itemCount: widget.category.athkar.length,
                    crossAxisCount: 1,
                    crossAxisSpacing: 8.0,
                    alignment: CrossAxisAlignment.start,
                    itemBuilder: (context, index) {
                      return SizedBox(
                        height: screenSize.height / 1.8,
                        child: Obx(
                          () => AthkarItemBuilder(
                            key: ObjectKey(widget.category.athkar[index]),
                            athkarModel: widget.category.athkar[index],
                            controller: controller,
                            currentPage: currentPage.value,
                            totalPages: totalPages.value,
                          ),
                        ),
                      );
                    },
                  ),
                  0.1.sh.verticalSpace,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class AthkarButtons extends GetView<AthkarController> {
  final AthkarModel? athkarModel;
  final AthkarCategoryModel? athkarCategoryModel;
  final bool showShare;
  final bool showVibration;

  const AthkarButtons({
    super.key,
    this.athkarModel,
    this.athkarCategoryModel,
    this.showShare = false,
    this.showVibration = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(
            Icons.remove_circle,
            color: Theme.of(context).cardColor,
          ),
          onPressed: () => controller.decreaseFontSize(),
          tooltip: 'A-',
        ),
        Obx(() => Text(
              'A',
              style: TextStyle(fontSize: controller.currentFontSize),
            )),
        IconButton(
          icon: Icon(
            Icons.add_circle,
            color: Theme.of(context).cardColor,
          ),
          onPressed: () => controller.increaseFontSize(),
          tooltip: 'A+',
        ),
        if (showVibration)
          Obx(
            () => IconButton(
              icon: Icon(
                controller.deviceVibration.value
                    ? Icons.vibration_rounded
                    : Icons.mobile_off,
                color: Theme.of(context).cardColor,
              ),
              onPressed: () => controller.addVibration(),
              tooltip: '📳',
            ),
          ),
        if (showShare)
          IconButton(
            icon: Icon(Icons.share, color: Theme.of(context).cardColor),
            onPressed: () {
              Get.toNamed(
                AppRouter.kShareThekerScreen,
                arguments: {
                  'athkarModel': athkarModel,
                  'athkarCategoryModel': athkarCategoryModel,
                },
              );
            },
            tooltip: 'Share'.tr,
          ),
      ],
    );
  }
}
