import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/athkar/presentation/controller/athkar_controller.dart';

class SearchAthkarScreen extends StatelessWidget {
  const SearchAthkarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.find<AthkarController>();

    return Scaffold(
      key: const ValueKey<String>('SearchAthkarScreen'),
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 16.w, vertical: 0.05.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            const Padding(
                              padding: EdgeInsets.all(8.0),
                              child: CustomHorizontalArrow(
                                isBack: true,
                                color: AppColor.kOrangeColor,
                              ),
                            ),
                            CustomText(
                              'Back'.tr,
                              style: TextStyle(
                                fontSize: 21.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    16.verticalSpace,
                    // Search Bar

                    SmoothEdgesContainer(
                      borderRadius: BorderRadius.circular(60.r),
                      padding: EdgeInsets.all(16.w),
                      color: AppColor.kRectangleColor,
                      child: Column(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: AppColor.kRectangleColor,
                              borderRadius: BorderRadius.circular(15.r),
                            ),
                            child: CustomTextField(
                              hintText: 'Search Athkar...'.tr,
                              prefixIconData: const Icon(Icons.search),
                              onChanged: (value) {
                                controller.onSearchTextChanged(value);
                              },
                            ),
                          ),
                          16.verticalSpace,
                          // Search Results
                          Obx(() {
                            if (controller.searchResults.isNotEmpty) {
                              return ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: controller.searchResults.length,
                                itemBuilder: (context, index) {
                                  var athkar = controller.searchResults[index];
                                  return Card(
                                    color: Theme.of(context)
                                        .scaffoldBackgroundColor,
                                    margin: EdgeInsets.symmetric(vertical: 8.h),
                                    elevation: 2,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12.r),
                                    ),
                                    child: ListTile(
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16.w,
                                        vertical: 12.h,
                                      ),
                                      title: CustomText(
                                        athkar.text ?? '',
                                      ),
                                      // subtitle: athkar.subtitle != null
                                      //     ? CustomText(
                                      //         athkar.subtitle!,
                                      //         style: TextStyle(
                                      //           fontSize: 14.sp,
                                      //           color: AppColor.kGreyColor,
                                      //         ),
                                      //       )
                                      //     : null,
                                      trailing: const Icon(
                                        Icons.arrow_forward_ios,
                                        size: 16,
                                        color: AppColor.kOrangeColor,
                                      ),
                                      onTap: () {
                                        controller.navigateToAthkarDetails(
                                            athkar,
                                            isFromSearch: true);
                                      },
                                    ),
                                  );
                                },
                              );
                            } else {
                              return Center(
                                child: CustomText(
                                  'Search Result'.tr,
                                  style: TextStyle(
                                    fontSize: 17.sp,
                                    color:
                                        AppColor.kWhiteColor.withOpacity(0.7),
                                  ),
                                ),
                              );
                            }
                          }),
                        ],
                      ),
                    ),

                    0.1.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class Debouncer {
  final int milliseconds;
  VoidCallback? action;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  void run(VoidCallback action) {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}
