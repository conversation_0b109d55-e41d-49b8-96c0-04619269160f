import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/athkar/data/models/athkar_category_model.dart';
import 'package:salawati/features/athkar/presentation/controller/athkar_controller.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_details_screen.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_sub_categories_screen.dart';
import 'package:salawati/features/athkar/presentation/screens/search_athkar_screen.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';

class AthkarScreen extends StatelessWidget {
  const AthkarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.find<AthkarController>();
    return Scaffold(
      key: const ValueKey<String>('AthkarScreen'),
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.05.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SettingsShareRow(),
                    16.verticalSpace,
                    // Search Bar

                    SmoothEdgesContainer(
                      borderRadius: BorderRadius.circular(60.r),
                      // padding: EdgeInsets.all(24.w),
                      color: AppColor.kRectangleColor,
                      child: Column(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: AppColor.kRectangleColor,
                              borderRadius: BorderRadius.circular(15.r),
                            ),
                            child: GestureDetector(
                              onTap: () {
                                // Navigate to the SearchAthkarScreen
                                Get.to(() => const SearchAthkarScreen());
                              },
                              child: AbsorbPointer(
                                child: CustomTextField(
                                  hintText: 'Search Athkar...'.tr,
                                  prefixIconData: Icon(
                                    Icons.search,
                                    color:
                                        AppColor.kWhiteColor.withOpacity(0.6),
                                    size: 20.h,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    16.verticalSpace,
                    CustomText(
                      'Athkar',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    CustomText(
                      'Hisn Al Muslim',
                      style: TextStyle(
                        fontSize: 17.sp,
                        color: AppColor.kWhiteColor.withOpacity(0.7),
                      ),
                    ),
                    16.verticalSpace,
                    Obx(() {
                      return GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: controller.athkar.length,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 8.w,
                          mainAxisSpacing: 8.w,
                          childAspectRatio: 1,
                        ),
                        itemBuilder: (BuildContext context, int index) {
                          AthkarCategoryModel category =
                              controller.athkar[index];
                          return AthkarCategoryGridTile(
                            icon: AthkarController.instance.icons[
                                    AthkarController
                                        .instance.athkar[index].id] ??
                                AppSvgs.kAthkarIcon,
                            title:
                                AthkarController.instance.athkar[index].name ??
                                    "",
                            category: category,
                          );
                        },
                      );
                    }),
                    0.1.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class AthkarCategoryGridTile extends StatelessWidget {
  const AthkarCategoryGridTile({
    super.key,
    required this.category,
    required this.icon,
    required this.title,
  });

  final AthkarCategoryModel category;
  final String icon;
  final String title;
  @override
  Widget build(BuildContext context) {
    return SmoothEdgesContainer(
      borderRadius: BorderRadius.circular(40.r),
      color: AppColor.kRectangleColor,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(40.r),
          splashColor: Colors.white.withAlpha(25),
          highlightColor: Colors.white.withAlpha(13),
          onTap: () {
            if (category.subCategories.isNotEmpty) {
              LayoutController.instance.changeScreenLayout(
                AthkarSubCategoriesScreen(
                  category: category,
                ),
              );
            } else {
              LayoutController.instance.changeScreenLayout(
                AthkarDetailsScreen(
                  category: category,
                ),
              );
            }
          },
          child: Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 14.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    icon,
                    width: 35.w,
                    height: 35.w,
                  ),
                  16.verticalSpace,
                  CustomText(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
