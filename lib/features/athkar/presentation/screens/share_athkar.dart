import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_button.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/athkar/data/models/athkar_model.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_details_screen.dart';
import 'package:salawati/features/athkar/presentation/widgets/title_with_dotted_line.dart';

import 'package:image/image.dart' as img;
import 'package:flutter_animate/flutter_animate.dart';
import 'package:share_plus/share_plus.dart';

import '../../data/models/athkar_category_model.dart';

class ShareThekerScreen extends StatefulWidget {
  const ShareThekerScreen({super.key});

  @override
  ShareThekerScreenState createState() => ShareThekerScreenState();
}

class ShareThekerScreenState extends State<ShareThekerScreen> {
  final GlobalKey _shareableContentKey = GlobalKey(); // Key for RepaintBoundary
  final ShareThekerController controller =
      Get.put(ShareThekerController()); // Create the controller locally
  bool _isSharing = false; // Add a sharing state variable
  bool _hasBackgroundChanged = false; // Add this boolean

  AthkarModel? athkarModel;
  AthkarCategoryModel? athkarCategoryModel;

  int _selectedBackgroundIndex = 0;
  final List<String> _availableBackgrounds = [
    'assets/images/share_background/1.jpg',
    'assets/images/share_background/2.jpg',
    'assets/images/share_background/3.jpg',
    'assets/images/share_background/4.jpg',
    'assets/images/share_background/5.jpg',
    'assets/images/share_background/6.jpg',
    'assets/images/share_background/7.jpg',
    'assets/images/share_background/8.jpg',
    'assets/images/share_background/9.jpg',
    'assets/images/share_background/10.jpg',
    'assets/images/share_background/11.jpg',
  ];
  final List<ImageProvider> _backgroundImages = [];

  @override
  void initState() {
    super.initState();
    _loadBackgroundImages();
    _selectedBackgroundIndex = cacheMemory.read('selectedBackgroundIndex') ?? 0;
    // Access arguments here
    final arguments = Get.arguments as Map<String, dynamic>;
    _selectedBackgroundIndex = cacheMemory.read('selectedBackgroundIndex') ?? 0;
    athkarModel = arguments['athkarModel'] as AthkarModel?;
    athkarCategoryModel =
        arguments['athkarCategoryModel'] as AthkarCategoryModel?;
  }

  Future<void> _loadBackgroundImages() async {
    for (var path in _availableBackgrounds) {
      final image = AssetImage(path);
      _backgroundImages.add(image);
    }
    setState(() {});
  }

  Future<void> shareAthkarAsImaeg(
      GlobalKey globalKey, BuildContext context) async {
    if (globalKey.currentContext == null) return;

    final boundary =
        globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;

    final image = await boundary.toImage(pixelRatio: 2.0);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final pngBytes = byteData!.buffer.asUint8List();

    // Compress the image
    final compressedImage = img.decodeImage(pngBytes);
    if (compressedImage == null) {
      debugPrint('Failed to decode image');
      return;
    }
    final compressedBytes = img.encodePng(compressedImage, level: 6);

    final tempDir = await getTemporaryDirectory(); // Use temporary directory
    final file = File('${tempDir.path}/shared_athkar.png');
    await file.writeAsBytes(compressedBytes);

    try {
      // Use the deprecated API for now as it works on iOS
      // This will need to be updated when the package is updated
      await Share.shareXFiles([XFile(file.path)], text: 'share_athkar'.tr);
    } catch (e) {
      debugPrint('Error sharing image: $e');
    } finally {
      await file.delete(); // Delete the temporary file after sharing
    }
  }

  Future<void> shareAthkarAsText(String textToShare) async {
    String text = "share text".trParams({
      'android_link': playStoreUrl,
      'ios_link': appStoreUrl,
    });
    // Use the deprecated API for now as it works on iOS
    // This will need to be updated when the package is updated
    await Share.share("$textToShare\n\n$text", subject: 'share_athkar'.tr);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: GetBuilder<ShareThekerController>(
          builder: (controller) => Stack(
            children: [
              Opacity(
                opacity: controller.hideOnShare.value ? 0.2 : 0.1,
                child: RepaintBoundary(
                  key: _shareableContentKey,
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: 0.6,
                        child: Transform.scale(
                          scale: 1.1,
                          child: Image.asset(
                            _availableBackgrounds[_selectedBackgroundIndex],
                            height: double.infinity,
                            width: double.infinity,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: Get.height,
                        child: Stack(
                          alignment: Alignment.topCenter,
                          children: [
                            ShareAthkarBox(
                              athkarCategoryModel: athkarCategoryModel!,
                              athkarModel: athkarModel!,
                            ),
                          ],
                        ),
                      ),
                      const Align(
                        alignment: Alignment.bottomCenter,
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 60),
                          child: CircleAvatar(
                            backgroundColor: Colors.transparent,
                            foregroundColor: Colors.transparent,
                            radius: 50,
                            backgroundImage:
                                AssetImage('assets/launcher/launcher.png'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Stack(
                children: [
                  Opacity(
                    opacity: 0.6,
                    child: Transform.scale(
                      scale: 1.1,
                      child: Image.asset(
                        _availableBackgrounds[_selectedBackgroundIndex],
                        height: double.infinity,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      ).animate(
                        key: ValueKey(_selectedBackgroundIndex),
                        effects: [
                          if (_hasBackgroundChanged) ...[
                            FadeEffect(
                                duration: 500.ms, curve: Curves.easeInOut),
                            ScaleEffect(
                              begin: const Offset(1.2, 1.2),
                              end: const Offset(1.0, 1.0),
                              duration: 600.ms,
                              curve: Curves.easeOutBack,
                            ),
                            Effect(
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  ShareHeader(widget: athkarModel!),
                  SizedBox(
                    height: Get.height,
                    child: Stack(
                      alignment: Alignment.topCenter,
                      children: [
                        ShareAthkarBox(
                          athkarCategoryModel: athkarCategoryModel!,
                          athkarModel: athkarModel!,
                        ),
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Column(
                            children: [
                              ShareBoxImageSlider(
                                  availableBackgrounds: _availableBackgrounds,
                                  backgroundImages: _backgroundImages,
                                  selectedIndex: _selectedBackgroundIndex,
                                  update: (index) {
                                    setState(() {
                                      if (_selectedBackgroundIndex != index) {
                                        _hasBackgroundChanged =
                                            true; // Set to true if changed
                                      }
                                      _selectedBackgroundIndex = index;
                                      cacheMemory.write(
                                          'selectedBackgroundIndex',
                                          _selectedBackgroundIndex);
                                    });
                                  }),
                              _isSharing
                                  ? SizedBox(
                                      height: 65.h,
                                      child: Center(
                                        // Show loading indicator
                                        child: SizedBox(
                                          height: 25.h,
                                          width: 25.h,
                                          child: CircularProgressIndicator(
                                            color: AppColor.kWhiteColor
                                                .withAlpha((0.6 * 255).round()),
                                            strokeWidth: 4,
                                          ),
                                        ),
                                      ),
                                    )
                                  : ShareBoxButtons(
                                      shareasImage: () async {
                                        setState(() {
                                          _isSharing =
                                              true; // Set sharing state to true
                                        });
                                        controller.hideOnShare.value = true;

                                        WidgetsBinding.instance
                                            .addPostFrameCallback((_) async {
                                          try {
                                            await shareAthkarAsImaeg(
                                                _shareableContentKey,
                                                context); // Use the correct key
                                          } catch (error) {
                                            debugPrint(
                                                'Sharing cancelled or error occurred: $error');
                                          } finally {
                                            controller.hideOnShare.value =
                                                false;
                                            setState(() {
                                              _isSharing =
                                                  false; // Set sharing state to false
                                            });
                                          }
                                        });
                                      },
                                      shareasText: () async {
                                        setState(() {
                                          _isSharing = true;
                                        });
                                        try {
                                          await shareAthkarAsText(
                                              athkarModel?.text ??
                                                  ""); // Share the text
                                        } catch (e) {
                                          debugPrint("Error sharing text: $e");
                                        } finally {
                                          setState(() {
                                            _isSharing = false;
                                          });
                                        }
                                      },
                                    )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ShareBoxButtons extends StatelessWidget {
  const ShareBoxButtons({
    super.key,
    required this.shareasImage,
    required this.shareasText,
  });

  final Function() shareasImage;
  final Function() shareasText;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
            child: CustomButton(
              onTap: shareasImage,
              label: 'shareAsImage',
              color: AppColor.kOrangeColor,
              fontSize: 16,
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
            child: CustomButton(
              onTap: shareasText,
              label: 'shareAsText',
              color: AppColor.kScaffoldColor,
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }
}

class ShareBoxImageSlider extends StatefulWidget {
  final List<String> availableBackgrounds;
  final List<ImageProvider> backgroundImages;
  final Function(int) update;
  final int selectedIndex;

  const ShareBoxImageSlider({
    super.key,
    required this.availableBackgrounds,
    required this.backgroundImages,
    required this.update,
    required this.selectedIndex,
  });

  @override
  State<ShareBoxImageSlider> createState() => _ShareBoxImageSliderState();
}

class _ShareBoxImageSliderState extends State<ShareBoxImageSlider> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.availableBackgrounds.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: GestureDetector(
              onTap: () {
                widget.update(index);
              },
              child: Stack(
                alignment: Alignment.center,
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.transparent,
                    radius: 40.r,
                    backgroundImage: widget.backgroundImages[index],
                  )
                      .animate(target: widget.selectedIndex == index ? 1 : 0)
                      .move(delay: 300.ms, duration: 600.ms)
                      .blurXY(begin: 0.1, end: 0.9)
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class ShareAthkarBox extends StatelessWidget {
  const ShareAthkarBox({
    super.key,
    required this.athkarModel,
    required this.athkarCategoryModel,
  });

  final AthkarModel athkarModel;
  final AthkarCategoryModel athkarCategoryModel;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(top: 70.h, left: 10.w, right: 10.w, bottom: 140.h),
      child: Column(
        children: [
          CustomText(
            athkarCategoryModel.name ?? '',
            style: TextStyle(
              fontSize: 21.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          8.verticalSpace,
          SmoothEdgesContainer(
            borderRadius: BorderRadius.all(Radius.circular(60.r)),
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: 15.w,
              vertical: 10.h,
            ),
            color:
                Theme.of(context).primaryColor.withAlpha((0.8 * 255).round()),
            child: SingleChildScrollView(
              child: TitleWithDottedLine(
                title: athkarModel.text,
                withDottedLine: false,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ShareHeader extends StatelessWidget {
  const ShareHeader({
    super.key,
    required this.widget,
  });

  final AthkarModel widget;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText(
            'share_athkar',
            style: TextStyle(
              fontSize: 21.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          AthkarButtons(athkarModel: widget),
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: CircleAvatar(
                  radius: 13,
                  child: SvgPicture.asset(
                    AppSvgs.kClose,
                    height: 10,
                  )),
            ),
          ),
        ],
      ),
    );
  }
}

class CaptureWidget extends StatefulWidget {
  final Widget child;

  const CaptureWidget({super.key, required this.child});

  @override
  CaptureWidgetState createState() => CaptureWidgetState();
}

class CaptureWidgetState extends State<CaptureWidget> {
  late final _repaintKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: _repaintKey,
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: widget.child,
      ),
    );
  }

  Future<Uint8List> captureImage() async {
    final boundary =
        _repaintKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    final image = await boundary.toImage(pixelRatio: 2.0);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }
}

class ShareThekerController extends GetxController {
  RxBool hideOnShare = false.obs;
}
