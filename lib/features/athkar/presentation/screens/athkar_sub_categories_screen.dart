import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/athkar/data/models/athkar_category_model.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_details_screen.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_screen.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';

class AthkarSubCategoriesScreen extends StatelessWidget {
  const AthkarSubCategoriesScreen({super.key, required this.category});
  final AthkarCategoryModel category;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: const ValueKey<String>('AthkarSubCategoriesScreen'),
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.1.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SettingsShareRow(),
                    16.verticalSpace,
                    Row(
                      children: [
                        InkWell(
                          onTap: () => Get.find<LayoutController>()
                              .changeScreenLayout(const AthkarScreen()),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              children: [
                                const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: CustomHorizontalArrow(
                                    isBack: true,
                                    color: AppColor.kOrangeColor,
                                  ),
                                ),
                                CustomText(
                                  'Athkar',
                                  style: TextStyle(
                                    fontSize: 21.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    CustomText(
                      category.name ?? "",
                      style: TextStyle(
                        fontSize: 17.sp,
                        color: AppColor.kWhiteColor.withOpacity(0.7),
                      ),
                    ),
                    16.verticalSpace,
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: category.subCategories.length,
                      itemBuilder: (context, index) {
                        return SmoothEdgesContainer(
                          borderRadius: BorderRadius.circular(40.r),
                          color: AppColor.kRectangleColor,
                          child: Material(
                            color: AppColor.kRectangleColor,
                            child: InkWell(
                              onTap: () {
                                LayoutController.instance.changeScreenLayout(
                                  AthkarDetailsScreen(
                                    parentCategory: category,
                                    category: category.subCategories[index],
                                  ),
                                );
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: 16.h, horizontal: 14.w),
                                child: Center(
                                  child: CustomText(
                                    category.subCategories[index].name ?? "",
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.visible,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 16.w,
                        mainAxisSpacing: 16.w,
                        childAspectRatio: 1,
                      ),
                    ),
                    0.1.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
