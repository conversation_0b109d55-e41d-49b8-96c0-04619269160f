import 'package:salawati/features/athkar/data/models/athkar_model.dart';

class AthkarCategoryModel {
  String? name;
  int id;
  int? parentId;
  List<AthkarCategoryModel> subCategories;
  List<AthkarModel> athkar;

  AthkarCategoryModel({
    required this.subCategories,
    required this.athkar,
    required this.id,
    required this.name,
    this.parentId,
  });

  factory AthkarCategoryModel.fromJson(Map<String, dynamic> json) {
    return AthkarCategoryModel(
      name: _handleNull(json['name']),
      id: json['id'],
      parentId: json['parent_id'],
      subCategories: json['sub_categories'] == null
          ? []
          : json['sub_categories']
              .map<AthkarCategoryModel>(
                  (item) => AthkarCategoryModel.fromJson(item))
              .toList(),
      athkar: json['adhkar'] == null
          ? []
          : json['adhkar']
              .map<AthkarModel>((item) => AthkarModel.fromJson(item))
              .toList(),
    );
  }
}

String? _handleNull(x) {
  return x.toString() == 'null' || x.toString().isEmpty ? null : x.toString();
}
