class AthkarModel {
  final String? notes;
  final String? reference;
  final String? text;
  final String? explanation;
  final int? count;
  final int? categoryId;

  AthkarModel({
    required this.notes,
    required this.reference,
    required this.text,
    required this.explanation,
    required this.count,
    required this.categoryId,
  });

  factory AthkarModel.fromJson(Map<String, dynamic> json) {
    return AthkarModel(
      notes: _handleNull(json['notes']),
      reference: _handleNull(json['reference']),
      text: _handleNull(json['text']),
      explanation: _handleNull(json['explanation']),
      count: json['repeatation_count'],
      categoryId: json['category_id'],
    );
  }
}

String? _handleNull(x) {
  return x.toString() == 'null' || x.toString().isEmpty ? null : x.toString();
}
