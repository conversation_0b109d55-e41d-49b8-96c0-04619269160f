// ignore_for_file: constant_identifier_names

import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:adhan/adhan.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:home_widget/home_widget.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_date.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';

class HomeWidgetController extends GetxController {
  static HomeWidgetController get instance => Get.find();

  @override
  Future<void> onInit() async {
    super.onInit();
    debugPrint('amin startHomeWidget2 onInit');
    if (Platform.isAndroid) {
      // await PrayerWidgetService.initializeAppGroup();
      await PrayerWidgetService.updatePrayerTimeWidget(
          Get.find<PrayerController>());
    }
  }
}

class PrayerWidgetService {
  // Configuration constants
  // static const _appGroupId = 'group.prayer_widget';
  // static const _iOSWidgetName = 'prayer_widget';
  static const _androidWidgetName = 'PrayerWidgetProvider';
  static const _qualifiedAndroidName =
      'com.salawati.app.glance.PrayerWidgetReceiver';
  static const _widgetUpdateTask = 'prayerWidgetUpdate';

  // Data keys
  static const _devicePixelRatio = "device_pixel_ratio";
  static const _cityName = "city_name";
  static const _currentLocation = "current_location";
  static const _widgetDataKey = 'prayer_times_image';
  static final logger = BackgroundLogger();

  // Minimum update interval (15 minutes in seconds)
  static const _updateInterval = 15 * 60;

  // Main widget update method
  static Future<void> updatePrayerTimeWidget(
      PrayerController controller) async {
    debugPrint(
        'Native called aminhomewidget ${controller.latitude} - ${controller.longitude}');
    try {
      await _sendDataToWidget(controller);
      if (kDebugMode) {
        debugPrint(
            "🔵 [Background] Native called  _sendDataToWidget Widget updated successfully");
      }
    } catch (e) {
      debugPrint(
          "🔴 Native called [Background Error] Widget update failed: $e");
    }
  }

  // Data handling and widget rendering
  static Future<void> _sendDataToWidget(PrayerController controller) async {
    final logger = BackgroundLogger();

    try {
      final pixelRatio = await HomeWidget.getWidgetData<double>(
        _devicePixelRatio,
        defaultValue: 3.5,
      );

      final date = DateTime.now();
      // await initializeAppGroup();

      await controller.justCalculateTimes(
        date: DateComponents(date.year, date.month, date.day),
      );
      await Future.wait([
        controller.calculatePrayerTimes(),
        controller.getCustomLocation(),
        // controller.getCurrentCityUsingGeocode(),
      ]);

      await _renderWidget(
        controller: controller,
        pixelRatio: pixelRatio ?? 3.5,
      );

      await _updateNativeWidget();
    } on PlatformException catch (e) {
      logger.log('Platform error during widget update: ${e.message}');
      rethrow;
    }
  }

  // Platform-specific initialization
  // static Future<void> initializeAppGroup() async {
  //   if (Platform.isIOS) {
  //     await HomeWidget.setAppGroupId(_appGroupId);
  //   }
  // }

  // Widget rendering
  static Future<void> _renderWidget({
    required PrayerController controller,
    required double pixelRatio,
  }) async {
    await AppFunctions.renderFlutterWidgetAsHomeWidget(
      PrayerTimesImageForHomeWidget(
        prayerTimes: controller.prayerTimes.value,
        cityName: controller.currentCity.value ?? 'Makkah'.tr,
      ),
      logicalSize: const Size(400, 200),
      key: _widgetDataKey,
      pixelRatio: pixelRatio,
      delay: const Duration(seconds: 1),
    );
  }

  // Native widget update
  static Future<void> _updateNativeWidget() async {
    await HomeWidget.updateWidget(
      // iOSName: _iOSWidgetName,
      androidName: _androidWidgetName,
      qualifiedAndroidName: _qualifiedAndroidName,
    );
  }

  // Logging utilities
  static void _logInfo(String message) {
    if (kDebugMode) {
      logger.log(message);
    }
  }

  static void _logError(String message, StackTrace? stack) {
    logger.log(message);
  }
}

// Production-ready logging setup (add this in a separate file)
class BackgroundLogger {
  void log(String message) {
    if (kDebugMode) {
      debugPrint('🔵 [Background] $message');
    }
  }

  void error(String message, StackTrace stack) {
    if (kDebugMode) {
      debugPrint('''
🔴 [Background Error] $message
📜 Stack Trace:
$stack
      ''');
    }
  }
}

class PrayerTimesImageForHomeWidget extends StatelessWidget {
  const PrayerTimesImageForHomeWidget({
    super.key,
    required this.prayerTimes,
    required this.cityName,
  });
  final PrayerTimes? prayerTimes;
  final String cityName;
  @override
  Widget build(BuildContext context) {
    // final now = DateTime.now();
    var nextPrayer = AppFunctions.nextPrayer(prayerTimes!);
    var prayerNames = {
      'fajr': 'الفجر',
      'sunrise': 'الشروق',
      'dhuhr': 'الظهر',
      'asr': 'العصر',
      'maghrib': 'المغرب',
      'isha': 'العشاء',
    };
    precacheImage(const AssetImage(AppImages.kWidgetBg), context);

    // debugPrint('amin home widget last update ($now) ');

    return Directionality(
      textDirection: TextDirection.rtl,
      child: AspectRatio(
        aspectRatio: 400 / 200,
        child: SmoothEdgesContainer(
          width: double.maxFinite,
          borderRadius: BorderRadius.circular(32),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.5),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        Image.asset(
                          AppImages.kWidgetBg,
                          fit: BoxFit.cover,
                          width: double.maxFinite,
                          alignment: Alignment.topCenter,
                          height: double.maxFinite,
                        ),
                        SizedBox(
                          width: double.maxFinite,
                          height: double.maxFinite,
                          child: Column(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 16),
                                  child: Column(
                                    children: [
                                      // CustomText(
                                      //   'افتح التطبيق لتحديث بيانات الويدجت',
                                      //   style: TextStyle(
                                      //     color: AppColor.kWhiteColor
                                      //         .withOpacity(0.7),
                                      //     fontSize: 12,
                                      //   ),
                                      // ),
                                      Row(
                                        children: [
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              CustomText(cityName),
                                              const CustomDate(isHijry: false),
                                              const CustomDate(isHijry: true),
                                              // Text(DateTime.now().toString())
                                            ],
                                          ),
                                          const Spacer(),
                                          Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              CustomText(
                                                'الصلاة القادمة',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 8,
                                              ),
                                              CustomText(
                                                // '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}',
                                                prayerTimes == null
                                                    ? ''
                                                    : '${prayerNames[nextPrayer.name] ?? ""} ${AppFunctions.formatTime(AppFunctions.getPrayerTime(nextPrayer.name, prayerTimes!))}',
                                                style: TextStyle(
                                                  color: AppColor.kWhiteColor
                                                      .withOpacity(0.7),
                                                ),
                                              ),
                                              // CustomText(
                                              //   'آخر تحديث',
                                              //   style: TextStyle(
                                              //     color: AppColor.kWhiteColor
                                              //         .withOpacity(0.7),
                                              //   ),
                                              // ),
                                              // CustomText(
                                              //   '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}',
                                              //   style: TextStyle(
                                              //     color: AppColor.kWhiteColor
                                              //         .withOpacity(0.7),
                                              //   ),
                                              // ),
                                            ],
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SmoothEdgesContainer(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(
                                    50,
                                  ),
                                ),
                                color: AppColor.kDarkBlueColor,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 16,
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: prayerTimes == null
                                        ? []
                                        : [
                                            PrayerTimeHomeWidgetTitle(
                                              prayerTime: prayerTimes!.fajr,
                                              prayerName:
                                                  prayerNames['fajr'] ?? "",
                                            ),
                                            PrayerTimeHomeWidgetTitle(
                                              prayerName: 'الشروق',
                                              prayerTime: prayerTimes!.sunrise,
                                            ),
                                            PrayerTimeHomeWidgetTitle(
                                              prayerTime: prayerTimes!.dhuhr,
                                              prayerName:
                                                  prayerNames['dhuhr'] ?? "",
                                            ),
                                            PrayerTimeHomeWidgetTitle(
                                              prayerTime: prayerTimes!.asr,
                                              prayerName:
                                                  prayerNames['asr'] ?? "",
                                            ),
                                            PrayerTimeHomeWidgetTitle(
                                              prayerTime: prayerTimes!.maghrib,
                                              prayerName:
                                                  prayerNames['maghrib'] ?? "",
                                            ),
                                            PrayerTimeHomeWidgetTitle(
                                              prayerTime: prayerTimes!.isha,
                                              prayerName:
                                                  prayerNames['isha'] ?? "",
                                            ),
                                          ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class PrayerTimeHomeWidgetTitle extends StatelessWidget {
  const PrayerTimeHomeWidgetTitle({
    super.key,
    required this.prayerTime,
    required this.prayerName,
  });

  final DateTime? prayerTime;
  final String prayerName;

  @override
  Widget build(BuildContext context) {
    return SmoothEdgesContainer(
      color: AppColor.kRectangleColor,
      height: 50,
      width: 50,
      borderRadius: BorderRadius.circular(32),
      child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomText(
              prayerName,
              style: TextStyle(
                color: AppColor.kWhiteColor.withOpacity(0.7),
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            CustomText(
              AppFunctions.formatTime(
                prayerTime!,
              ),
              style: const TextStyle(
                color: AppColor.kOrangeColor,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ]),
    );
  }
}
