// import 'dart:async';

// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import 'package:adhan/adhan.dart';
// import 'package:home_widget/home_widget.dart';
// import 'package:intl/intl.dart';
// import 'package:geolocator/geolocator.dart'
//     as geo; // Import for location (if needed)

// class NewPrayerController extends GetxController {
//   RxString currentTime = ''.obs;
//   RxMap<String, String> prayerTimes = <String, String>{}.obs;
//   RxString fajrTime = ''.obs;
//   RxString sunriseTime = ''.obs;
//   RxString dhuhrTime = ''.obs;
//   RxString asrTime = ''.obs;
//   RxString maghribTime = ''.obs;
//   RxString ishaTime = ''.obs;
//   RxString city = 'الرياض'.obs; // City name
//   String _formatTime(DateTime dateTime) {
//     return DateFormat('hh:mm:ss')
//         .format(dateTime); // Example format, adjust as needed
//   }

//   @override
//   void onInit() {
//     super.onInit();
//     debugPrint('amin1: NewPrayerController initialized');
//     _startLiveTime();
//     _updatePrayerTimes();
//     _startBackgroundUpdates();
//   }

//   void _startLiveTime() {
//     debugPrint('amin1: Starting live time updates');
//     Timer.periodic(const Duration(seconds: 1), (timer) {
//       currentTime.value = _formatTime(DateTime.now());
//       // debugPrint('amin1: Time updated to: ${currentTime.value}');
//     });
//   }

//   Future<void> _updatePrayerTimes() async {
//     debugPrint('amin1: Starting prayer time update');
//     try {
//       final myCoordinates = Coordinates(24.7136, 46.6753);
//       debugPrint(
//           'amin1: Using coordinates: ${myCoordinates.latitude}, ${myCoordinates.longitude}');

//       final params = CalculationMethod.muslim_world_league.getParameters();
//       params.madhab = Madhab.hanafi;
//       final prayerTimesObject = PrayerTimes(
//           myCoordinates, DateComponents.from(DateTime.now()), params);

//       debugPrint('amin1: Calculated prayer times:');
//       debugPrint('amin1: Fajr - ${prayerTimesObject.fajr}');
//       debugPrint('amin1: Sunrise - ${prayerTimesObject.sunrise}');
//       debugPrint('amin1: Dhuhr - ${prayerTimesObject.dhuhr}');
//       debugPrint('amin1: Asr - ${prayerTimesObject.asr}');

//       fajrTime.value = DateFormat('hh:mm ص').format(prayerTimesObject.fajr);
//       sunriseTime.value =
//           DateFormat('hh:mm ص').format(prayerTimesObject.sunrise);
//       dhuhrTime.value = DateFormat('hh:mm م').format(prayerTimesObject.dhuhr);
//       asrTime.value = DateFormat('hh:mm م').format(prayerTimesObject.asr);
//       maghribTime.value =
//           DateFormat('hh:mm م').format(prayerTimesObject.maghrib);
//       ishaTime.value = DateFormat('hh:mm م').format(prayerTimesObject.isha);

//       prayerTimes.value = {
//         'الفجر': fajrTime.value,
//         'الشروق': sunriseTime.value,
//         'الظهر': dhuhrTime.value,
//         'العصر': asrTime.value,
//       };

//       debugPrint('amin1: Formatted prayer times:');
//       debugPrint('amin1: $prayerTimes');

//       await _updateHomeWidget();
//     } catch (e) {
//       debugPrint('amin1: Error in _updatePrayerTimes: $e');
//     }
//   }

//   void _startBackgroundUpdates() {
//     debugPrint('amin1: Starting background updates timer');
//     Timer.periodic(const Duration(minutes: 15), (timer) {
//       debugPrint('amin1: Background update triggered');
//       _updatePrayerTimes();
//     });
//   }

//   Future<void> _updateHomeWidget() async {
//     try {
//       await HomeWidget.setAppGroupId('group.salah-widget');

//       // Save data with explicit types
//       await HomeWidget.saveWidgetData<String>('city', city.value);
//       await HomeWidget.saveWidgetData<String>('currentTime', currentTime.value);

//       // Save prayer times with explicit keys
//       await Future.wait([
//         HomeWidget.saveWidgetData<String>('الفجر', fajrTime.value),
//         HomeWidget.saveWidgetData<String>('الشروق', sunriseTime.value),
//         HomeWidget.saveWidgetData<String>('الظهر', dhuhrTime.value),
//         HomeWidget.saveWidgetData<String>('العصر', asrTime.value),
//       ]);

//       // Trigger update with correct iOS name
//       await HomeWidget.updateWidget(
//         iOSName: 'AppWidget',
//         androidName: 'AppWidgetProvider',
//       );

//       debugPrint('amin1: Widget data updated successfully');
//     } catch (e) {
//       debugPrint('amin1: Widget update error: $e');
//     }
//   }

//   static Future<void> updatePrayerTimesInBackground() async {
//     debugPrint('amin1: Background update started');
//     try {
//       final controller = Get.find<NewPrayerController>();
//       debugPrint('amin1: Controller instance found: ${controller.hashCode}');
//       await controller._updatePrayerTimes();
//       debugPrint('amin1: Background update completed successfully');
//     } catch (e) {
//       debugPrint('amin1: Error in background update: $e');
//     }
//   }
// }
