class MosquesModel {
  List<Mosque> mosques;
  MosquesModel({required this.mosques});

  factory MosquesModel.fromJson(Map<String, dynamic> json) {
    if (json['results'] != null && json['results'] is List) {
      return MosquesModel(
        mosques: json['results']
            .map((x) => Mosque.fromJson(x))
            .toList()
            .cast<Mosque>(),
      );
    }
    return MosquesModel(mosques: []);
  }
}

class Mosque {
  final String name;
  final double latitude;
  final double longitude;
  final String vicinity;

  Mosque(
      {required this.name,
      required this.latitude,
      required this.longitude,
      required this.vicinity});

  factory Mosque.fromJson(Map<String, dynamic> json) {
    return Mosque(
      name: json['name'],
      latitude: json['geometry']['location']['lat'].toDouble(),
      longitude: json['geometry']['location']['lng'].toDouble(),
      vicinity: json['vicinity'],
    );
  }
}
