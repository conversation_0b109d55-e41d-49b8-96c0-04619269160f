import 'package:flutter/material.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/mosque/data/models/mosque_model.dart';
import 'package:url_launcher/url_launcher.dart';

class MosqueDialog extends StatelessWidget {
  const MosqueDialog({
    super.key,
    required this.mosque,
  });
  final Mosque mosque;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: const EdgeInsets.all(16),
      // actionsAlignment: MainAxisAlignment.center,
      actionsPadding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      backgroundColor: Theme.of(context).primaryColor,
      content: IntrinsicHeight(
        child: SmoothEdgesContainer(
          borderRadius: BorderRadius.circular(60),
          padding: const EdgeInsets.all(0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CustomText(mosque.name),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () async {
            var url =
                'https://www.google.com/maps/search/?api=1&query=${mosque.latitude},${mosque.longitude}';
            if (await canLaunchUrl(Uri.parse(url))) {
              await launchUrl(Uri.parse(url));
            } else {
              throw 'Could not launch $url';
            }
          },
          icon: CustomText(
            'Open on Google Maps',
            style: const TextStyle(
              fontSize: 13,
            ),
          ),
        ),
      ],
    );
  }
}
