import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:salawati/core/widgets/custom_lottie_api.dart';
import 'package:salawati/features/mosque/presentation/cubit/mosque_cubit.dart';
import 'package:salawati/features/mosque/presentation/cubit/mosque_state.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';

class MosqueScreen extends StatefulWidget {
  const MosqueScreen({super.key});

  @override
  State<MosqueScreen> createState() => _MosqueScreenState();
}

class _MosqueScreenState extends State<MosqueScreen> {
  late GoogleMapController mapController;

  PrayerController prayerController = Get.find<PrayerController>();
  late MosqueCubit mosqueCubit;
  late double latitude;
  late double longitude;
  String? mapStyle;

  @override
  void initState() {
    super.initState();
    latitude = prayerController.latitude;
    longitude = prayerController.longitude;
    mosqueCubit = BlocProvider.of<MosqueCubit>(context);
    mosqueCubit.getNearMosques(latitude, longitude);

    // Load map style
    DefaultAssetBundle.of(context)
        .loadString('assets/json/map_style.json')
        .then((string) {
      setState(() {
        mapStyle = string;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    double cameraLat = 0;
    double cameraLong = 0;
    bool isFirstTime = true;
    return Scaffold(
      body: BlocBuilder<MosqueCubit, MosqueState>(builder: (context, state) {
        if (state is GetNearMosqueLoading && mosqueCubit.mosques.isEmpty) {
          return const Center(
              child: CustomLottieApi(lottieApi: LottieApi.loading));
        }
        if (state is GetNearMosqueFailure) {
          return const Center(
              child: CustomLottieApi(lottieApi: LottieApi.error));
        }
        return Padding(
          padding: const EdgeInsets.only(bottom: 60),
          child: GoogleMap(
            onMapCreated: (GoogleMapController controller) {
              mapController = controller;
            },
            style: mapStyle,
            initialCameraPosition: CameraPosition(
              target: LatLng(latitude, longitude), // Example coordinates
              zoom: 15,
            ),
            onCameraMove: (position) {
              isFirstTime = false;
              cameraLat = position.target.latitude;
              cameraLong = position.target.longitude;
            },
            onCameraIdle: () {
              if (isFirstTime) {
                mosqueCubit.getNearMosques(latitude, longitude);
              } else {
                mosqueCubit.getNearMosques(cameraLat, cameraLong);
              }
            },
            markers: mosqueCubit.markers,
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
          ),
        );
        // return const Center(
        //     child: CustomLottieApi(lottieApi: LottieApi.loading));
      }),
    );
  }
}
