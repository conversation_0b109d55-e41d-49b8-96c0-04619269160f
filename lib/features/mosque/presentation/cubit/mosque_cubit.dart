import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:salawati/features/mosque/data/models/mosque_model.dart';
import 'package:salawati/features/mosque/data/repo/mosque_repo.dart';
import 'package:salawati/features/mosque/presentation/cubit/mosque_state.dart';
import 'package:salawati/features/mosque/presentation/widgets/mosque_dialog.dart';

import '../../../../core/data/data_state.dart';

class MosqueCubit extends Cubit<MosqueState> {
  MosqueCubit() : super(MosqueInitial());

  List<Mosque> mosques = [];
  Future<void> getNearMosques(double latitude, double longitude) async {
    emit(GetNearMosqueLoading());
    final dataState = await MosqueRepo2.getNearMosques(latitude, longitude);
    if (dataState is DataSuccess) {
      mosques.addAll(dataState.data!.mosques);
      await setMarkers();
      emit(GetNearMosqueSuccess());
    }
    if (dataState is DataFailed) {
      emit(GetNearMosqueFailure(dataState.error!.statusMessage.toString()));
    }
  }

  Set<Marker> markers = {};
  Future<void> setMarkers() async {
    var icon = await BitmapDescriptor.asset(
      const ImageConfiguration(size: Size(24, 24)),
      'assets/images/mosque_marker.png',
    );
    markers = mosques
        .map((mosque) => Marker(
              markerId: MarkerId(
                  mosque.latitude.toString() + mosque.longitude.toString()),
              position: LatLng(mosque.latitude, mosque.longitude),
              onTap: () {
                Get.dialog(
                  MosqueDialog(mosque: mosque),
                );
              },
              icon: icon,
            ))
        .toSet();
  }
}
