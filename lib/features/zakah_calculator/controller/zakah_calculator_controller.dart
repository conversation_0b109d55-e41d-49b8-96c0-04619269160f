import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/money_zakah_section.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/silver_zakah_section.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/zakah_gold_row.dart';

class ZakahCalculatorController extends GetxController {
  static RxList<ZakahGoldRow> goldRowList = <ZakahGoldRow>[ZakahGoldRow()].obs;

  static void addRow() {
    goldRowList.add(ZakahGoldRow());
  }

  static void removeRow(int index) {
    goldRowList.removeAt(index);
  }

  static RxDouble nissab = 0.0.obs;

  static void calculateNissabValue(ZakahType zakahType) async {
    // Dismiss keyboard before calculations
    await SystemChannels.textInput.invokeMethod('TextInput.hide');
    double nissabValue = 0;
    double userMoney = 0;
    String unit = "";
    if (zakahType == ZakahType.money) {
      nissabValue = double.parse(moneyGoldGramPriceController.text) * 85;
      userMoney = double.parse(moneyAmountPriceController.text);
    } else if (zakahType == ZakahType.silver) {
      nissabValue = 595;
      userMoney = double.parse(silverAmountPriceController.text);
      unit = "Grams";
    } else {
      nissabValue = 85;
      unit = "Grams";
      for (ZakahGoldRow element in ZakahCalculatorController.goldRowList) {
        userMoney += (element.gold * element.karats) / 24;
      }
    }
    if (nissabValue > userMoney) {
      AppFunctions.showWarningMessage('You do not have to pay zakat',
          "${'The Nissab value is'.tr} $nissabValue");
    } else {
      nissab.value = userMoney / 40;
      AppFunctions.showInfoMessage(
        "The Zakah".tr,
        "${nissab.value.toStringAsFixed(2)} ${unit.tr}",
      );
    }
  }
}
