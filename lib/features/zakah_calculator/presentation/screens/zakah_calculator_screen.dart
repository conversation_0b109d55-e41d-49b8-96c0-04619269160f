import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_button.dart';
import 'package:salawati/core/widgets/custom_horizontal_arrow.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/zakah_calculator/controller/zakah_calculator_controller.dart';
import 'package:salawati/features/zakah_calculator/presentation/screens/zakah_type_screen.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/gold_zakah_section.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/money_zakah_section.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/silver_zakah_section.dart';

// ignore: must_be_immutable
class ZakahCalculatorScreen extends StatelessWidget {
  ZakahCalculatorScreen({super.key, required this.zakahType});

  ZakahCalculatorController controller = Get.put(ZakahCalculatorController());
  final ZakahType zakahType;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.1.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SettingsShareRow(),
                    16.verticalSpace,
                    InkWell(
                      onTap: () => Get.find<LayoutController>()
                          .changeScreenLayout(const ZakahTypeScreen()),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            const Padding(
                              padding: EdgeInsets.all(8.0),
                              child: CustomHorizontalArrow(
                                isBack: true,
                                color: AppColor.kOrangeColor,
                              ),
                            ),
                            CustomText(
                              ZakahTypeLabels[zakahType] ?? "",
                              style: TextStyle(
                                fontSize: 21.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    28.verticalSpace,
                    FittedBox(
                      alignment: AlignmentDirectional.centerStart,
                      fit: BoxFit.scaleDown,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Obx(() => CustomText(
                                ZakahCalculatorController.nissab
                                    .toStringAsFixed(2),
                                style: TextStyle(
                                  color: AppColor.kOrangeColor,
                                  fontSize: 48.sp,
                                ),
                              )),
                          8.horizontalSpace,
                          if (zakahType != ZakahType.money)
                            CustomText(
                              'Grams',
                              style: TextStyle(
                                  color: AppColor.kWhiteColor.withOpacity(0.6)),
                            ),
                        ],
                      ),
                    ),
                    if (zakahType == ZakahType.money)
                      const MoneyZakahSection()
                    else if (zakahType == ZakahType.gold)
                      const GoldZakahSection()
                    else if (zakahType == ZakahType.silver)
                      const SilverZakahSection(),
                    16.verticalSpace,
                    CustomButton(
                      onTap: () =>
                          ZakahCalculatorController.calculateNissabValue(
                              zakahType),
                      label: 'Calculate',
                      color: AppColor.kOrangeColor,
                    ),
                    0.1.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
