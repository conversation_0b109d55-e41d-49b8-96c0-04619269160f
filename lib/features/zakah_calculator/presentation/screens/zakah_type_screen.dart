import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/zakah_type_item.dart';

// ignore: must_be_immutable
class ZakahTypeScreen extends StatelessWidget {
  const ZakahTypeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.1.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SettingsShareRow(),
                    16.verticalSpace,
                    CustomText(
                      'Zakah calculator',
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    28.verticalSpace,
                    CustomText('Choose zakah type'),
                    18.verticalSpace,
                    GridView(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 8.w,
                        mainAxisSpacing: 8.w,
                        childAspectRatio: 1,
                      ),
                      children: const [
                        ZakahTypeItem(
                          label: 'Money Zakah',
                          svgIcon: AppSvgs.kMoney,
                          zakahType: ZakahType.money,
                        ),
                        ZakahTypeItem(
                          label: 'Gold Zakah',
                          svgIcon: AppSvgs.kGold,
                          zakahType: ZakahType.gold,
                        ),
                        ZakahTypeItem(
                          label: 'Silver Zakah',
                          svgIcon: AppSvgs.kSilver,
                          zakahType: ZakahType.silver,
                        ),
                      ],
                    ),
                    0.1.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
