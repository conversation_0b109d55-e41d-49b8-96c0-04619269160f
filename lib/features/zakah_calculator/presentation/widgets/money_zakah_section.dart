import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/features/zakah_calculator/controller/zakah_calculator_controller.dart';

TextEditingController moneyGoldGramPriceController = TextEditingController();
TextEditingController moneyAmountPriceController = TextEditingController();

class MoneyZakahSection extends StatefulWidget {
  const MoneyZakahSection({
    super.key,
  });

  @override
  State<MoneyZakahSection> createState() => _MoneyZakahSectionState();
}

class _MoneyZakahSectionState extends State<MoneyZakahSection> {
  @override
  void initState() {
    moneyGoldGramPriceController.clear();
    moneyAmountPriceController.clear();
    ZakahCalculatorController.nissab.value = 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText('${'Gold Price per Gram'.tr} 24 ${'Karats'.tr}'),
        4.verticalSpace,
        Container(
          decoration: BoxDecoration(
            color: AppColor.kRectangleColor,
            borderRadius: BorderRadius.circular(15.r),
          ),
          child: CustomTextField(
            hintText: '0.00',
            controller: moneyGoldGramPriceController,
            keyboardType: TextInputType.number,
          ),
        ),
        16.verticalSpace,
        CustomText('Money'),
        4.verticalSpace,
        Container(
          decoration: BoxDecoration(
            color: AppColor.kRectangleColor,
            borderRadius: BorderRadius.circular(15.r),
          ),
          child: CustomTextField(
            hintText: '0.00',
            controller: moneyAmountPriceController,
            keyboardType: TextInputType.number,
          ),
        ),
        16.verticalSpace,
      ],
    );
  }
}
