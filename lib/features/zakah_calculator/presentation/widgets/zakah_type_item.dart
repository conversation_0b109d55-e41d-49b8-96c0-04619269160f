import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/zakah_calculator/presentation/screens/zakah_calculator_screen.dart';

class ZakahTypeItem extends StatelessWidget {
  const ZakahTypeItem({
    super.key,
    required this.label,
    required this.svgIcon,
    required this.zakahType,
  });
  final String label;
  final ZakahType zakahType;
  final String svgIcon;

  @override
  Widget build(BuildContext context) {
    return SmoothEdgesContainer(
      decoration: BoxDecoration(color: AppColor.kRectangleColor),
      borderRadius: BorderRadius.circular(80),
      child: InkWell(
        onTap: () {
          Get.find<LayoutController>().changeScreenLayout(
            ZakahCalculatorScreen(
              zakahType: zakahType,
            ),
          );
        },
        child: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 14.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(svgIcon),
                16.verticalSpace,
                CustomText(
                  label,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
