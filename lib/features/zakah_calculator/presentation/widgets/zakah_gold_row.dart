import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_animated_dropdown.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';

// ignore: must_be_immutable
class ZakahGoldRow extends StatelessWidget {
  ZakahGoldRow({super.key});
  double gold = 0;
  int karats = 24;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          flex: 5,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Gold'),
              Container(
                decoration: BoxDecoration(
                  color: AppColor.kRectangleColor,
                  borderRadius: BorderRadius.circular(15.r),
                ),
                child: CustomTextField(
                  hintText: '0.00 g',
                  onChanged: (p0) => gold = double.parse(p0),
                ),
              ),
            ],
          ),
        ),
        8.horizontalSpace,
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Karats'),
              Container(
                decoration: BoxDecoration(
                  color: AppColor.kRectangleColor,
                  borderRadius: BorderRadius.circular(15.r),
                ),
                child: CustomAnimatedDropdown<int>(
                  expandedFillColor: AppColor.kScaffoldColor,
                  hintText: '24 ${'Karats'.tr}',
                  onChanged: (value) => karats = value ?? 24,
                  initialItem: 24,
                  items: const [18, 21, 22, 24],
                  listItemBuilder: (context, item, isSelected, onItemSelect) {
                    return CustomText("$item ${"Karats".tr}");
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
