import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_text_field.dart';
import 'package:salawati/features/zakah_calculator/controller/zakah_calculator_controller.dart';

TextEditingController silverAmountPriceController = TextEditingController();

class SilverZakahSection extends StatefulWidget {
  const SilverZakahSection({
    super.key,
  });

  @override
  State<SilverZakahSection> createState() => _SilverZakahSectionState();
}

class _SilverZakahSectionState extends State<SilverZakahSection> {
  @override
  void initState() {
    silverAmountPriceController.clear();
    ZakahCalculatorController.nissab.value = 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText('Silver'),
        4.verticalSpace,
        Container(
          decoration: BoxDecoration(
            color: AppColor.kRectangleColor,
            borderRadius: BorderRadius.circular(15.r),
          ),
          child: CustomTextField(
            hintText: '0.00',
            controller: silverAmountPriceController,
            keyboardType: TextInputType.number,
          ),
        ),
        16.verticalSpace,
      ],
    );
  }
}
