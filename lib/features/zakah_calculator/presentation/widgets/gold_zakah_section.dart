import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/zakah_calculator/controller/zakah_calculator_controller.dart';
import 'package:salawati/features/zakah_calculator/presentation/widgets/zakah_gold_row.dart';

class GoldZakahSection extends StatefulWidget {
  const GoldZakahSection({super.key});

  @override
  State<GoldZakahSection> createState() => _GoldZakahSectionState();
}

class _GoldZakahSectionState extends State<GoldZakahSection> {
  @override
  void initState() {
    ZakahCalculatorController.goldRowList = <ZakahGoldRow>[ZakahGoldRow()].obs;
    ZakahCalculatorController.nissab.value = 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(() => ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: ZakahCalculatorController.goldRowList.length,
              itemBuilder: (context, index) => Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                      flex: 8,
                      child: ZakahCalculatorController.goldRowList[index]),
                  if (index != 0)
                    Expanded(
                      child: IconButton(
                        icon: const Icon(
                          Icons.remove_circle_outline,
                          color: Colors.red,
                        ),
                        onPressed: () async {
                          ZakahCalculatorController.removeRow(index);
                          // Dismiss keyboard before calculations
                          await SystemChannels.textInput
                              .invokeMethod('TextInput.hide');
                        },
                      ),
                    ),
                ],
              ),
              separatorBuilder: (context, index) => 16.verticalSpace,
            )),
        16.verticalSpace,
        InkWell(
          onTap: () => ZakahCalculatorController.addRow(),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                16.horizontalSpace,
                Icon(
                  Icons.add_circle,
                  size: 28.sp,
                  color: AppColor.kWhiteColor,
                ),
                8.horizontalSpace,
                CustomText('Add more gold karats'),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
