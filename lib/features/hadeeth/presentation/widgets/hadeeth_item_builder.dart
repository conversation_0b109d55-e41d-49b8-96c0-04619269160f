import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/hadeeth/data/models/hadeeth_model.dart';
import 'package:salawati/features/hadeeth/presentation/controller/hadeeth_controller.dart';

class HadeethItemBuilder extends StatefulWidget {
  const HadeethItemBuilder({super.key, this.shiftIndex = 0});
  final int shiftIndex;
  @override
  State<HadeethItemBuilder> createState() => _HadeethItemBuilderState();
}

bool isExpanded = false;

class _HadeethItemBuilderState extends State<HadeethItemBuilder> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      HadeethModel hadeethModel = HadeethController.hadeethList![
          HadeethController.currentIndex.value - widget.shiftIndex];
      String fullText = "${hadeethModel.ar}\n\n${hadeethModel.en}\n\n";
      return InkWell(
        onTap: () {
          setState(() {
            isExpanded = !isExpanded;
          });
        },
        child: Stack(
          alignment: AlignmentDirectional.bottomStart,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 3000),
              height: isExpanded ? null : 0.25.sh,
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 15,
                    spreadRadius: 15,
                    color: AppColor.kWhiteColor.withOpacity(0.025),
                  ),
                ],
                borderRadius: BorderRadius.circular(21.r),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: Directionality(
                        textDirection: TextDirection.rtl,
                        child: CustomText(
                          hadeethModel.ar,
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ),
                    20.verticalSpace,
                    SizedBox(
                      width: double.infinity,
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: CustomText(
                          hadeethModel.en,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                    32.verticalSpace,
                  ],
                ),
              ),
            ),
            Container(
              height: 0.07.sh,
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: AlignmentDirectional.topCenter,
                    end: AlignmentDirectional.bottomCenter,
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.5),
                      // Colors.transparent,
                      // AppColor.kBlackColor.withOpacity(0.7),
                      Theme.of(context).primaryColor.withOpacity(1),
                    ],
                  ),
                  // color: AppColor.kPurpleColor,
                  borderRadius:
                      BorderRadius.vertical(bottom: Radius.circular(21.r))),
            ),
            PositionedDirectional(
              start: 24.w,
              bottom: 24.w,
              child: Row(
                children: [
                  InkWell(
                    onTap: () => AppFunctions.shareText(context, fullText),
                    child: SvgPicture.asset(
                      AppSvgs.kShare,
                      // ignore: deprecated_member_use
                      color: AppColor.kWhiteColor,
                    ),
                  ),
                  16.horizontalSpace,
                  InkWell(
                    onTap: () => AppFunctions.copyTextToClipboard(fullText),
                    child: SvgPicture.asset(
                      AppSvgs.kCopy,
                      // ignore: deprecated_member_use
                      color: AppColor.kWhiteColor,
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      );
    });
  }
}
