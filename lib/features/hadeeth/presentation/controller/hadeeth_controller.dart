import 'dart:convert';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/hadeeth/data/models/hadeeth_model.dart';

class HadeethController extends GetxController {
  static List<HadeethModel>? hadeethList;
  static RxInt currentIndex = 4.obs;
  @override
  onInit() {
    super.onInit();
    loadHadeeth();
    calculateHadeethIndex();
  }

  Future<void> loadHadeeth() async {
    String jsonString = await rootBundle.loadString(AppJsons.kHadeethData);
    List<dynamic> jsonData = json.decode(jsonString);
    hadeethList = jsonData.map((item) => HadeethModel.fromJson(item)).toList();
    update();
  }

  Future<void> calculateHadeethIndex() async {
    DateTime installDate;
    int differenceDaysCount;
    if (cacheMemory.read(INSTALL_DATE) == null) {
      await cacheMemory.write(INSTALL_DATE, DateTime.now().toString());
      installDate = DateTime.now();
      differenceDaysCount = 0;
    } else {
      installDate = DateTime.parse(cacheMemory.read(INSTALL_DATE));
      differenceDaysCount = DateTime.now().difference(installDate).inDays;
    }
    currentIndex.value += differenceDaysCount;
  }
}
