import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_date.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/hadeeth/presentation/controller/hadeeth_controller.dart';
import 'package:salawati/features/hadeeth/presentation/widgets/hadeeth_item_builder.dart';

class HadeethScreen extends StatelessWidget {
  const HadeethScreen({super.key});
  @override
  Widget build(BuildContext context) {
    String locale = cacheMemory.read('lang') ?? 'ar';

    Get.put(HadeethController());
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AppImages.kBg,
            fit: BoxFit.fitWidth,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 28.w, vertical: 0.1.sh),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SettingsShareRow(),
                    16.verticalSpace,
                    CustomText(
                      'Today Hadith',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    CustomDate(fontSize: 17.sp),
                    const CustomDate(isHijry: true),
                    16.verticalSpace,
                    GetBuilder<HadeethController>(
                      builder: (controller) =>
                          HadeethController.hadeethList == null
                              ? SizedBox(
                                  height: 0.25.sh,
                                  width: double.infinity,
                                  child: const CustomShimmer())
                              : const HadeethItemBuilder(),
                    ),
                    16.verticalSpace,
                    CustomText(
                      'Past days',
                      style: TextStyle(
                        fontSize: 22.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    16.verticalSpace,
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: 4,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 8.w,
                        mainAxisSpacing: 8.w,
                        childAspectRatio: 1,
                      ),
                      itemBuilder: (BuildContext context, int index) {
                        return InkWell(
                          onTap: () {
                            Get.dialog(
                              AlertDialog(
                                contentPadding: EdgeInsets.zero,
                                content: GetBuilder<HadeethController>(
                                  builder: (controller) =>
                                      HadeethController.hadeethList == null
                                          ? SizedBox(
                                              height: 0.25.sh,
                                              width: double.infinity,
                                              child: const CustomShimmer())
                                          : HadeethItemBuilder(
                                              shiftIndex: index + 1),
                                ),
                              ),
                            );
                          },
                          child: SmoothEdgesContainer(
                            color: AppColor.kRectangleColor,
                            borderRadius: BorderRadius.circular(60.r),
                            padding: EdgeInsets.symmetric(
                                vertical: 16.h, horizontal: 14.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomText(
                                  DateFormat('EEEE', locale).format(
                                      DateTime.now()
                                          .subtract(Duration(days: index + 1))),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                CustomText(
                                  DateFormat('MMMM d', locale).format(
                                      DateTime.now()
                                          .subtract(Duration(days: index + 1))),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                    0.15.sh.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
