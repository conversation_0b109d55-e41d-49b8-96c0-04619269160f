import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/permissions/presentation/controller/permission_flow_controller.dart';
import 'package:salawati/features/permissions/presentation/widgets/permission_card.dart';

class BatteryOptimizationPermissionScreen
    extends GetView<PermissionFlowController> {
  const BatteryOptimizationPermissionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),

            // Content
            Column(
              children: [
                // Header
                _buildHeader(),

                // Progress indicator
                _buildProgressIndicator(),

                32.verticalSpace,

                // Permission card
                Expanded(
                  child: Center(
                    child: SingleChildScrollView(
                      child: Obx(() => PermissionCard(
                            icon: controller.getStepIcon(
                                PermissionStep.batteryOptimization),
                            title: controller.getStepTitle(
                                PermissionStep.batteryOptimization),
                            description: controller.getStepDescription(
                                PermissionStep.batteryOptimization),
                            buttonText: controller.getStepButtonText(
                                PermissionStep.batteryOptimization),
                            onAllow: controller.requestCurrentPermission,
                            onSkip: controller.canSkip
                                ? controller.skipCurrentPermission
                                : null,
                            isLoading: controller.isLoading,
                            errorMessage: controller.errorMessage.isEmpty
                                ? null
                                : controller.errorMessage,
                            onClearError: controller.clearError,
                            canSkip: controller.canSkip,
                          )),
                    ),
                  ),
                ),

                // Skip all button
                _buildSkipAllButton(),

                24.verticalSpace,
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppColor.kRectangleColor.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.arrow_back,
                color: AppColor.kGreyColor,
                size: 20.sp,
              ),
            ),
          ),

          Expanded(
            child: CustomText(
              'setup_permissions',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppColor.kOrangeColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: () => _showSkipDialog(),
            child: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppColor.kRectangleColor.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                color: AppColor.kGreyColor,
                size: 20.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // Progress bar
          Obx(() {
            final progress = controller.getProgress();
            return Container(
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColor.kGreyColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progress,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColor.kOrangeColor,
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
              ),
            );
          }),

          8.verticalSpace,

          // Step indicator
          CustomText(
            'Step 3 of 3',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColor.kGreyColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkipAllButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: TextButton(
        onPressed: () => _showSkipDialog(),
        child: CustomText(
          'skip_all_permissions',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.kGreyColor,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }

  void _showSkipDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: CustomText(
          'skip_permissions_title',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: CustomText(
          'skip_permissions_message',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.kGreyColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: CustomText(
              'Cancel',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.kGreyColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.skipAllPermissions();
            },
            child: CustomText(
              'skip_all',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.kOrangeColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
