import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:salawati/core/services/permission_manager.dart';
import 'package:salawati/core/utils/app_router.dart';

enum PermissionStep {
  location,
  notification,
  batteryOptimization,
  complete,
}

class PermissionFlowController extends GetxController {
  final PermissionManager _permissionManager = Get.find<PermissionManager>();

  // Reactive state
  final Rx<PermissionStep> _currentStep = PermissionStep.location.obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxBool _canSkip = true.obs;

  // Getters
  PermissionStep get currentStep => _currentStep.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  bool get canSkip => _canSkip.value;

  @override
  void onInit() {
    super.onInit();
    _permissionManager.startPermissionFlow();
  }

  Future<void> requestCurrentPermission() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      PermissionResult result;

      switch (_currentStep.value) {
        case PermissionStep.location:
          result = await _permissionManager.requestLocationPermission();
          break;
        case PermissionStep.notification:
          result = await _permissionManager.requestNotificationPermission();
          break;
        case PermissionStep.batteryOptimization:
          result = await _permissionManager.requestBatteryOptimizationPermission();
          break;
        case PermissionStep.complete:
          _completeFlow();
          return;
      }

      if (result.granted) {
        _moveToNextStep();
      } else if (result.permanentlyDenied) {
        _errorMessage.value = _getPermanentlyDeniedMessage(_currentStep.value);
      } else if (result.errorMessage != null) {
        _errorMessage.value = result.errorMessage!;
      } else {
        _errorMessage.value = _getDeniedMessage(_currentStep.value);
      }
    } catch (e) {
      debugPrint('Error requesting permission: $e');
      _errorMessage.value = 'An unexpected error occurred. Please try again.';
    } finally {
      _isLoading.value = false;
    }
  }

  void skipCurrentPermission() {
    _permissionManager.skipPermission(_getPermissionType(_currentStep.value));
    _moveToNextStep();
  }

  void _moveToNextStep() {
    switch (_currentStep.value) {
      case PermissionStep.location:
        _currentStep.value = PermissionStep.notification;
        break;
      case PermissionStep.notification:
        _currentStep.value = PermissionStep.batteryOptimization;
        break;
      case PermissionStep.batteryOptimization:
        _currentStep.value = PermissionStep.complete;
        break;
      case PermissionStep.complete:
        _completeFlow();
        break;
    }
    _errorMessage.value = '';
  }

  void _completeFlow() {
    _permissionManager.completePermissionFlow();
    Get.offAllNamed(AppRouter.kLayoutScreen);
  }

  void skipAllPermissions() {
    _permissionManager.skipPermissionFlow();
    Get.offAllNamed(AppRouter.kLayoutScreen);
  }

  PermissionType _getPermissionType(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return PermissionType.location;
      case PermissionStep.notification:
        return PermissionType.notification;
      case PermissionStep.batteryOptimization:
        return PermissionType.batteryOptimization;
      case PermissionStep.complete:
        throw Exception('Complete step does not have a permission type');
    }
  }

  String _getPermanentlyDeniedMessage(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return 'location_permission_permanently_denied'.tr;
      case PermissionStep.notification:
        return 'notification_permission_permanently_denied'.tr;
      case PermissionStep.batteryOptimization:
        return 'battery_optimization_permanently_denied'.tr;
      case PermissionStep.complete:
        return '';
    }
  }

  String _getDeniedMessage(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return 'location_permission_denied'.tr;
      case PermissionStep.notification:
        return 'notification_permission_denied'.tr;
      case PermissionStep.batteryOptimization:
        return 'battery_optimization_denied'.tr;
      case PermissionStep.complete:
        return '';
    }
  }

  String getStepTitle(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return 'location_access'.tr;
      case PermissionStep.notification:
        return 'notifications'.tr;
      case PermissionStep.batteryOptimization:
        return 'battery_optimization'.tr;
      case PermissionStep.complete:
        return 'all_set'.tr;
    }
  }

  String getStepDescription(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return 'location_permission_description'.tr;
      case PermissionStep.notification:
        return 'notification_permission_description'.tr;
      case PermissionStep.batteryOptimization:
        return 'battery_optimization_description'.tr;
      case PermissionStep.complete:
        return 'permission_complete_description'.tr;
    }
  }

  String getStepButtonText(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return 'allow_location'.tr;
      case PermissionStep.notification:
        return 'allow_notifications'.tr;
      case PermissionStep.batteryOptimization:
        return 'optimize_battery'.tr;
      case PermissionStep.complete:
        return 'get_started'.tr;
    }
  }

  String getStepIcon(PermissionStep step) {
    switch (step) {
      case PermissionStep.location:
        return '📍';
      case PermissionStep.notification:
        return '🔔';
      case PermissionStep.batteryOptimization:
        return '🔋';
      case PermissionStep.complete:
        return '✅';
    }
  }

  double getProgress() {
    switch (_currentStep.value) {
      case PermissionStep.location:
        return 0.33;
      case PermissionStep.notification:
        return 0.66;
      case PermissionStep.batteryOptimization:
        return 0.90;
      case PermissionStep.complete:
        return 1.0;
    }
  }

  void clearError() {
    _errorMessage.value = '';
  }

  void goToSettings() {
    // This will be implemented to open app settings
    // For now, just clear the error and allow user to try again
    _errorMessage.value = '';
  }
}
