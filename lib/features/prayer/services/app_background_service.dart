// import 'dart:ui';

// import 'package:flutter/material.dart';
// import 'package:flutter_background_service/flutter_background_service.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'dart:async';

// import 'package:get/get.dart';
// import 'package:salawati/core/utils/app_functions.dart';
// import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
// import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
// import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

// class AppBackgroundService {
//   final service = FlutterBackgroundService();
//   Future<void> init() async {
//     await service.configure(
//       iosConfiguration: IosConfiguration(
//         autoStart: true,
//         onForeground: onStart,
//         onBackground: onIosBackground,
//       ),
//       androidConfiguration: AndroidConfiguration(
//         autoStart: true,
//         onStart: onStart,
//         isForegroundMode: false,
//         autoStartOnBoot: true,
//       ),
//     );

//     var result = await service.startService();
//     debugPrint("startService result: $result");
//   }
// }

// Future<void> updateWidgetData() async {
//   AppFunctions.initializeDateLocales();
//   if (!Get.isRegistered<SettingsController>()) {
//     Get.put<SettingsController>(SettingsController());
//   }
//   if (!Get.isRegistered<PrayerController>()) {
//     Get.put(await PrayerController().init());
//   }
//   PrayerTimesHomeWidget.updatePrayerTimeWidget(PrayerController.instance);
// }

// @pragma('vm:entry-point')
// Future<bool> onIosBackground(ServiceInstance service) async {
//   WidgetsFlutterBinding.ensureInitialized();
//   DartPluginRegistrant.ensureInitialized();

//   FlutterLocalNotificationsPlugin().show(
//     13124124,
//     "اشعار تجريبي من الخلفية",
//     "الوقت الحالي للصلاة هو ${DateTime.now().second}",
//     const NotificationDetails(
//         android: AndroidNotificationDetails(
//       "prayer alarm",
//       "منبه الصلاة",
//       icon: '@mipmap/ic_launcher',
//       ongoing: false,
//     )),
//   );
//   updateWidgetData();
//   return true;
// }

// @pragma('vm:entry-point')
// void onStart(ServiceInstance service) async {
//   WidgetsFlutterBinding.ensureInitialized();
//   DartPluginRegistrant.ensureInitialized();
//   Timer.periodic(const Duration(minutes: 1), (timer) {
//     updateWidgetData();
//   });
// }
