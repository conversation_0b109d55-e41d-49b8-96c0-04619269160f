import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/prayer/presentation/screens/custom_timer.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../controller/prayer_controller.dart';

class PrayerProgressController extends GetxController {
  static PrayerProgressController get instance => Get.find();

  final RxDouble progress = 0.0.obs;
  final RxString previousPrayerName = ''.obs;
  final RxString nextPrayerName = ''.obs;

  Rxn<DateTime> previousPrayerTime = Rxn<DateTime>();
  Rxn<DateTime> nextPrayerTime = Rxn<DateTime>();
  Rxn<Duration> reminingTime = Rxn<Duration>();

  final Rxn<Duration> timeSincePrevious = Rxn<Duration>();

  // print(previousPrayer);
  // print(nextPrayer);
  Timer? _timer;

  @override
  void onInit() {
    super.onInit();
    _updateProgress();
    _timer =
        Timer.periodic(const Duration(seconds: 1), (_) => _updateProgress());
  }

  Future<void> _updateProgress() async {
    tz.initializeTimeZones();
    final currentTimeZone = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(currentTimeZone));

    final prayerController = Get.find<PrayerController>();
    var nowLocal = DateTime.parse(
      tz.TZDateTime.now(tz.local)
          .toIso8601String()
          .replaceFirst('z', '')
          .replaceFirst('Z', ''),
    );

    final prayerTimes = prayerController
        .calculatePrayerTimesByDateTimeForNotification(nowLocal);
    // final previousPrayer = AppFunctions.getPrayerTime('fajr', prrr);
    // final nextPrayer = AppFunctions.getPrayerTime('dhuhr', prrr);

    final previousPrayer = AppFunctions.previousPrayerTime(prayerTimes);
    final nextPrayer = AppFunctions.nextPrayerTime(prayerTimes);
    previousPrayerName.value =
        AppFunctions.previousPrayer(prayerTimes).name.capitalizeFirst ?? '';
    nextPrayerName.value =
        AppFunctions.nextPrayer(prayerTimes).name.capitalizeFirst ?? '';

    var startLocal = DateTime.parse(
      previousPrayer
          .toIso8601String()
          .replaceFirst('z', '')
          .replaceFirst('Z', ''),
    );
    var endLocal = DateTime.parse(
      nextPrayer.toIso8601String().replaceFirst('z', '').replaceFirst('Z', ''),
    );

    previousPrayerTime.value = previousPrayer;
    nextPrayerTime.value = nextPrayer;
    reminingTime.value = endLocal.difference(nowLocal);
    timeSincePrevious.value = nowLocal.difference(startLocal);

    double p;
    if (nowLocal.isBefore(startLocal)) {
      p = 0.0;
    } else if (nowLocal.isAfter(endLocal)) {
      p = 1.0;
    } else {
      final totalSeconds = endLocal.difference(startLocal).inSeconds;
      final elapsedSeconds = nowLocal.difference(startLocal).inSeconds;

      p = elapsedSeconds / totalSeconds;
    }
    progress.value = p.clamp(0.0, 1.0);
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }
}

class PrayerProgressBar extends StatefulWidget {
  const PrayerProgressBar({super.key});

  @override
  State<PrayerProgressBar> createState() => _PrayerProgressBarState();
}

class _PrayerProgressBarState extends State<PrayerProgressBar>
    with SingleTickerProviderStateMixin {
  final RxBool showTimeUntilNext = true.obs;
  late AnimationController _animationController;
  late Animation<double> _animation;
  double _previousProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    // Initial dummy animation
    _animation = Tween<double>(begin: 0.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startAnimation(double newProgress) {
    _animationController.stop();
    _animationController.reset();
    _animation =
        Tween<double>(begin: _previousProgress, end: newProgress).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _animationController.forward().whenComplete(() {
      _previousProgress = newProgress;
    });
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PrayerProgressController());

    return Obx(() {
      final currentProgress = controller.progress.value;
      if (!_animationController.isAnimating &&
          currentProgress != _previousProgress) {
        _startAnimation(currentProgress);
      }
      // Get the localized text with prayer name
      final String prayerName = showTimeUntilNext.value
          ? controller.nextPrayerName.value.tr
          : controller.previousPrayerName.value.tr;

      // Get the localized text format
      final String textFormat = showTimeUntilNext.value
          ? 'Time until next %s'
          : 'Time since previous %s';

      // Create the final text with localized numbers if needed
      final text = textFormat.trArgs([prayerName]);
      return AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return GestureDetector(
            onTap: () {
              if (_animationController.isAnimating) return;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                showTimeUntilNext.value = !showTimeUntilNext.value;
              });
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CustomText(
                  text,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                4.verticalSpace,
                if (controller.reminingTime.value != null)
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: TimerCountdown(
                      format: CountDownTimerFormat.daysHoursMinutesSeconds,
                      timeTextStyle: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(
                              fontSize: 48,
                              color: AppColor.kWhiteColor,
                              fontWeight: FontWeight.bold),
                      colonsTextStyle: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(
                              fontSize: 48,
                              color: AppColor.kWhiteColor,
                              fontWeight: FontWeight.bold),
                      spacerWidth: 2,
                      enableDescriptions: false,
                      endTime: showTimeUntilNext.value
                          ? DateTime.now().add(controller.reminingTime.value!)
                          : DateTime.now()
                              .add(controller.timeSincePrevious.value!),
                    ),
                  ),
              ],
            ),
          );
        },
      );
    });
  }
}

class PrayerNameAndTime extends StatelessWidget {
  const PrayerNameAndTime({
    super.key,
    required this.prayerTime,
    required this.prayerName,
  });

  final DateTime prayerTime;
  final String prayerName;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        CustomText(
          prayerName,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white,
          ),
        ),
        const SizedBox(width: 6),
        CustomText(
          AppFunctions.formatTimeWithoutAMPM(prayerTime),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            color: Colors.white,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 3),
          child: CustomText(
            AppFunctions.formatTimeAmPMArabic(
              prayerTime,
            ),
            style: const TextStyle(
              fontSize: 8,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
