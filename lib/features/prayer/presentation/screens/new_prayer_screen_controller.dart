// import 'dart:async';

// // import 'package:alarm/service/alarm_storage.dart';
// import 'package:flutter/material.dart';

// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:home_widget/home_widget.dart';

// import 'package:salawati/core/utils/location_controller.dart';

// import '../../../../core/utils/app_functions.dart';
// import '../../../home_widget/prayer_times_home_widget.dart';
// import '../../../settings/presentation/controller/settings_controller.dart';
// import '../controller/prayer_controller.dart';

// class PrayerWidgetController extends GetxController {
//   static PrayerWidgetController get instance => Get.find();
//   RxBool isInitialized = false.obs;
//   RxList<HomeWidgetInfo> installedWidgets = <HomeWidgetInfo>[].obs;

//   @override
//   void onInit() async {
//     super.onInit();

//     await importentInitialization();

//     debugPrint('amin PrayerWidgetController.oninit() started ');

//     if (!Get.isRegistered<PrayerController>()) {
//       Get.put(PrayerController());
//     }
//     if (!Get.isRegistered<SettingsController>()) {
//       Get.put(
//           SettingsController(prayerController: Get.find<PrayerController>()),
//           permanent: true);
//     }
//     await prayerControllerInitialization();
//   }

//   Future<void> prayerControllerInitialization() async {
//     // Initialize location first
//     await Get.find<LocationController>().checkStatus();

//     // Then initialize prayer controller
//     final controller = PrayerController();
//     await controller.init();

//     await PrayerWidgetService.updatePrayerTimeWidget(controller);
//   }

//   Future<void> importentInitialization() async {
//     debugPrint('amin importentInitialization started ');

//     await AppFunctions.initializeDateLocales();
//     // await AlarmStorage.init();
//     await GetStorage.init();
//   }
// }
