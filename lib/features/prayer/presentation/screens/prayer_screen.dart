import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/prayer/presentation/widgets/prayers_grid_view.dart';

import '../widgets/prayer_background_body.dart';
import 'prayer_screen_widget.dart';

class PrayerScreen extends GetView<PrayerController> {
  const PrayerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize controllers
    // Get.put(LocationController());
    // final prayerController = Get.put(PrayerController());
    var screenSize = MediaQuery.sizeOf(context);

    return Scaffold(
      body: Stack(
        children: [
          // Background Image
          Padding(
            padding: EdgeInsets.only(bottom: screenSize.height / 1.50),
            child: Clip<PERSON>ath(
              clipper: const ShapeBorderClipper(
                shape: ContinuousRectangleBorder(
                  borderRadius: BorderRadius.vertical(
                    bottom: Radius.circular(
                      60,
                    ),
                  ),
                ),
              ),
              child: Image.asset(
                'assets/images/makkah.png',
                height: double.infinity,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
          ),
          // Main Content
          Column(
            children: [
              // Prayer Background Body
              PrayerBackgroundBody(controller: controller),
              // WidgetSwitcherButton(
              //     widget1: PrayerBackgroundBody2(controller: controller),
              //     widget2: PrayerBackgroundBody(controller: controller)),
              // Expanded Content
              Expanded(
                child: SmoothEdgesContainer(
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(60.r)),
                  width: double.infinity,
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: Stack(
                    children: [
                      // Background Image for Content
                      Image.asset(
                        width: double.infinity,
                        AppImages.kBg,
                        fit: BoxFit.cover,
                      ),

                      // Scrollable Content
                      GetBuilder<PrayerController>(
                        builder: (prayerController) {
                          if (prayerController.prayerTimes.value == null) {
                            // Show shimmer loading effect
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 32.h),
                              child: PanalCustomShimmer(
                                isLoading:
                                    prayerController.prayerTimes.value == null,
                              ),
                            );
                          }

                          // Main Content
                          return ListView(
                            padding: EdgeInsets.zero,
                            children: [
                              // Next Prayer Widget
                              // HomeNextPrayerWidget(
                              //     controller: prayerController),

                              0.015.sh.verticalSpace,
                              // Prayers Grid View
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 16.w),
                                child: const PrayersGridView(),
                              ),
                              // Add extra space at the bottom
                              0.15.sh.verticalSpace,
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// class PrayerScreen extends StatefulWidget {
//   const PrayerScreen({super.key});

//   @override
//   State<PrayerScreen> createState() => _PrayerScreenState();
// }

// class _PrayerScreenState extends State<PrayerScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         Image.asset(
//           AppImages.kMainbg,
//           height: double.infinity,
//           width: double.infinity,
//           fit: BoxFit.fill,
//         ),
//         Column(
//           children: [
//             const PrayerBackgroundBody(),

//             //panel
//             Expanded(
//               child: SmoothEdgesContainer(
//                 borderRadius: BorderRadius.vertical(top: Radius.circular(60.r)),
//                 width: double.infinity,
//                 color: Theme.of(context).scaffoldBackgroundColor,
//                 child: Stack(
//                   children: [
//                     //background za5rafeh
//                     Image.asset(
//                       width: double.infinity,
//                       AppImages.kBg,
//                       fit: BoxFit.cover,
//                     ),
//                     // panel body
//                     SingleChildScrollView(
//                       child: GetBuilder<PrayerController>(
//                         builder: (controller) {
//                           if (controller.prayerTimes == null) {
//                             return Padding(
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: 16, vertical: 32.h),
//                               child: const PanalCustomShimmer(),
//                             );
//                           }
//                           return Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               HomeNextPrayerWidget(controller: controller),
//                               8.verticalSpace,

//                               // test custom widget
//                               TextButton(
//                                 onPressed: () async {
//                                   FlutterBackgroundService()
//                                       .invoke('setAsForeground');
//                                 },
//                                 child: const CustomText("setAsForeground"),
//                               ),
//                               TextButton(
//                                 onPressed: () async {
//                                   FlutterBackgroundService()
//                                       .invoke('setAsBackground');
//                                 },
//                                 child: const CustomText("setAsBackground"),
//                               ),
//                               const TestFlutterBackgroundServicesButton(),
//                               Padding(
//                                 padding: EdgeInsets.symmetric(horizontal: 16.w),
//                                 child: const PrayersGridView(),
//                               ),
//                               0.15.sh.verticalSpace,
//                             ],
//                           );
//                         },
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }

class WidgetSwitcherButton extends StatefulWidget {
  final Widget widget1;
  final Widget widget2;

  const WidgetSwitcherButton({
    super.key,
    required this.widget1,
    required this.widget2,
  });

  @override
  WidgetSwitcherButtonState createState() => WidgetSwitcherButtonState();
}

class WidgetSwitcherButtonState extends State<WidgetSwitcherButton> {
  bool _showFirstWidget = true;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showFirstWidget = !_showFirstWidget;
        });
      },
      child: _showFirstWidget ? widget.widget1 : widget.widget2,
    );
  }
}
