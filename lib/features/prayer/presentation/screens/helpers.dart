// import 'package:adhan/adhan.dart';
// import 'package:circular_countdown_timer/circular_countdown_timer.dart';

// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get_state_manager/src/simple/get_state.dart';
// import 'package:get/get_utils/get_utils.dart';
// import 'package:salawati/core/utils/app_functions.dart';
// import 'package:intl/intl.dart' as intl;

// import '../../../../core/utils/app_color.dart';
// import '../../../../core/utils/app_consts.dart';
// import '../../../../core/utils/custom_text.dart';
// import '../controller/prayer_controller.dart';

// class PrayersCicleClock extends StatefulWidget {
//   const PrayersCicleClock({super.key});

//   @override
//   State<PrayersCicleClock> createState() => _PrayersCicleClockState();
// }

// class _PrayersCicleClockState extends State<PrayersCicleClock> {
//   final CountDownController _controller = CountDownController();

//   @override
//   void initState() {
//     super.initState();

//     _controller.start();
//   }

//   @override
//   Widget build(BuildContext context) {
//     var isRtl = cacheMemory.read('lang') == 'ar';

//     return GetBuilder<PrayerController>(builder: (controller) {
//       if (controller.prayerTimes == null) {
//         // Show loading indicator or error message
//         return const Center(
//           child: CircularProgressIndicator(),
//         );
//       }

//       PrayerTimes prayerTimes = controller.prayerTimes!;
//       Map<String, DateTime> prayers = {
//         FAJR: prayerTimes.fajr,
//         SUNRISE: prayerTimes.sunrise,
//         DHUHR: prayerTimes.dhuhr,
//         ASR: prayerTimes.asr,
//         MAGHRIB: prayerTimes.maghrib,
//         ISHA: prayerTimes.isha,
//       };

//       var nextPrayer =
//           AppFunctions.nextPrayer(controller.prayerTimes!).name.capitalizeFirst;

//       var nextPrayerTime = AppFunctions.nextPrayerTime(controller.prayerTimes!);

//       // var nextPrayerTime = DateTime.parse('2024-09-18 06:00:00.000Z');

// // Transform nextPrayerTime1 to match nextPrayerTime format
//       var formatter = intl.DateFormat('yyyy-MM-dd HH:mm:ss');
//       var transformedNextPrayerTime = formatter.format(nextPrayerTime.toUtc());

//       var currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch ~/ 1000;
//       var nextPrayerTimeInSeconds =
//           DateTime.parse(transformedNextPrayerTime).millisecondsSinceEpoch ~/
//                   1000 +
//               1;

// // Calculate the difference in seconds
//       Duration timeDifference =
//           Duration(seconds: nextPrayerTimeInSeconds - currentTimeInSeconds);

//       bool isLessThanAnHour = timeDifference.inSeconds < 3600;
//       final now = DateTime.now();

//       // debugPrint('Transformed Next Prayer Time: $transformedNextPrayerTime');
//       // debugPrint('Is less than an hour? ${isLessThanAnHour ? "Yes" : "No"}');

//       return Directionality(
//         textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
//         child: Column(
//           children: [
//             for (var i = 0; i < prayers.length; i++)
//               if (nextPrayer == prayers.keys.toList()[i])
//                 Padding(
//                   padding: const EdgeInsets.all(8.0),
//                   child: Directionality(
//                     textDirection:
//                         isRtl ? TextDirection.rtl : TextDirection.rtl,
//                     child: Container(
//                       constraints: const BoxConstraints(maxWidth: 300),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.stretch,
//                         children: [
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               const Icon(
//                                 Icons.night_shelter,
//                                 size: 18,
//                                 color: AppColor.kLightBlueColor,
//                               ),
//                               const SizedBox(width: 10),
//                               Text(
//                                 'الصلاة التالية',
//                                 style: Theme.of(context).textTheme.titleSmall,
//                               ),
//                               CustomText(
//                                 '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}',
//                                 style: const TextStyle(color: Colors.black),
//                               ),
//                               const Spacer(),
//                               const Icon(Icons.arrow_forward_ios, size: 20),
//                             ],
//                           ),
//                           const SizedBox(
//                               height: 10), // Add some vertical spacing
//                           Row(
//                             children: [
//                               const Icon(Icons.wb_sunny_outlined, size: 20),
//                               const SizedBox(
//                                   width: 20), // Increase horizontal spacing
//                               Expanded(
//                                 flex: 2,
//                                 child: Text(
//                                   AppFunctions.nextPrayer(
//                                           controller.prayerTimes!)
//                                       .name
//                                       .capitalizeFirst!
//                                       .tr,
//                                   style:
//                                       Theme.of(context).textTheme.titleMedium,
//                                 ),
//                               ),
//                               const SizedBox(
//                                   width: 20), // Increase horizontal spacing
//                               Expanded(
//                                 flex: 4,
//                                 child: Text(
//                                   AppFunctions.formatTime(
//                                       AppFunctions.nextPrayerTime(
//                                           controller.prayerTimes!)),
//                                   style:
//                                       Theme.of(context).textTheme.titleMedium,
//                                 ),
//                               ),
//                               const SizedBox(
//                                   width: 20), // Increase horizontal spacing
//                               Container(
//                                 width: 45,
//                                 height: 45,
//                                 decoration: BoxDecoration(
//                                   borderRadius: BorderRadius.circular(50),
//                                   color: Colors.white,
//                                 ),
//                                 child: CircularCountDownTimer(
//                                   duration: nextPrayerTimeInSeconds,
//                                   initialDuration: currentTimeInSeconds,
//                                   controller: _controller,
//                                   width: 40,
//                                   height: 40,
//                                   ringColor: Colors.grey[300]!,
//                                   ringGradient: null,
//                                   fillColor: Colors.white,
//                                   fillGradient: null,
//                                   // backgroundColor: Colors.purple[500],
//                                   backgroundGradient: null,
//                                   strokeWidth: 1.0,
//                                   strokeCap: StrokeCap.round,
//                                   textStyle: const TextStyle(
//                                     fontSize: 10.0,
//                                     color: Colors.black,
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                   textAlign: TextAlign.center,
//                                   textFormat: isLessThanAnHour
//                                       ? CountdownTextFormat.MM_SS
//                                       : CountdownTextFormat.HH_MM_SS,
//                                   isReverse: true,
//                                   isReverseAnimation: true,
//                                   isTimerTextShown: true,
//                                   autoStart: true,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//           ],
//         ),
//       );
//     });
//   }
// }

// class MainPrayersCicleClock extends StatelessWidget {
//   const MainPrayersCicleClock({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<PrayerController>(builder: (controller) {
//       return controller.prayerDatePage.value == 0
//           ? Padding(
//               padding: EdgeInsets.symmetric(horizontal: 16.w),
//               // child: const PrayersGridView2(),
//               child: const PrayersCicleClock(),
//             )
//           : const SizedBox();
//     });
//   }
// }
