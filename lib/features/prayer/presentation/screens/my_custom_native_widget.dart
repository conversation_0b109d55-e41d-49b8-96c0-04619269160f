// import 'dart:async';
// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';

// import 'package:get/get.dart';
// import 'package:home_widget/home_widget.dart';

// import 'package:salawati/core/utils/app_color.dart';
// import 'package:salawati/features/prayer/presentation/widgets/prayers_grid_view.dart';
// import 'package:workmanager/workmanager.dart';

// import '../controller/prayer_controller.dart';
// import 'helpers.dart';

// const String appGroupId = 'group.prayer_widget';
// const String iOSWidgetName = 'prayer_widgetExtension';
// const String androidWidgetName = 'HomeWidgetExampleProvider';
// const String androidWidgetName2 = 'PrayerWidgetProvider';
// const String qualifiedAndroidName =
//     'com.salawati.app.HomeWidgetExampleProvider';
// const String qualifiedAndroidName2 = 'com.salawati.app.PrayerWidgetProvider';

// // /// Used for Background Updates using Workmanager Plugin
// // @pragma("vm:entry-point")
// // void callbackDispatcher() async {
// //   Workmanager().executeTask((taskName, inputData) {
// //     return Future.wait<bool?>([
// //       // HomeWidget.saveWidgetData(
// //       //   'title',
// //       //   'Updated from Background',
// //       // ),
// //       // HomeWidget.saveWidgetData(
// //       //   'message',
// //       //   '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}',
// //       // ),
// //     ]).then((value) async {
// //       Future.wait<bool?>([
// //         HomeWidget.updateWidget(
// //           name: androidWidgetName,
// //           iOSName: iOSWidgetName,
// //         ),
// //         if (Platform.isAndroid)
// //           HomeWidget.updateWidget(
// //             qualifiedAndroidName: qualifiedAndroidName,
// //           ),
// //       ]);

// //       return !value.contains(false);
// //     });
// //   });
// // }

// /// Called when Doing Background Work initiated from Widget
// @pragma("vm:entry-point")
// Future<void> interactiveCallback(Uri? data) async {
//   if (data?.host == 'titleclicked') {
//     // final greetings = [
//     //   'Hello',
//     //   'Hallo',
//     //   'Bonjour',
//     //   'Hola',
//     //   'Ciao',
//     //   '哈洛',
//     //   '안녕하세요',
//     //   'xin chào',
//     // ];
//     // final selectedGreeting = greetings[Random().nextInt(greetings.length)];
//     await HomeWidget.setAppGroupId(appGroupId);
//     // await HomeWidget.saveWidgetData<String>('title', selectedGreeting);
//     await HomeWidget.updateWidget(
//       name: androidWidgetName,
//       iOSName: iOSWidgetName,
//     );
//     await HomeWidget.updateWidget(
//       name: androidWidgetName2,
//       iOSName: iOSWidgetName,
//     );
//     if (Platform.isAndroid) {
//       await HomeWidget.updateWidget(
//         qualifiedAndroidName: qualifiedAndroidName,
//       );
//       await HomeWidget.updateWidget(
//         qualifiedAndroidName: qualifiedAndroidName2,
//       );
//     }
//   }
// }

// class MyCustomNativeWidget extends StatefulWidget {
//   const MyCustomNativeWidget({super.key});

//   @override
//   State<MyCustomNativeWidget> createState() => _MyCustomNativeWidgetState();
// }

// class _MyCustomNativeWidgetState extends State<MyCustomNativeWidget> {
//   bool _isRequestPinWidgetSupported = false;

//   @override
//   void initState() {
//     super.initState();
//     HomeWidget.setAppGroupId(appGroupId);
//     HomeWidget.registerInteractivityCallback(interactiveCallback);
//     _checkPinability();
//     _sendAndUpdate();
//   }

//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     _checkForWidgetLaunch();
//     HomeWidget.widgetClicked.listen(_launchedFromWidget);
//   }

//   @override
//   void dispose() {
//     super.dispose();
//   }

//   Future _sendData() async {
//     try {
//       return Future.wait([
//         HomeWidget.renderFlutterWidget(
//           Builder(builder: (context) {
//             return GetBuilder<PrayerController>(builder: (controller) {
//               return controller.prayerDatePage.value == 0
//                   ? const PrayersGridView2()
//                   : const SizedBox();
//             });
//           }),
//           logicalSize: const Size(330, 1000),
//           pixelRatio: 3,
//           key: 'widget1',
//         ),
//         HomeWidget.renderFlutterWidget(
//           Builder(builder: (context) {
//             return GetBuilder<PrayerController>(builder: (controller) {
//               return const PrayersCicleClock();
//             });
//           }),
//           logicalSize: const Size(320, 1000),
//           pixelRatio: 3,
//           key: 'widget2',
//         ),
//       ] as Iterable<Future>);
//     } on PlatformException catch (exception) {
//       debugPrint('Error Sending Data. $exception');
//     }
//   }

//   Future _updateWidget() async {
//     try {
//       return Future.wait([
//         HomeWidget.updateWidget(
//           name: androidWidgetName,
//           iOSName: iOSWidgetName,
//           qualifiedAndroidName: qualifiedAndroidName,
//         ),
//         HomeWidget.updateWidget(
//           name: androidWidgetName2,
//           iOSName: iOSWidgetName,
//           qualifiedAndroidName: qualifiedAndroidName2,
//         ),

//         // if (Platform.isAndroid)
//         //   HomeWidget.updateWidget(
//         //     qualifiedAndroidName: qualifiedAndroidName2,
//         //   ),
//       ]);
//     } on PlatformException catch (exception) {
//       debugPrint('Error Updating Widget. $exception');
//     }
//   }

//   // Future _loadData() async {
//   //   try {
//   //     return Future.wait([
//   //       HomeWidget.getWidgetData<String>('title', defaultValue: 'Default Title')
//   //           .then((value) => _titleController.text = value ?? ''),
//   //       HomeWidget.getWidgetData<String>(
//   //         'message',
//   //         defaultValue: 'Default Message',
//   //       ).then((value) => _messageController.text = value ?? ''),
//   //     ]);
//   //   } on PlatformException catch (exception) {
//   //     debugPrint('Error Getting Data. $exception');
//   //   }
//   // }

//   Future<void> _sendAndUpdate() async {
//     await _sendData();
//     await _updateWidget();
//   }

//   void _checkForWidgetLaunch() {
//     HomeWidget.initiallyLaunchedFromHomeWidget().then(_launchedFromWidget);
//   }

//   void _launchedFromWidget(Uri? uri) {
//     if (uri != null) {
//       showDialog(
//         context: context,
//         builder: (buildContext) => AlertDialog(
//           backgroundColor: AppColor.kDarkBlueColor,
//           title: const Text('App started from HomeScreenWidget'),
//           content: Text('Here is the URI: $uri'),
//         ),
//       );
//     }
//   }

//   void _startBackgroundUpdate() {
//     Workmanager().registerPeriodicTask(
//       '1',
//       'widgetBackgroundUpdate',
//       frequency: const Duration(minutes: 15),
//     );
//   }

//   void _stopBackgroundUpdate() {
//     Workmanager().cancelByUniqueName('1');
//   }

//   Future<void> _getInstalledWidgets() async {
//     try {
//       final widgets = await HomeWidget.getInstalledWidgets();
//       if (!mounted) return;

//       String getText(HomeWidgetInfo widget) {
//         if (Platform.isIOS) {
//           return 'iOS Family: ${widget.iOSFamily}, iOS Kind: ${widget.iOSKind}';
//         } else {
//           return 'Android Widget id: ${widget.androidWidgetId}, '
//               'Android Class Name: ${widget.androidClassName}, '
//               'Android Label: ${widget.androidLabel}';
//         }
//       }

//       await showDialog(
//         context: context,
//         builder: (buildContext) => AlertDialog(
//           title: const Text('Installed Widgets'),
//           content: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Text(
//                 'Number of widgets: ${widgets.length}',
//                 style: Theme.of(context)
//                     .textTheme
//                     .titleSmall
//                     ?.copyWith(color: Colors.black),
//               ),
//               const Divider(),
//               for (final widget in widgets)
//                 Text(
//                   getText(widget),
//                   style: Theme.of(context)
//                       .textTheme
//                       .titleSmall
//                       ?.copyWith(color: Colors.black),
//                 ),
//             ],
//           ),
//         ),
//       );
//     } on PlatformException catch (exception) {
//       debugPrint('Error getting widget information. $exception');
//     }
//   }

//   Future<void> _checkPinability() async {
//     final isRequestPinWidgetSupported =
//         await HomeWidget.isRequestPinWidgetSupported();
//     if (mounted) {
//       setState(() {
//         _isRequestPinWidgetSupported = isRequestPinWidgetSupported ?? false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('HomeWidget Example'),
//       ),
//       body: SingleChildScrollView(
//         child: Center(
//           child: Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Column(
//               children: [
//                 const MainPrayersCicleClock(),

//                 ElevatedButton(
//                   onPressed: _sendAndUpdate,
//                   child: const Text('Send Data to Widget'),
//                 ),
//                 // ElevatedButton(
//                 //   onPressed: _loadData,
//                 //   child: const Text('Load Data'),
//                 // ),
//                 ElevatedButton(
//                   onPressed: _checkForWidgetLaunch,
//                   child: const Text('Check For Widget Launch'),
//                 ),
//                 if (Platform.isAndroid)
//                   ElevatedButton(
//                     onPressed: _startBackgroundUpdate,
//                     child: const Text('Update in background'),
//                   ),
//                 if (Platform.isAndroid)
//                   ElevatedButton(
//                     onPressed: _stopBackgroundUpdate,
//                     child: const Text('Stop updating in background'),
//                   ),
//                 ElevatedButton(
//                   onPressed: _getInstalledWidgets,
//                   child: const Text('Get Installed Widgets'),
//                 ),
//                 if (_isRequestPinWidgetSupported)
//                   ElevatedButton(
//                     onPressed: () => HomeWidget.requestPinWidget(
//                       name: androidWidgetName,
//                       androidName: androidWidgetName,
//                       qualifiedAndroidName: qualifiedAndroidName,
//                     ),
//                     child: const Text('Pin Widget1'),
//                   ),
//                 if (_isRequestPinWidgetSupported)
//                   ElevatedButton(
//                     onPressed: () => HomeWidget.requestPinWidget(
//                       name: androidWidgetName2,
//                       androidName: androidWidgetName2,
//                       qualifiedAndroidName: qualifiedAndroidName2,
//                     ),
//                     child: const Text('Pin Widget2'),
//                   ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
