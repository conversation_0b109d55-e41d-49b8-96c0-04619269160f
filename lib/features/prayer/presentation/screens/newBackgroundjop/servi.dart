// import 'dart:async';
// import 'dart:io';
// import 'dart:math';
// import 'dart:ui';

// import 'package:flutter/material.dart';
// import 'package:flutter_background_service/flutter_background_service.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:get/get.dart';
// import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

// import '../new_prayer_screen_controller.dart';

// const iosNotificationInterval = Duration(minutes: 1); // 16 minute interval33
// const androidNotificationInterval = Duration(minutes: 1); // 1 minute interval33

// int getUniqueNotificationId = DateTime.now().millisecondsSinceEpoch % 1000000;
// const notificationChannelId = 'my_foreground';
// const notificationId = 888;

// Future<void> initializeService() async {
//   final service = FlutterBackgroundService();

//   AndroidNotificationChannel channel = const AndroidNotificationChannel(
//     notificationChannelId,
//     "منبه الصلاة",
//     importance: Importance.high,
//   );
//   final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();

//   await flutterLocalNotificationsPlugin
//       .resolvePlatformSpecificImplementation<
//           AndroidFlutterLocalNotificationsPlugin>()
//       ?.createNotificationChannel(channel);

//   await service.configure(
//       iosConfiguration: IosConfiguration(
//         autoStart: true,
//         onForeground: onStart,
//         onBackground: onIosBackground,
//       ),
//       androidConfiguration: AndroidConfiguration(
//         autoStartOnBoot: true,
//         // this will be executed when app is in foreground or background in separated isolate
//         onStart: onStart,
//         isForegroundMode: true,
//         // auto start service
//         autoStart: true,
//         notificationChannelId: notificationChannelId,
//         initialNotificationTitle: "منبة الصلاة شغال في الخلفية",
//         initialNotificationContent: "فعال",

//         foregroundServiceNotificationId: notificationId,
//         // foregroundServiceTypes: [AndroidForegroundType.mediaPlayback],
//       ));
//   // Log the result of configuring the service
//   debugPrint("Service configuration completed");

//   // Start the service
//   await service.startService();
//   debugPrint("Service started successfully");
// }

// @pragma('vm:entry-point')
// void onStart(ServiceInstance service) async {
//   debugPrint("Service started...");
//   DartPluginRegistrant.ensureInitialized();

//   final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();
//   service.on('setAsForeground').listen((event) {
//     debugPrint(event);
//     debugPrint("Set as foreground service");
//   }, onError: (e, s) {
//     debugPrint('error listening for foreground: $e, $s');
//   }, onDone: () {
//     debugPrint('foreground listen closed');
//   });
//   if (service is AndroidServiceInstance) {
//     service.on('setAsForeground').listen((event) {
//       service.setAsForegroundService();
//       debugPrint("Set as foreground service");
//     }, onError: (e, s) {
//       debugPrint('error listening for foreground: $e, $s');
//     }, onDone: () {
//       debugPrint('foreground listen closed');
//     });
//     service.on('setAsBackground').listen((event) {
//       service.setAsBackgroundService();
//       debugPrint("Set as setAsBackground service");
//     }, onError: (e, s) {
//       debugPrint('error listening for foreground: $e, $s');
//     }, onDone: () {
//       debugPrint('setAsBackground listen closed');
//     });
//   }
//   service.on('stopService').listen((event) {
//     service.stopSelf();
//     debugPrint("Stopped service");
//   }, onError: (e, s) {
//     debugPrint('error listening for stopService: $e, $s');
//   }, onDone: () {
//     debugPrint('stopService listen closed');
//   });

//   if (service is AndroidServiceInstance) {
//     if (await service.isForegroundService()) {
//       _startPeriodicNotifications(flutterLocalNotificationsPlugin);
//     }
//   }
//   if (service is IOSServiceInstance) {
//     _startPeriodicNotifications(flutterLocalNotificationsPlugin);
//   }

//   // if (service is IOSServiceInstance) {
//   //   _startPeriodicNotifications(flutterLocalNotificationsPlugin);
//   //   debugPrint("_startPeriodicNotifications ");
//   // }
// }

// @pragma('vm:entry-point')
// Future<bool> onIosBackground(ServiceInstance service) async {
//   WidgetsFlutterBinding.ensureInitialized();
//   DartPluginRegistrant.ensureInitialized();

//   debugPrint('onIosBackgroundFetch hit');
//   // Update your widget data here
//   // await PrayerTimesHomeWidget.updatePrayerTimeWidget();

//   return true;
// }

// void _startPeriodicNotifications(FlutterLocalNotificationsPlugin plugin) {
//   Timer.periodic(
//       Platform.isIOS ? iosNotificationInterval : androidNotificationInterval,
//       (timer) {
//     _showNotification(plugin);
//   });
// }

// void _showNotification(FlutterLocalNotificationsPlugin plugin) async {
//   debugPrint('amin badri');

//   if (!Get.isRegistered<PrayerWidgetController>()) {
//     Get.put(PrayerWidgetController());
//   }
//   final controller = Get.find<PrayerWidgetController>();
//   if (!Get.isRegistered<SettingsController>()) {
//     Get.put<SettingsController>(SettingsController());
//   }

//   final settingsController = Get.find<SettingsController>();

//   controller.onInit();
//   await settingsController.onInit();

//   await controller.prayerControllerInitialization();

//   int randomMinute = Random().nextInt(60);
//   String prayerTime =
//       "${DateTime.now().hour}:${randomMinute.toString().padLeft(2, '0')}";

//   plugin.show(
//     notificationId,
//     "اشعار تجريبي",
//     "الوقت الحالي للصلاة هو $prayerTime",
//     const NotificationDetails(
//         android: AndroidNotificationDetails(
//       "prayer alarm",
//       "منبه الصلاة",
//       icon: '@mipmap/ic_launcher',
//       ongoing: false,
//     )),
//   );
// }
