import 'package:flutter/material.dart';
// import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';

import '../../../../core/utils/app_functions.dart';
import '../../../../core/utils/custom_text.dart';
import '../../../../core/widgets/custom_date.dart';
import '../../../../core/widgets/shimmer.dart';
import '../controller/prayer_controller.dart';

class PanalCustomShimmer extends StatelessWidget {
  final bool isLoading;

  const PanalCustomShimmer({
    super.key,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    if (!isLoading) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 15,
          width: .35.sw,
          child: const CustomShimmer(),
        ),
        16.verticalSpace,
        SizedBox(
          height: 15,
          width: .5.sw,
          child: const CustomShimmer(),
        ),
        16.verticalSpace,
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 6,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8.w,
            mainAxisSpacing: 8.w,
          ),
          itemBuilder: (BuildContext context, int index) {
            return SmoothEdgesContainer(
              height: 90,
              width: 90,
              color: AppColor.kRectangleColor,
              borderRadius: BorderRadius.circular(60.r),
              child: const CustomShimmer(),
            );
          },
        ),
      ],
    );
  }
}

class HomeNextPrayerWidget extends StatelessWidget {
  final PrayerController controller;

  const HomeNextPrayerWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 20.verticalSpace,

          40.verticalSpace,
          if (controller.prayerDatePage.value == 0) ...[
            Obx(() {
              if (controller.prayerTimes.value == null) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              final nextPrayer =
                  AppFunctions.nextPrayer(controller.prayerTimes.value!);
              final nextPrayerTime =
                  AppFunctions.nextPrayerTime(controller.prayerTimes.value!);

              return CustomText(
                '${nextPrayer.name.capitalizeFirst!.tr} ${AppFunctions.formatTime(nextPrayerTime)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 30.sp,
                ),
              );
            }),

            // 10.verticalSpace,

            // const PrayerProgressBar(),
            Obx(() {
              if (controller.prayerTimes.value == null) {
                return const SizedBox.shrink();
              }

              return Obx(() => CustomText(
                    AppFunctions.formatDuration(controller.remainingTime.value),
                    style: const TextStyle(
                      color: AppColor.kOrangeColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ));
            }),
            8.verticalSpace,
          ] else ...[
            Builder(builder: (context) {
              DateTime dateTime = DateTime.now().add(Duration(
                days: Get.find<PrayerController>().prayerDatePage.value,
              ));
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomDate(isHijry: false, dateTime: dateTime),
                  CustomDate(isHijry: true, dateTime: dateTime),
                ],
              );
            }),
          ],
        ],
      ),
    );
  }
}
