import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/prayer_notification_widget.dart';

class PrayerGridTile extends StatefulWidget {
  const PrayerGridTile({
    super.key,
    required this.prayerName,
    required this.prayerTime,
    required this.isNotified,
    this.isNextPrayer = false,
  });

  final String prayerName;
  final DateTime prayerTime;
  final bool isNotified;
  final bool isNextPrayer;

  @override
  PrayerGridTileState createState() => PrayerGridTileState();
}

class PrayerGridTileState extends State<PrayerGridTile> {
  @override
  Widget build(BuildContext context) {
    final locationService = Get.find<LocationService>();
    final formattedTime = AppFunctions.formatTime(widget.prayerTime);
    return GetBuilder<SettingsController>(builder: (settingsController) {
      final isCurrentlyNotified = settingsController
          .athanNotificationsMap[widget.prayerName]!.isNotified;
      // locationService.toggleAutoLocation(true);
      return SmoothEdgesContainer(
        color: AppColor.kRectangleColor,
        borderRadius: BorderRadius.circular(60.r),
        child: Material(
          color: isCurrentlyNotified
              ? AppColor.kOrangeColor.withAlpha(76) // 0.3 * 255 = 76
              : Colors.transparent,
          child: InkWell(
            onTap: () async {
              // if (settingsController.isLocationAuto.value) {
              //   if (locationController.combinedState.value ==
              //       CombinedLocationState.disabled) {
              //     await locationController.handleLocationToggle(true);
              //   }
              // }
              // settingsController.toggleNotification(widget.prayerName);
              await Get.bottomSheet(
                GetBuilder<SettingsController>(builder: (settingsController) {
                  return PrayerNotificationWidgetForHome(
                      title: widget.prayerName);
                }),
                backgroundColor: AppColor.kScaffoldColor,
                shape: RoundedRectangleBorder(
                  // Customize shape (optional)
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20.r)),
                ),
                isScrollControlled: false,
              );
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Align(
                    alignment: AlignmentDirectional.topEnd,
                    child: GetBuilder<SettingsController>(
                      builder: (settingsController) {
                        final isNotified = settingsController
                            .athanNotificationsMap[widget.prayerName]!
                            .isNotified;
                        return SvgPicture.asset(
                          AppSvgs.kSetting,
                          colorFilter: isNotified
                              ? const ColorFilter.mode(
                                  AppColor.kOrangeColor, BlendMode.srcIn)
                              : const ColorFilter.mode(
                                  AppColor.kWhiteColor, BlendMode.srcIn),
                          height: 15.h,
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: AlignmentDirectional.centerStart,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(
                            widget.prayerName,
                            style: TextStyle(
                              color: AppColor.kWhiteColor.withAlpha(178), // 0.7 * 255 = 178
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          CustomText(
                            formattedTime,
                            style: const TextStyle(
                              color: AppColor.kWhiteColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}

class PrayerSunnahGridTile extends StatefulWidget {
  const PrayerSunnahGridTile({
    super.key,
    required this.prayerName,
    required this.prayerTime,
    required this.isNotified,
    this.isNextPrayer = false,
  });

  final String prayerName;
  final DateTime prayerTime;
  final bool isNotified;
  final bool isNextPrayer;

  @override
  PrayerSunnahGridTileState createState() => PrayerSunnahGridTileState();
}

class PrayerSunnahGridTileState extends State<PrayerSunnahGridTile> {
  final _showActivationOverlay = false;
  final _isActiveIcon = false;
  @override
  Widget build(BuildContext context) {
    // final locationController = Get.find<LocationController>();
    final formattedTime = AppFunctions.formatTime(widget.prayerTime);
    return GetBuilder<SettingsController>(builder: (settingsController) {
      final isCurrentlyNotified = settingsController
          .athanNotificationsMap[widget.prayerName]!.isNotified;
      // LocationService.instance.toggleAutoLocation(true);
      return PrayerTileProgressOverlay(
        activatedIcon:
            _isActiveIcon ? AppSvgs.kNotification : AppSvgs.kMuteAlarm,
        isIconVisible: _showActivationOverlay,
        child: GestureDetector(
          onTap: () {
            Get.bottomSheet(
              GetBuilder<SettingsController>(builder: (settingsController) {
                return SunnahPrayerNotificationWidgetForHome(
                    title: widget.prayerName);
              }),
              backgroundColor: AppColor.kScaffoldColor,
              shape: RoundedRectangleBorder(
                // Customize shape (optional)
                borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
              ),
              isScrollControlled: false,
            );
          },
          child: SmoothEdgesContainer(
            color: AppColor.kRectangleColor,
            borderRadius: BorderRadius.circular(30.r),
            child: Material(
              color: isCurrentlyNotified
                  ? AppColor.kOrangeColor.withAlpha(76) // 0.3 * 255 = 76
                  : Colors.transparent,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: AlignmentDirectional.centerStart,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AutoSizeText(widget.prayerName.tr,
                              maxLines: 1,
                              maxFontSize: 18,
                              style: TextStyle(
                                color: AppColor.kWhiteColor.withAlpha(178), // 0.7 * 255 = 178
                                fontWeight: FontWeight.bold,
                                fontSize: 12.sp,
                                fontFamily: 'Tajawal',
                              )),
                          CustomText(
                            formattedTime,
                            style: const TextStyle(
                              color: AppColor.kWhiteColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    GetBuilder<SettingsController>(
                      builder: (settingsController) {
                        final isNotified = settingsController
                            .athanNotificationsMap[widget.prayerName]!
                            .isNotified;
                        return SvgPicture.asset(
                          AppSvgs.kSetting,
                          colorFilter: isNotified
                              ? const ColorFilter.mode(
                                  AppColor.kOrangeColor, BlendMode.srcIn)
                              : const ColorFilter.mode(
                                  AppColor.kWhiteColor, BlendMode.srcIn),
                          height: 15.h,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
// class TimeLinearProgressIndicator extends StatefulWidget {
//   const TimeLinearProgressIndicator({
//     super.key,
//     required this.startTime,
//     required this.endTime,
//   });

//   final DateTime startTime;
//   final DateTime endTime;

//   @override
//   _TimeLinearProgressIndicatorState createState() =>
//       _TimeLinearProgressIndicatorState();
// }

// class _TimeLinearProgressIndicatorState
//     extends State<TimeLinearProgressIndicator> {
//   late Duration totalDuration;
//   double progressValue = 0.0;
//   Timer? timer;

//   @override
//   void initState() {
//     super.initState();
//     initializeTimer();
//   }

//   @override
//   void didUpdateWidget(TimeLinearProgressIndicator oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     // Re-initialize the timer if startTime or endTime changes
//     if (widget.startTime != oldWidget.startTime ||
//         widget.endTime != oldWidget.endTime) {
//       initializeTimer();
//     }
//     _checkForWidgetLaunch();
//     HomeWidget.widgetClicked.listen(_launchedFromWidget);
//   }

//   void _checkForWidgetLaunch() {
//     HomeWidget.initiallyLaunchedFromHomeWidget().then(_launchedFromWidget);
//   }

//   void _launchedFromWidget(Uri? uri) {
//     if (uri != null) {
//       showDialog(
//         context: context,
//         builder: (buildContext) => AlertDialog(
//           title: const Text('App started from HomeScreenWidget'),
//           content: Text('Here is the URI: $uri'),
//         ),
//       );
//     }
//   }

//   void initializeTimer() {
//     timer?.cancel(); // Cancel any existing timer before starting a new one
//     totalDuration = widget.endTime.difference(widget.startTime);

//     // Immediately set initial progress value to avoid delay on widget build
//     updateProgress();

//     startTimer();
//   }

//   @override
//   void dispose() {
//     timer?.cancel(); // Cancel the timer to prevent memory leaks
//     super.dispose();
//   }

//   void startTimer() {
//     timer = Timer.periodic(const Duration(milliseconds: 100), (Timer t) {
//       // Update every 100 milliseconds for smooth progress
//       if (mounted) {
//         updateProgress();
//       } else {
//         timer?.cancel(); // Cancel the timer if the widget is disposed
//       }
//     });
//   }

//   void updateProgress() {
//     setState(() {
//       final now = DateTime.now();
//       if (now.isBefore(widget.endTime)) {
//         final elapsedDuration = now.difference(widget.startTime);
//         progressValue =
//             elapsedDuration.inMilliseconds / totalDuration.inMilliseconds;
//       } else {
//         progressValue =
//             1.0; // If current time is past end time, set progress to 1.0
//         timer?.cancel(); // Stop the timer when progress is complete
//       }
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return LinearProgressIndicator(
//       // Directly return LinearProgressIndicator
//       value: progressValue,
//       minHeight: 10, // Adjust height as needed
//     );
//   }

//   String formatTime(DateTime dateTime) {
//     return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
//   }
// }

class PrayerTileProgressOverlay extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final String? activatedIcon;
  final bool isIconVisible;
  final double? iconSize;

  const PrayerTileProgressOverlay({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 1),
    this.activatedIcon,
    this.isIconVisible = false,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        // Main child widget
        child,

        // Conditional overlay with icon
        if (isIconVisible && activatedIcon != null)
          Positioned(
            bottom: 10,
            left: 10,
            child: IgnorePointer(
              child: AnimatedOpacity(
                key: ValueKey(isIconVisible),
                opacity: isIconVisible ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: SvgPicture.asset(
                  activatedIcon!,
                  colorFilter: const ColorFilter.mode(
                    AppColor.kOrangeColor,
                    BlendMode.srcIn,
                  ),
                  height: iconSize ?? 20.h,
                  width: iconSize ?? 20.h,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
