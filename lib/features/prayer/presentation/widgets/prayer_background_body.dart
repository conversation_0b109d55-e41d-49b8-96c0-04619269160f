import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_date.dart';
import 'package:salawati/core/widgets/settings_share_row.dart';
import 'package:salawati/core/widgets/shimmer.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/prayer/presentation/screens/prayer_progress_bar.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

import '../../../../core/utils/app_functions.dart';

class PrayerBackgroundBody extends StatefulWidget {
  final PrayerController controller;
  const PrayerBackgroundBody({required this.controller, super.key});

  @override
  State<PrayerBackgroundBody> createState() => _PrayerBackgroundBodyState();
}

class _PrayerBackgroundBodyState extends State<PrayerBackgroundBody> {
  @override
  Widget build(BuildContext context) {
    Widget orangeVertical = Row(
      children: [
        ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Container(
              height: 20,
              width: 2,
              color: AppColor.kOrangeColor,
            )),
        const SizedBox(width: 10),
      ],
    );
    return GetBuilder<PrayerController>(builder: (controller) {
      if (controller.currentCountry.value == null) {
        return SizedBox(
          height: 0.37.sh,
          // width: 100.w,
          child: const CustomShimmer(),
        );
      }
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 32.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            0.01.sh.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SettingsShareRow(),
                Builder(
                  builder: (context) {
                    return InkWell(
                      onTap: () async {
                        if (kDebugMode) {
                        } else {
                          AppFunctions.shareText(context,
                              Get.find<PrayerController>().printPrayerTimes());
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SvgPicture.asset(AppSvgs.kShare),
                      ),
                    );
                  },
                ),
              ],
            ),
            .01.sh.verticalSpace,
            const PrayerProgressBar(),
            .04.sh.verticalSpace,
            Container(
              height: 130,
              width: double.infinity,
              decoration: BoxDecoration(
                  color: Theme.of(context)
                      .scaffoldBackgroundColor
                      .withOpacity(0.4),
                  borderRadius: BorderRadius.circular(21.r),
                  border: Border.all(
                    width: 0.5,
                    color: AppColor.kDarkGreyColor,
                  )),
              child: ClipPath(
                clipper: ShapeBorderClipper(
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(21.r),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 14),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                orangeVertical,
                                CustomText(
                                  'Date',
                                  style: TextStyle(
                                    color: AppColor.kGreyColor,
                                    fontSize: 12.sp,
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Obx(() {
                                  // Get the current date with offset from prayer page
                                  final dateOffset =
                                      widget.controller.prayerDatePage.value;
                                  final currentDate = DateTime.now()
                                      .add(Duration(days: dateOffset));

                                  return CustomDate(
                                    isHijry: true,
                                    fontSize: 12,
                                    color: Colors.white,
                                    withDay: false,
                                    dateTime: currentDate,
                                  );
                                }),
                                4.verticalSpace,
                                Obx(() {
                                  final dateOffset =
                                      widget.controller.prayerDatePage.value;
                                  final currentDate = DateTime.now()
                                      .add(Duration(days: dateOffset));

                                  return CustomDate(
                                    isHijry: false,
                                    fontSize: 12,
                                    color: Colors.white,
                                    withDay: false,
                                    dateTime: currentDate,
                                  );
                                }),
                                10.verticalSpace,
                                Obx(() {
                                  final dateOffset =
                                      widget.controller.prayerDatePage.value;
                                  final currentDate = DateTime.now()
                                      .add(Duration(days: dateOffset));

                                  return CustomDateDayOnly(
                                    fontSize: 18,
                                    color: Colors.white,
                                    dateTime: currentDate,
                                  );
                                }),
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                    Image.asset(
                      'assets/images/Dividershadow.png',
                      fit: BoxFit.contain,
                    ),
                    Expanded(
                      flex: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 14),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    orangeVertical,
                                    CustomText(
                                      'Current Location',
                                      style: TextStyle(
                                        color: AppColor.kGreyColor,
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                    SizedBox(width: 10),
                                    Obx(() => (SettingsController
                                            .instance.isLocationAuto.value)
                                        ? InkWell(
                                            onTap: () {
                                              LocationService.instance
                                                  .updateCurrentLocation();
                                            },
                                            child: LocationService
                                                    .instance
                                                    .isCurrentLocationUpdating
                                                    .value
                                                ? SizedBox(
                                                    height: 18,
                                                    width: 18,
                                                    child:
                                                        CircularProgressIndicator(
                                                      color: Colors.white,
                                                      strokeWidth: 1,
                                                    ),
                                                  )
                                                : const Icon(
                                                    Icons.refresh_rounded,
                                                    size: 20,
                                                    color: AppColor.kWhiteColor,
                                                  ),
                                          )
                                        : SizedBox.shrink())
                                  ],
                                ),
                              ],
                            ),
                            const Spacer(),
                            Obx(() {
                              Get.log(
                                  "LocationService.instance.currentLocation.value ${LocationService.instance.currentLocation.value}");
                              if (LocationService
                                      .instance.currentLocation.value ==
                                  null) {
                                return SizedBox(
                                  height: 50.h,
                                  width: 100.w,
                                  child: const CustomShimmer(),
                                );
                              }
                              return SizedBox(
                                child: Row(
                                  children: [
                                    SvgPicture.asset(
                                      AppSvgs.kLocation,
                                      height: 11,
                                      width: 9,
                                    ),
                                    const SizedBox(width: 4),
                                    AutoSizeText(
                                        LocationService.instance.currentLocation
                                            .value!.countryName,
                                        maxLines: 1,
                                        maxFontSize: 12,
                                        style: TextStyle(
                                          color: AppColor.kWhiteColor,
                                          fontSize: 12.sp,
                                          fontFamily: 'Tajawal',
                                        )),
                                  ],
                                ),
                              );
                            }),
                            15.verticalSpace,
                            Obx(() {
                              if (LocationService
                                      .instance.currentLocation.value ==
                                  null) {
                                return SizedBox(
                                  height: 10.h,
                                  width: 100.w,
                                  child: const CustomShimmer(),
                                );
                              }
                              return AutoSizeText(
                                  LocationService
                                      .instance.currentLocation.value!.cityName,
                                  maxLines: 2,
                                  maxFontSize: 16,
                                  style: TextStyle(
                                    color: AppColor.kWhiteColor,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: 'Tajawal',
                                  ));
                            }),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}

class PrayerBackgroundBody2 extends StatefulWidget {
  final PrayerController controller;
  const PrayerBackgroundBody2({required this.controller, super.key});

  @override
  State<PrayerBackgroundBody2> createState() => _PrayerBackgroundBody2State();
}

class _PrayerBackgroundBody2State extends State<PrayerBackgroundBody2> {
  @override
  Widget build(BuildContext context) {
    Widget orangeVertical = Row(
      children: [
        ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Container(
              height: 20,
              width: 2,
              color: AppColor.kOrangeColor,
            )),
        const SizedBox(width: 10),
      ],
    );
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          0.01.sh.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SettingsShareRow(),
              Builder(
                builder: (context) {
                  return InkWell(
                    onTap: () async {
                      if (kDebugMode) {
                      } else {
                        AppFunctions.shareText(context,
                            Get.find<PrayerController>().printPrayerTimes());
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: SvgPicture.asset(AppSvgs.kShare),
                    ),
                  );
                },
              ),
            ],
          ),
          .01.sh.verticalSpace,
          const PrayerProgressBar(),
          .02.sh.verticalSpace,
          Container(
            height: 80,
            width: double.infinity,
            decoration: BoxDecoration(
                color:
                    Theme.of(context).scaffoldBackgroundColor.withOpacity(0.8),
                borderRadius: BorderRadius.circular(21.r),
                border: Border.all(
                  width: 0.5,
                  color: AppColor.kDarkGreyColor,
                )),
            child: ClipPath(
              clipper: ShapeBorderClipper(
                shape: ContinuousRectangleBorder(
                  borderRadius: BorderRadius.circular(21.r),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/svgs/map3.svg',
                          height: 30,
                          width: 34,
                        ),
                        10.horizontalSpace,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            GetBuilder<PrayerController>(builder: (controller) {
                              if (controller.currentCountry.value == null) {
                                return SizedBox(
                                  height: 10.h,
                                  width: 100.w,
                                  child: const CustomShimmer(),
                                );
                              }
                              return SizedBox(
                                child: AutoSizeText(
                                    controller.currentCountry.value!,
                                    maxLines: 1,
                                    maxFontSize: 14,
                                    style: TextStyle(
                                      color: AppColor.kWhiteColor,
                                      fontSize: 14.sp,
                                      fontFamily: 'Tajawal',
                                    )),
                              );
                            }),
                            2.verticalSpace,
                            GetBuilder<PrayerController>(builder: (controller) {
                              if (controller.currentCity.value == null) {
                                return SizedBox(
                                  height: 10.h,
                                  width: 100.w,
                                  child: const CustomShimmer(),
                                );
                              }
                              return SizedBox(
                                width: 100,
                                child:
                                    AutoSizeText(controller.currentCity.value!,
                                        maxLines: 2,
                                        maxFontSize: 14,
                                        style: TextStyle(
                                          color: AppColor.kWhiteColor,
                                          fontSize: 14.sp,
                                          fontFamily: 'Tajawal',
                                        )),
                              );
                            }),
                          ],
                        ),
                      ],
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Obx(() {
                              final dateOffset =
                                  widget.controller.prayerDatePage.value;
                              final currentDate = DateTime.now()
                                  .add(Duration(days: dateOffset));

                              return CustomDate(
                                isHijry: true,
                                fontSize: 12,
                                color: Colors.white,
                                withDay: false,
                                dateTime: currentDate,
                              );
                            }),
                            4.verticalSpace,
                            Obx(() {
                              final dateOffset =
                                  widget.controller.prayerDatePage.value;
                              final currentDate = DateTime.now()
                                  .add(Duration(days: dateOffset));

                              return CustomDate(
                                isHijry: false,
                                fontSize: 12,
                                color: Colors.white,
                                withDay: false,
                                dateTime: currentDate,
                              );
                            }),
                          ],
                        ),
                        10.horizontalSpace,
                        SvgPicture.asset(
                          'assets/svgs/calender3.svg',
                          height: 30,
                          width: 34,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
