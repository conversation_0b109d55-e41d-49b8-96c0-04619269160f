import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:adhan/adhan.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:home_widget/home_widget.dart';
import 'package:salawati/main.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/utils/app_consts.dart';
import '../controller/prayer_controller.dart';

class PrayerDataService extends GetxController {
  static PrayerDataService get instance => Get.find<PrayerDataService>();

  final _prayerTimes = <PrayerTimeModel>[].obs;
  final PrayerController _prayerController = Get.find<PrayerController>();
  final _widgetUpdateDebouncer = Debouncer(delay: const Duration(seconds: 2));

  List<PrayerTimeModel> get prayerTimes => _prayerTimes.toList();

  @override
  void onInit() {
    if (Platform.isIOS) {
      _initializeService();
      _setupStorageListener();
    }

    super.onInit();
  }

  Future<void> _initializeService() async {
    try {
      await HomeWidget.setAppGroupId(widgetappGroup);
      await refreshPrayerData();
    } catch (e) {
      _logError('Widget Initialization error', e);
    }
  }

  void _setupStorageListener() {
    final storage = GetStorage();
    storage.listenKey(CITY, (value) {
      if (value != null) {
        _widgetUpdateDebouncer.run(() async {
          await HomeWidget.setAppGroupId(widgetappGroup);
          await refreshPrayerData();
          await sendToWidget();
        });
      }
    });
  }

  Future<void> refreshPrayerData() async {
    if (Platform.isIOS) {
      try {
        await _prayerController.calculatePrayerTimes();
        await _updatePrayerTimes();
      } catch (e) {
        _logError('Widget Prayer time calculation error', e);
        _prayerTimes.clear();
      }
    }
  }

  Future<void> _updatePrayerTimesWithMockData() async {
    final times = <PrayerTimeModel>[];
    // Use the current time as the starting point for day 1.
    final baseTime = DateTime.now().toLocal();
    const uuid = Uuid();
// Using a fixed 10-second interval between prayers.
    const int prayerIntervalSeconds = 10;
// Adjust dayBaseTime for each day. You might set a small day transition delay if needed.
    const int dayTransitionDelaySeconds = 3;
// Calculate the offset for each day.
    const int dayOffsetSeconds =
        (prayerIntervalSeconds * 4) + dayTransitionDelaySeconds;
    for (int day = 0; day < 3; day++) {
      final dayBaseTime =
          baseTime.add(Duration(seconds: day * dayOffsetSeconds));

      times.addAll([
        _createPrayerTime(
          id: uuid.v4(),
          date: dayBaseTime,
          label: 'Fajr',
          time: dayBaseTime,
          icon: 'sunrise.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: dayBaseTime,
          label: 'Dhuhr',
          time: dayBaseTime.add(const Duration(seconds: prayerIntervalSeconds)),
          icon: 'sun.max.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: dayBaseTime,
          label: 'Asr',
          time: dayBaseTime
              .add(const Duration(seconds: prayerIntervalSeconds * 2)),
          icon: 'sun.min.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: dayBaseTime,
          label: 'Maghrib',
          time: dayBaseTime
              .add(const Duration(seconds: prayerIntervalSeconds * 3)),
          icon: 'sunset.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: dayBaseTime,
          label: 'Isha',
          time: dayBaseTime
              .add(const Duration(seconds: prayerIntervalSeconds * 4)),
          icon: 'moon.stars.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: dayBaseTime,
          label: 'Middle Of The Night',
          time: dayBaseTime
              .add(const Duration(seconds: prayerIntervalSeconds * 6)),
          icon: 'moon.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: dayBaseTime,
          label: 'Last Third of the Night',
          time: dayBaseTime
              .add(const Duration(seconds: prayerIntervalSeconds * 8)),
          icon: 'moon.stars.fill',
        ),
      ]);
    }

    // Optionally filter out prayer times that are too old.
    _prayerTimes.assignAll(
      times
          .where((p) =>
              p.time.isAfter(baseTime.subtract(const Duration(hours: 4))))
          .toList(),
    );
  }

  Future<void> _updatePrayerTimes() async {
    final fetchedPrayerTimes = _prayerController.prayerTimes.value;
    if (fetchedPrayerTimes == null) return;

    final coordinates = _prayerController.coordinates;
    final params = _prayerController.params;
    final now = DateTime.now().toLocal();
    final times = <PrayerTimeModel>[];
    const uuid = Uuid();

    // Generate prayer times for 25 days starting today.
    for (int day = 0; day < 25; day++) {
      final date = now.add(Duration(days: day));
      final dailyTimes = PrayerTimes(
        coordinates,
        DateComponents.from(date),
        params,
      );
      final sunnahTimes = SunnahTimes(dailyTimes);

      times.addAll([
        _createPrayerTime(
          id: uuid.v4(),
          date: date,
          label: 'Fajr',
          time: dailyTimes.fajr,
          icon: 'sunrise.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: date,
          label: 'Dhuhr',
          time: dailyTimes.dhuhr,
          icon: 'sun.max.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: date,
          label: 'Asr',
          time: dailyTimes.asr,
          icon: 'sun.min.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: date,
          label: 'Maghrib',
          time: dailyTimes.maghrib,
          icon: 'sunset.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: date,
          label: 'Isha',
          time: dailyTimes.isha,
          icon: 'moon.stars.fill',
        ),
        // NEW: Additional times from SunnahTimes.
        _createPrayerTime(
          id: uuid.v4(),
          date: date,
          label: 'Middle Of The Night',
          time: sunnahTimes.middleOfTheNight,
          icon: 'moon.fill',
        ),
        _createPrayerTime(
          id: uuid.v4(),
          date: date,
          label: 'Last Third of the Night',
          time: sunnahTimes.lastThirdOfTheNight,
          icon: 'moon.stars.fill',
        ),
      ]);
    }

    // Optionally filter out old times
    _prayerTimes.assignAll(
      times
          .where((p) => p.time.isAfter(now.subtract(const Duration(hours: 4))))
          .toList(),
    );
  }

  PrayerTimeModel _createPrayerTime({
    required String id,
    required DateTime date,
    required String label,
    required DateTime time,
    required String icon,
  }) {
    return PrayerTimeModel(
      id: id,
      date: date,
      label: label,
      time: time.toLocal(),
      iconName: icon,
      timezoneOffset: time.timeZoneOffset.inMinutes,
    );
  }

  Future<void> sendToWidget() async {
    if (Platform.isIOS) {
      try {
        await HomeWidget.setAppGroupId(widgetappGroup);
        final storage = GetStorage();
        final payload = PrayerPayloadModel(
          userLanguage: storage.read('lang') ?? 'ar',
          userLocation: storage.read(CITY) ?? 'Makkah'.tr,
          updatedAt: DateTime.now(),
          prayers: _prayerTimes.toList(),
        );

        final jsonString = jsonEncode(payload.toJson());
        await HomeWidget.saveWidgetData<String>('prayerDataNew', jsonString);

        final List<HomeWidgetInfo> info =
            await HomeWidget.getInstalledWidgets();

        final saved = await HomeWidget.getWidgetData<String>('prayerDataNew');
        debugPrint(
            'Widget Data saved successfully: ${saved != null} - ${DateTime.now()}');
        await _updateAllWidgets();
        // log('Widget updated at $saved');
        // log('Widget listed $info');
      } catch (e) {
        _logError('Widget update error', e);
      }
    }
  }

  Future<void> _updateAllWidgets() async {
    const widgets = [
      // ['LargePrayerWidget'],
      ['NextPrayerSmallWidgetFallback'],
      ['MediumPrayerWidgetFallback'],

      // ['AllPrayersSmallWidget'],

      // ['ExtraMediumPrayerWidget'],
      // ['LargeScrollablePrayerWidget'],
    ];
    for (final widget in widgets) {
      await HomeWidget.updateWidget(
        iOSName: widget[0],
        androidName: widget[0],
      );
    }
  }

  void _logError(String message, dynamic error) {
    debugPrint('🔴 $message: ${error.toString()}');
  }
}

class Debouncer {
  final Duration delay;
  Timer? _timer;
  Debouncer({required this.delay});
  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }
}

@immutable
class PrayerTimeModel {
  final String id;
  final DateTime date;
  final String label;
  final DateTime time;
  final String iconName;
  final int timezoneOffset;
  const PrayerTimeModel({
    required this.id,
    required this.date,
    required this.label,
    required this.time,
    required this.iconName,
    required this.timezoneOffset,
  });
  Map<String, dynamic> toJson() => {
        'id': id,
        'date': date.millisecondsSinceEpoch,
        'label': label,
        'time': time.millisecondsSinceEpoch,
        'iconName': iconName,
        'timezoneOffset': timezoneOffset,
      };
}

@immutable
class PrayerPayloadModel {
  final String userLanguage;
  final String userLocation;
  final DateTime updatedAt;
  final List<PrayerTimeModel> prayers;
  const PrayerPayloadModel({
    required this.userLanguage,
    required this.userLocation,
    required this.updatedAt,
    required this.prayers,
  });
  Map<String, dynamic> toJson() => {
        'userLanguage': userLanguage,
        'userLocation': userLocation,
        'updatedAt': updatedAt.millisecondsSinceEpoch,
        'prayers': prayers.map((p) => p.toJson()).toList(),
      };
}
