import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'dart:convert';

/// Production monitoring service for awesome_notifications deployment
class ProductionMonitoringService {
  static const String _metricsKey = 'notification_metrics';
  static const String _errorsKey = 'notification_errors';
  static const String _performanceKey = 'notification_performance';
  
  static final GetStorage _storage = GetStorage();
  static final ProductionMonitoringService _instance = ProductionMonitoringService._internal();
  
  factory ProductionMonitoringService() => _instance;
  ProductionMonitoringService._internal();

  /// Notification delivery metrics
  static Map<String, int> get deliveryMetrics {
    final metricsJson = _storage.read(_metricsKey);
    if (metricsJson != null) {
      return Map<String, int>.from(json.decode(metricsJson));
    }
    return {
      'awesome_scheduled': 0,
      'awesome_delivered': 0,
      'awesome_failed': 0,
      'legacy_scheduled': 0,
      'legacy_delivered': 0,
      'legacy_failed': 0,
      'prayer_notifications': 0,
      'athkar_notifications': 0,
      'system_notifications': 0,
    };
  }

  /// Record notification scheduled
  static Future<void> recordNotificationScheduled({
    required String system, // 'awesome' or 'legacy'
    required String type,   // 'prayer', 'athkar', 'system'
  }) async {
    final metrics = deliveryMetrics;
    metrics['${system}_scheduled'] = (metrics['${system}_scheduled'] ?? 0) + 1;
    metrics['${type}_notifications'] = (metrics['${type}_notifications'] ?? 0) + 1;
    
    await _storage.write(_metricsKey, json.encode(metrics));
    
    if (kDebugMode) {
      print('📊 Notification scheduled: $system/$type');
    }
  }

  /// Record notification delivered
  static Future<void> recordNotificationDelivered({
    required String system,
    required String type,
  }) async {
    final metrics = deliveryMetrics;
    metrics['${system}_delivered'] = (metrics['${system}_delivered'] ?? 0) + 1;
    
    await _storage.write(_metricsKey, json.encode(metrics));
    
    if (kDebugMode) {
      print('✅ Notification delivered: $system/$type');
    }
  }

  /// Record notification failed
  static Future<void> recordNotificationFailed({
    required String system,
    required String type,
    required String error,
  }) async {
    final metrics = deliveryMetrics;
    metrics['${system}_failed'] = (metrics['${system}_failed'] ?? 0) + 1;
    
    await _storage.write(_metricsKey, json.encode(metrics));
    await _recordError(system, type, error);
    
    if (kDebugMode) {
      print('❌ Notification failed: $system/$type - $error');
    }
  }

  /// Record error details
  static Future<void> _recordError(String system, String type, String error) async {
    final errors = _getErrors();
    final errorEntry = {
      'timestamp': DateTime.now().toIso8601String(),
      'system': system,
      'type': type,
      'error': error,
    };
    
    errors.add(errorEntry);
    
    // Keep only last 100 errors
    if (errors.length > 100) {
      errors.removeRange(0, errors.length - 100);
    }
    
    await _storage.write(_errorsKey, json.encode(errors));
  }

  /// Get error history
  static List<Map<String, dynamic>> _getErrors() {
    final errorsJson = _storage.read(_errorsKey);
    if (errorsJson != null) {
      return List<Map<String, dynamic>>.from(json.decode(errorsJson));
    }
    return [];
  }

  /// Get delivery success rate
  static double getDeliverySuccessRate(String system) {
    final metrics = deliveryMetrics;
    final scheduled = metrics['${system}_scheduled'] ?? 0;
    final delivered = metrics['${system}_delivered'] ?? 0;
    
    if (scheduled == 0) return 0.0;
    return (delivered / scheduled) * 100;
  }

  /// Get failure rate
  static double getFailureRate(String system) {
    final metrics = deliveryMetrics;
    final scheduled = metrics['${system}_scheduled'] ?? 0;
    final failed = metrics['${system}_failed'] ?? 0;
    
    if (scheduled == 0) return 0.0;
    return (failed / scheduled) * 100;
  }

  /// Performance metrics
  static Map<String, dynamic> get performanceMetrics {
    final performanceJson = _storage.read(_performanceKey);
    if (performanceJson != null) {
      return Map<String, dynamic>.from(json.decode(performanceJson));
    }
    return {
      'initialization_time': 0.0,
      'scheduling_time': 0.0,
      'permission_request_time': 0.0,
      'system_switch_time': 0.0,
    };
  }

  /// Record performance metric
  static Future<void> recordPerformanceMetric(String metric, double value) async {
    final performance = performanceMetrics;
    performance[metric] = value;
    
    await _storage.write(_performanceKey, json.encode(performance));
    
    if (kDebugMode) {
      print('⏱️ Performance metric: $metric = ${value.toStringAsFixed(2)}ms');
    }
  }

  /// Get comprehensive monitoring report
  static Map<String, dynamic> getMonitoringReport() {
    final awesomeSuccess = getDeliverySuccessRate('awesome');
    final legacySuccess = getDeliverySuccessRate('legacy');
    final awesomeFailure = getFailureRate('awesome');
    final legacyFailure = getFailureRate('legacy');
    
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'delivery_metrics': deliveryMetrics,
      'success_rates': {
        'awesome': awesomeSuccess,
        'legacy': legacySuccess,
        'comparison': awesomeSuccess - legacySuccess,
      },
      'failure_rates': {
        'awesome': awesomeFailure,
        'legacy': legacyFailure,
        'comparison': awesomeFailure - legacyFailure,
      },
      'performance_metrics': performanceMetrics,
      'recent_errors': _getErrors().take(10).toList(),
      'total_errors': _getErrors().length,
    };
  }

  /// Export metrics for analytics
  static String exportMetricsAsJson() {
    return json.encode(getMonitoringReport());
  }

  /// Reset all metrics
  static Future<void> resetMetrics() async {
    await _storage.remove(_metricsKey);
    await _storage.remove(_errorsKey);
    await _storage.remove(_performanceKey);
    
    if (kDebugMode) {
      print('🔄 All monitoring metrics reset');
    }
  }

  /// Check system health
  static Map<String, dynamic> getSystemHealth() {
    final awesomeSuccess = getDeliverySuccessRate('awesome');
    final legacySuccess = getDeliverySuccessRate('legacy');
    final errors = _getErrors();
    final recentErrors = errors.where((error) {
      final timestamp = DateTime.parse(error['timestamp']);
      return DateTime.now().difference(timestamp).inHours < 24;
    }).length;
    
    String healthStatus = 'healthy';
    if (awesomeSuccess < 90 || recentErrors > 10) {
      healthStatus = 'warning';
    }
    if (awesomeSuccess < 70 || recentErrors > 25) {
      healthStatus = 'critical';
    }
    
    return {
      'status': healthStatus,
      'awesome_success_rate': awesomeSuccess,
      'legacy_success_rate': legacySuccess,
      'recent_errors_24h': recentErrors,
      'recommendation': _getHealthRecommendation(healthStatus, awesomeSuccess, legacySuccess),
    };
  }

  /// Get health-based recommendations
  static String _getHealthRecommendation(String status, double awesomeSuccess, double legacySuccess) {
    switch (status) {
      case 'critical':
        return 'Consider rolling back to legacy system or reducing rollout percentage';
      case 'warning':
        return 'Monitor closely and investigate recent errors';
      case 'healthy':
        if (awesomeSuccess > legacySuccess + 5) {
          return 'Awesome notifications performing well - consider increasing rollout';
        } else if (legacySuccess > awesomeSuccess + 5) {
          return 'Legacy system performing better - investigate awesome notifications issues';
        } else {
          return 'Both systems performing similarly - continue current deployment';
        }
      default:
        return 'Continue monitoring';
    }
  }
}
