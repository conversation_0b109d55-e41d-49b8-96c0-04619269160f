import 'package:flutter/foundation.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:salawati/core/services/notification_navigation_service.dart';

/// Manages awesome_notifications_service for all notification functionality
class NotificationServiceManager {
  static bool _isInitialized = false;

  /// Initialize awesome notifications system
  static Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      if (kDebugMode) {
        print('🚀 Initializing awesome notifications system...');
      }

      // Initialize awesome notifications service
      final awesomeInitialized = await AwesomeNotificationsService.initialize();
      if (kDebugMode) {
        print('✅ Awesome notifications initialized: $awesomeInitialized');
      }

      // Set up navigation callback for notification taps
      await _setupNavigationCallback();

      _isInitialized = true;

      if (kDebugMode) {
        print('🔄 Notification Service Manager initialized successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ NotificationServiceManager initialization failed: $e');
      }

      return false;
    }
  }

  /// Check if using awesome notifications (always true now)
  static bool get isUsingAwesomeNotifications => true;

  /// Check if using legacy notifications (always false now)
  static bool get isUsingLegacyNotifications => false;

  /// Get current notification system name
  static String get currentSystemName => 'Awesome Notifications';

  /// Request permissions from awesome notifications
  static Future<bool> requestPermissions() async {
    try {
      return await AwesomeNotificationsService.requestPermissions();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting permissions: $e');
      }
      return false;
    }
  }

  /// Check permissions from awesome notifications
  static Future<bool> hasPermissions() async {
    try {
      final status = await AwesomeNotificationsService.getPermissionStatus();
      return status.hasAllPermissions;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking permissions: $e');
      }
      return false;
    }
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    try {
      await AwesomeNotificationsService.cancelAllNotifications();
      if (kDebugMode) {
        print('✅ All notifications cancelled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error cancelling notifications: $e');
      }
    }
  }

  /// Get notification statistics (awesome notifications only)
  static Future<NotificationStatistics?> getStatistics() async {
    try {
      return await AwesomeNotificationsService.getNotificationStatistics();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting statistics: $e');
      }
      return null;
    }
  }

  /// Set up navigation callback for notification taps
  static Future<void> _setupNavigationCallback() async {
    try {
      if (kDebugMode) {
        print('🔗 Setting up notification navigation callback');
      }

      // Set the navigation callback in the awesome notifications manager
      AwesomeNotificationsService.setNavigationCallback((receivedAction) async {
        try {
          if (kDebugMode) {
            print(
                '📱 Navigation callback triggered for notification: ${receivedAction.id}');
          }

          // Use the notification navigation service to handle the tap
          final navigationService = NotificationNavigationService.instance;
          await navigationService.handleNotificationTap(receivedAction);
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error in navigation callback: $e');
          }
        }
      });

      if (kDebugMode) {
        print('✅ Navigation callback set up successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting up navigation callback: $e');
      }
    }
  }

  /// Reset the service manager (for testing)
  static void reset() {
    _isInitialized = false;
    // Clear navigation callback
    AwesomeNotificationsService.clearNavigationCallback();
  }

  /// Enable awesome notifications (legacy method for compatibility)
  static Future<void> enableAwesomeNotifications() async {
    // This method is kept for compatibility but does nothing
    // since awesome notifications are always enabled now
    if (kDebugMode) {
      print('✅ Awesome notifications already enabled (legacy method called)');
    }
  }

  /// Disable awesome notifications (legacy method for compatibility)
  static Future<void> disableAwesomeNotifications() async {
    // This method is kept for compatibility but does nothing
    // since we can't disable awesome notifications anymore
    if (kDebugMode) {
      print('⚠️ Cannot disable awesome notifications (legacy method called)');
    }
  }
}
