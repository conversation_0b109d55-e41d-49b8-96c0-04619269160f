import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:salawati/features/location/data/services/location_service.dart';

enum PermissionType {
  location,
  notification,
  batteryOptimization,
}

enum PermissionFlowStatus {
  notStarted,
  inProgress,
  completed,
  skipped,
}

class PermissionResult {
  final PermissionType type;
  final bool granted;
  final bool permanentlyDenied;
  final String? errorMessage;

  PermissionResult({
    required this.type,
    required this.granted,
    this.permanentlyDenied = false,
    this.errorMessage,
  });
}

class PermissionManager extends GetxController {
  static const String _permissionFlowKey = 'permission_flow_completed';
  static const String _locationPermissionKey = 'location_permission_asked';
  static const String _notificationPermissionKey = 'notification_permission_asked';
  static const String _batteryOptimizationKey = 'battery_optimization_asked';

  final GetStorage _storage = GetStorage();

  // Reactive state
  final RxBool _isPermissionFlowCompleted = false.obs;
  final Rx<PermissionFlowStatus> _flowStatus = PermissionFlowStatus.notStarted.obs;
  final RxMap<PermissionType, PermissionResult> _permissionResults =
      <PermissionType, PermissionResult>{}.obs;

  // Getters
  bool get isPermissionFlowCompleted => _isPermissionFlowCompleted.value;
  PermissionFlowStatus get flowStatus => _flowStatus.value;
  Map<PermissionType, PermissionResult> get permissionResults => _permissionResults;

  @override
  void onInit() {
    super.onInit();
    _loadPermissionState();
  }

  void _loadPermissionState() {
    _isPermissionFlowCompleted.value = _storage.read(_permissionFlowKey) ?? false;
    if (_isPermissionFlowCompleted.value) {
      _flowStatus.value = PermissionFlowStatus.completed;
    }
  }

  Future<bool> shouldShowPermissionFlow() async {
    debugPrint('PermissionManager: Checking if should show permission flow...');

    // Check if permission flow was already completed
    if (_isPermissionFlowCompleted.value) {
      debugPrint('PermissionManager: Permission flow already completed, skipping');
      return false;
    }

    // Check if this is first time opening the app
    final bool isFirstTime = !(_storage.hasData(_permissionFlowKey));
    debugPrint('PermissionManager: Is first time opening app: $isFirstTime');

    if (!isFirstTime) {
      debugPrint('PermissionManager: Not first time, skipping permission flow');
      return false; // Not first time, don't show permission flow
    }

    // First time opening app - check actual permission status
    // Only show permission flow if critical permissions are not granted
    debugPrint('PermissionManager: First time opening app, checking actual permission status...');
    final bool hasLocationPermission = await _checkLocationPermissionStatus();
    final bool hasNotificationPermission = await _checkNotificationPermissionStatus();

    debugPrint('PermissionManager: Location permission granted: $hasLocationPermission');
    debugPrint('PermissionManager: Notification permission granted: $hasNotificationPermission');

    // If both critical permissions are already granted, mark flow as completed and skip
    if (hasLocationPermission && hasNotificationPermission) {
      debugPrint('PermissionManager: Both critical permissions already granted, marking flow as completed');
      _isPermissionFlowCompleted.value = true;
      await _storage.write(_permissionFlowKey, true);
      return false;
    }

    // Show permission flow only if critical permissions are missing
    debugPrint('PermissionManager: Critical permissions missing, showing permission flow');
    return true;
  }

  Future<bool> _checkLocationPermissionStatus() async {
    try {
      final status = await Permission.location.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking location permission: $e');
      return false;
    }
  }

  Future<bool> _checkNotificationPermissionStatus() async {
    try {
      final status = await Permission.notification.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking notification permission: $e');
      return false;
    }
  }

  Future<void> startPermissionFlow() async {
    _flowStatus.value = PermissionFlowStatus.inProgress;
  }

  Future<PermissionResult> requestLocationPermission() async {
    try {
      final locationService = Get.find<LocationService>();

      // Check current status first
      final currentStatus = await Permission.location.status;
      if (currentStatus.isGranted) {
        final result = PermissionResult(
          type: PermissionType.location,
          granted: true,
        );
        _permissionResults[PermissionType.location] = result;
        await _storage.write(_locationPermissionKey, true);
        return result;
      }

      // Request permission through location service
      final permissionResult = await locationService.handlePermissionFlow();

      final bool granted = permissionResult == LocationPermissionResult.granted;
      final bool permanentlyDenied = permissionResult == LocationPermissionResult.permanentlyDenied;

      final result = PermissionResult(
        type: PermissionType.location,
        granted: granted,
        permanentlyDenied: permanentlyDenied,
      );

      _permissionResults[PermissionType.location] = result;
      await _storage.write(_locationPermissionKey, true);

      return result;
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      final result = PermissionResult(
        type: PermissionType.location,
        granted: false,
        errorMessage: e.toString(),
      );
      _permissionResults[PermissionType.location] = result;
      return result;
    }
  }

  Future<PermissionResult> requestNotificationPermission() async {
    try {
      // Check current status first
      final currentStatus = await Permission.notification.status;
      if (currentStatus.isGranted) {
        final result = PermissionResult(
          type: PermissionType.notification,
          granted: true,
        );
        _permissionResults[PermissionType.notification] = result;
        _storage.write(_notificationPermissionKey, true);
        return result;
      }

      // Request permission through awesome notifications
      final granted = await AwesomeNotificationsService.requestPermissions();

      final result = PermissionResult(
        type: PermissionType.notification,
        granted: granted,
        permanentlyDenied: !granted && currentStatus.isPermanentlyDenied,
      );

      _permissionResults[PermissionType.notification] = result;
      await _storage.write(_notificationPermissionKey, true);

      return result;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      final result = PermissionResult(
        type: PermissionType.notification,
        granted: false,
        errorMessage: e.toString(),
      );
      _permissionResults[PermissionType.notification] = result;
      return result;
    }
  }

  Future<PermissionResult> requestBatteryOptimizationPermission() async {
    try {
      if (!Platform.isAndroid) {
        // iOS doesn't need battery optimization permission
        final result = PermissionResult(
          type: PermissionType.batteryOptimization,
          granted: true,
        );
        _permissionResults[PermissionType.batteryOptimization] = result;
        return result;
      }

      final status = await Permission.ignoreBatteryOptimizations.request();
      final granted = status.isGranted;

      final result = PermissionResult(
        type: PermissionType.batteryOptimization,
        granted: granted,
        permanentlyDenied: status.isPermanentlyDenied,
      );

      _permissionResults[PermissionType.batteryOptimization] = result;
      await _storage.write(_batteryOptimizationKey, true);

      return result;
    } catch (e) {
      debugPrint('Error requesting battery optimization permission: $e');
      final result = PermissionResult(
        type: PermissionType.batteryOptimization,
        granted: false,
        errorMessage: e.toString(),
      );
      _permissionResults[PermissionType.batteryOptimization] = result;
      return result;
    }
  }

  void skipPermission(PermissionType type) {
    final result = PermissionResult(
      type: type,
      granted: false,
    );
    _permissionResults[type] = result;

    // Mark as asked so we don't show it again
    switch (type) {
      case PermissionType.location:
        _storage.write(_locationPermissionKey, true);
        break;
      case PermissionType.notification:
        _storage.write(_notificationPermissionKey, true);
        break;
      case PermissionType.batteryOptimization:
        _storage.write(_batteryOptimizationKey, true);
        break;
    }
  }

  void completePermissionFlow() {
    _flowStatus.value = PermissionFlowStatus.completed;
    _isPermissionFlowCompleted.value = true;
    _storage.write(_permissionFlowKey, true);
  }

  void skipPermissionFlow() {
    _flowStatus.value = PermissionFlowStatus.skipped;
    _isPermissionFlowCompleted.value = true;
    _storage.write(_permissionFlowKey, true);
  }

  // Helper methods to check individual permission status
  Future<bool> hasLocationPermission() async {
    final status = await Permission.location.status;
    return status.isGranted;
  }

  Future<bool> hasNotificationPermission() async {
    final status = await Permission.notification.status;
    return status.isGranted;
  }

  Future<bool> hasBatteryOptimizationPermission() async {
    if (!Platform.isAndroid) return true;
    final status = await Permission.ignoreBatteryOptimizations.status;
    return status.isGranted;
  }

  // Reset permission flow (for testing or re-onboarding)
  void resetPermissionFlow() {
    debugPrint('PermissionManager: Resetting permission flow for testing');
    _storage.remove(_permissionFlowKey);
    _storage.remove(_locationPermissionKey);
    _storage.remove(_notificationPermissionKey);
    _storage.remove(_batteryOptimizationKey);
    _isPermissionFlowCompleted.value = false;
    _flowStatus.value = PermissionFlowStatus.notStarted;
    _permissionResults.clear();
  }

  // Force show permission flow (for testing)
  void forceShowPermissionFlow() {
    debugPrint('PermissionManager: Forcing permission flow to show for testing');
    resetPermissionFlow();
  }

  // Test method to simulate first-time user experience
  Future<bool> testPermissionFlow() async {
    debugPrint('PermissionManager: Testing permission flow - simulating first time user');

    // Temporarily reset the permission flow flag
    final originalValue = _storage.read(_permissionFlowKey);
    _storage.remove(_permissionFlowKey);
    _isPermissionFlowCompleted.value = false;

    // Check what would happen
    final shouldShow = await shouldShowPermissionFlow();

    // Restore original value
    if (originalValue != null) {
      _storage.write(_permissionFlowKey, originalValue);
      _isPermissionFlowCompleted.value = originalValue;
    }

    debugPrint('PermissionManager: Test result - would show permission flow: $shouldShow');
    return shouldShow;
  }
}
