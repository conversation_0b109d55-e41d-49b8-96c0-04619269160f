import 'package:flutter/foundation.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'notification_service_manager.dart';

/// Production testing service for comprehensive validation
class ProductionTestingService {
  static const String _testTag = '🧪 [Production Test]';

  /// Comprehensive test suite for production deployment
  static Future<Map<String, dynamic>> runComprehensiveTests() async {
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
      'overall_status': 'unknown',
      'recommendations': <String>[],
    };

    if (kDebugMode) {
      print('$_testTag Starting comprehensive production tests...');
    }

    // Test 1: System Initialization
    results['tests']['initialization'] = await _testSystemInitialization();
    
    // Test 2: Permission Handling
    results['tests']['permissions'] = await _testPermissionHandling();
    
    // Test 3: Notification Scheduling
    results['tests']['scheduling'] = await _testNotificationScheduling();
    
    // Test 4: System Switching
    results['tests']['system_switching'] = await _testSystemSwitching();
    
    // Test 5: Error Handling
    results['tests']['error_handling'] = await _testErrorHandling();
    
    // Test 6: Performance
    results['tests']['performance'] = await _testPerformance();

    // Calculate overall status
    results['overall_status'] = _calculateOverallStatus(results['tests']);
    results['recommendations'] = _generateRecommendations(results['tests']);

    if (kDebugMode) {
      print('$_testTag Comprehensive tests completed: ${results['overall_status']}');
    }

    return results;
  }

  /// Test system initialization
  static Future<Map<String, dynamic>> _testSystemInitialization() async {
    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
      'errors': <String>[],
    };

    try {
      final startTime = DateTime.now();
      
      // Test NotificationServiceManager initialization
      final initSuccess = await NotificationServiceManager.initialize();
      result['details']['service_manager_init'] = initSuccess;
      
      // Test AwesomeNotificationsService initialization
      final awesomeInitSuccess = await AwesomeNotificationsService.initialize();
      result['details']['awesome_service_init'] = awesomeInitSuccess;
      
      final initTime = DateTime.now().difference(startTime).inMilliseconds;
      result['details']['initialization_time_ms'] = initTime;
      
      if (initSuccess && awesomeInitSuccess && initTime < 5000) {
        result['status'] = 'pass';
      } else {
        result['status'] = 'fail';
        if (!initSuccess) result['errors'].add('NotificationServiceManager failed to initialize');
        if (!awesomeInitSuccess) result['errors'].add('AwesomeNotificationsService failed to initialize');
        if (initTime >= 5000) result['errors'].add('Initialization took too long: ${initTime}ms');
      }
    } catch (e) {
      result['status'] = 'error';
      result['errors'].add('Initialization test failed: $e');
    }

    return result;
  }

  /// Test permission handling
  static Future<Map<String, dynamic>> _testPermissionHandling() async {
    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
      'errors': <String>[],
    };

    try {
      // Test permission checking
      final hasPermissions = await NotificationServiceManager.hasPermissions();
      result['details']['has_permissions'] = hasPermissions;
      
      // Test permission status from awesome notifications
      final permissionStatus = await AwesomeNotificationsService.getPermissionStatus();
      result['details']['permission_status'] = {
        'hasAllPermissions': permissionStatus.hasAllPermissions,
        'hasNotificationPermission': permissionStatus.hasNotificationPermission,
        'hasExactAlarmPermission': permissionStatus.hasExactAlarmPermission,
        'hasCriticalAlertPermission': permissionStatus.hasCriticalAlertPermission,
      };
      
      if (hasPermissions && permissionStatus.hasNotificationPermission) {
        result['status'] = 'pass';
      } else {
        result['status'] = 'warning';
        result['errors'].add('Some permissions may be missing');
      }
    } catch (e) {
      result['status'] = 'error';
      result['errors'].add('Permission test failed: $e');
    }

    return result;
  }

  /// Test notification scheduling
  static Future<Map<String, dynamic>> _testNotificationScheduling() async {
    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
      'errors': <String>[],
    };

    try {
      final testTime = DateTime.now().add(const Duration(minutes: 1));
      
      // Test awesome notifications scheduling
      final awesomeSuccess = await AwesomeNotificationsService.scheduleTestNotification(
        title: 'Production Test',
        body: 'Testing awesome notifications scheduling',
        scheduledTime: testTime,
      );
      result['details']['awesome_scheduling'] = awesomeSuccess;
      
      // Test prayer notification scheduling
      final prayerSuccess = await AwesomeNotificationsService.scheduleTestPrayerNotification(
        prayerName: 'Test',
        scheduledTime: testTime.add(const Duration(minutes: 1)),
      );
      result['details']['prayer_scheduling'] = prayerSuccess;
      
      // Get scheduled count
      final scheduledCount = await AwesomeNotificationsService.getTotalScheduledNotificationsCount();
      result['details']['scheduled_count'] = scheduledCount;
      
      if (awesomeSuccess && prayerSuccess && scheduledCount >= 2) {
        result['status'] = 'pass';
      } else {
        result['status'] = 'fail';
        if (!awesomeSuccess) result['errors'].add('Awesome notification scheduling failed');
        if (!prayerSuccess) result['errors'].add('Prayer notification scheduling failed');
        if (scheduledCount < 2) result['errors'].add('Scheduled count mismatch: $scheduledCount');
      }
      
      // Clean up test notifications
      await AwesomeNotificationsService.cancelAllNotifications();
      
    } catch (e) {
      result['status'] = 'error';
      result['errors'].add('Scheduling test failed: $e');
    }

    return result;
  }

  /// Test system switching
  static Future<Map<String, dynamic>> _testSystemSwitching() async {
    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
      'errors': <String>[],
    };

    try {
      final originalSystem = NotificationServiceManager.isUsingAwesomeNotifications;
      
      // Test switching to awesome notifications
      await NotificationServiceManager.enableAwesomeNotifications();
      final isAwesome = NotificationServiceManager.isUsingAwesomeNotifications;
      result['details']['switch_to_awesome'] = isAwesome;
      
      // Test switching to legacy
      await NotificationServiceManager.disableAwesomeNotifications();
      final isLegacy = NotificationServiceManager.isUsingLegacyNotifications;
      result['details']['switch_to_legacy'] = isLegacy;
      
      // Restore original system
      if (originalSystem) {
        await NotificationServiceManager.enableAwesomeNotifications();
      }
      
      if (isAwesome && isLegacy) {
        result['status'] = 'pass';
      } else {
        result['status'] = 'fail';
        if (!isAwesome) result['errors'].add('Failed to switch to awesome notifications');
        if (!isLegacy) result['errors'].add('Failed to switch to legacy notifications');
      }
    } catch (e) {
      result['status'] = 'error';
      result['errors'].add('System switching test failed: $e');
    }

    return result;
  }

  /// Test error handling
  static Future<Map<String, dynamic>> _testErrorHandling() async {
    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
      'errors': <String>[],
    };

    try {
      // Test invalid notification scheduling
      try {
        await AwesomeNotificationsService.scheduleTestNotification(
          title: '',
          body: '',
          scheduledTime: DateTime.now().subtract(const Duration(days: 1)), // Past time
        );
        result['details']['handles_invalid_input'] = false;
      } catch (e) {
        result['details']['handles_invalid_input'] = true;
        result['details']['invalid_input_error'] = e.toString();
      }
      
      // Test graceful degradation
      result['details']['graceful_degradation'] = true; // Assume true unless proven otherwise
      
      result['status'] = 'pass';
    } catch (e) {
      result['status'] = 'error';
      result['errors'].add('Error handling test failed: $e');
    }

    return result;
  }

  /// Test performance
  static Future<Map<String, dynamic>> _testPerformance() async {
    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
      'errors': <String>[],
    };

    try {
      // Test initialization performance
      final initStart = DateTime.now();
      await AwesomeNotificationsService.initialize();
      final initTime = DateTime.now().difference(initStart).inMilliseconds;
      result['details']['init_time_ms'] = initTime;
      
      // Test scheduling performance
      final scheduleStart = DateTime.now();
      await AwesomeNotificationsService.scheduleTestNotification(
        title: 'Performance Test',
        body: 'Testing scheduling performance',
        scheduledTime: DateTime.now().add(const Duration(hours: 1)),
      );
      final scheduleTime = DateTime.now().difference(scheduleStart).inMilliseconds;
      result['details']['schedule_time_ms'] = scheduleTime;
      
      // Clean up
      await AwesomeNotificationsService.cancelAllNotifications();
      
      if (initTime < 3000 && scheduleTime < 1000) {
        result['status'] = 'pass';
      } else {
        result['status'] = 'warning';
        if (initTime >= 3000) result['errors'].add('Initialization too slow: ${initTime}ms');
        if (scheduleTime >= 1000) result['errors'].add('Scheduling too slow: ${scheduleTime}ms');
      }
    } catch (e) {
      result['status'] = 'error';
      result['errors'].add('Performance test failed: $e');
    }

    return result;
  }

  /// Calculate overall status from individual test results
  static String _calculateOverallStatus(Map<String, dynamic> tests) {
    int passCount = 0;
    int failCount = 0;
    int errorCount = 0;
    int warningCount = 0;

    for (final test in tests.values) {
      switch (test['status']) {
        case 'pass':
          passCount++;
          break;
        case 'fail':
          failCount++;
          break;
        case 'error':
          errorCount++;
          break;
        case 'warning':
          warningCount++;
          break;
      }
    }

    if (errorCount > 0 || failCount > 2) {
      return 'critical';
    } else if (failCount > 0 || warningCount > 1) {
      return 'warning';
    } else if (passCount == tests.length) {
      return 'excellent';
    } else {
      return 'good';
    }
  }

  /// Generate recommendations based on test results
  static List<String> _generateRecommendations(Map<String, dynamic> tests) {
    final recommendations = <String>[];

    for (final entry in tests.entries) {
      final testName = entry.key;
      final testResult = entry.value;
      
      if (testResult['status'] == 'fail' || testResult['status'] == 'error') {
        recommendations.add('Fix issues in $testName test: ${testResult['errors'].join(', ')}');
      } else if (testResult['status'] == 'warning') {
        recommendations.add('Review $testName test warnings: ${testResult['errors'].join(', ')}');
      }
    }

    if (recommendations.isEmpty) {
      recommendations.add('All tests passed - system ready for production deployment');
    }

    return recommendations;
  }
}
