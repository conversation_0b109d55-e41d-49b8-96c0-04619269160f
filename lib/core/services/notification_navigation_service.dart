import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_screen.dart';
import 'package:salawati/features/athkar/presentation/screens/athkar_details_screen.dart';
import 'package:salawati/features/athkar/presentation/controller/athkar_controller.dart';
import 'package:salawati/features/athkar/data/models/athkar_category_model.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:awesome_notifications/awesome_notifications.dart';

/// Service to handle navigation from notification taps
class NotificationNavigationService {
  static final NotificationNavigationService _instance =
      NotificationNavigationService._internal();
  factory NotificationNavigationService() => _instance;
  NotificationNavigationService._internal();

  static NotificationNavigationService get instance => _instance;

  /// Handle notification tap navigation
  Future<void> handleNotificationTap(ReceivedAction receivedAction) async {
    try {
      if (kDebugMode) {
        print('🔔 Handling notification tap: ${receivedAction.payload}');
      }

      // Check if this is an athkar notification
      if (_isAthkarNotification(receivedAction)) {
        await _handleAthkarNotificationTap(receivedAction);
      } else {
        // Handle other notification types
        await _handleOtherNotificationTap(receivedAction);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling notification tap: $e');
      }
    }
  }

  /// Check if the notification is an athkar notification
  bool _isAthkarNotification(ReceivedAction receivedAction) {
    final payload = receivedAction.payload;
    if (payload == null) return false;

    // Check payload for athkar indicators
    final payloadString = payload['payload'] ?? '';
    final notificationType = payload['notificationType'] ?? '';
    final athkarType = payload['athkar_type'] ?? '';

    return payloadString == 'soundNotifications' ||
        notificationType.contains('athkar') ||
        notificationType.contains('dhikr') ||
        athkarType.isNotEmpty;
  }

  /// Handle athkar notification tap
  Future<void> _handleAthkarNotificationTap(
      ReceivedAction receivedAction) async {
    try {
      if (kDebugMode) {
        print('🕌 Handling athkar notification tap');
      }

      // Ensure we're on the main thread and app is ready
      await _ensureAppReady();

      // Get athkar type from payload
      final athkarType = _getAthkarTypeFromPayload(receivedAction.payload);

      if (kDebugMode) {
        print('📿 Athkar type: $athkarType');
      }

      // Navigate to appropriate athkar screen
      await _navigateToAthkarScreen(athkarType, receivedAction.payload);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling athkar notification: $e');
      }
    }
  }

  /// Get athkar type from notification payload
  String? _getAthkarTypeFromPayload(Map<String, String?>? payload) {
    if (payload == null) return null;

    // Try different payload keys
    return payload['athkar_type'] ??
        payload['notificationType']?.replaceAll('_', '') ??
        _extractAthkarTypeFromNotificationType(payload['notificationType']);
  }

  /// Extract athkar type from notification type string
  String? _extractAthkarTypeFromNotificationType(String? notificationType) {
    if (notificationType == null) return null;

    if (notificationType.contains('morning')) return 'morning';
    if (notificationType.contains('evening')) return 'evening';
    if (notificationType.contains('dhikr')) return 'dhikr';

    return null;
  }

  /// Navigate to athkar screen based on type
  Future<void> _navigateToAthkarScreen(
      String? athkarType, Map<String, String?>? payload) async {
    try {
      // Ensure athkar controller is initialized
      if (!Get.isRegistered<AthkarController>()) {
        Get.put(AthkarController());
      }

      // Ensure layout controller is initialized
      if (!Get.isRegistered<LayoutController>()) {
        Get.put(LayoutController());
      }

      // Wait for controllers to be ready
      await Future.delayed(const Duration(milliseconds: 100));

      if (athkarType != null &&
          (athkarType == 'morning' || athkarType == 'evening')) {
        // Navigate to specific athkar category
        await _navigateToSpecificAthkarCategory(athkarType);
      } else {
        // Navigate to main athkar screen
        await _navigateToMainAthkarScreen();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error navigating to athkar screen: $e');
      }
      // Fallback to main athkar screen
      await _navigateToMainAthkarScreen();
    }
  }

  /// Navigate to specific athkar category
  Future<void> _navigateToSpecificAthkarCategory(String athkarType) async {
    try {
      final athkarController = Get.find<AthkarController>();

      // Find the appropriate category
      AthkarCategoryModel? targetCategory;

      for (final category in athkarController.athkar) {
        final categoryName = category.name?.toLowerCase() ?? '';
        if ((athkarType == 'morning' && categoryName.contains('صباح')) ||
            (athkarType == 'evening' && categoryName.contains('مساء'))) {
          targetCategory = category;
          break;
        }
      }

      if (targetCategory != null) {
        if (kDebugMode) {
          print(
              '📿 Navigating to specific athkar category: ${targetCategory.name}');
        }

        // Use layout controller to navigate
        final layoutController = Get.find<LayoutController>();
        layoutController.changeScreenLayout(
          AthkarDetailsScreen(category: targetCategory),
        );
      } else {
        // Fallback to main athkar screen
        await _navigateToMainAthkarScreen();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error navigating to specific athkar category: $e');
      }
      await _navigateToMainAthkarScreen();
    }
  }

  /// Navigate to main athkar screen
  Future<void> _navigateToMainAthkarScreen() async {
    try {
      if (kDebugMode) {
        print('📿 Navigating to main athkar screen');
      }

      // Check if we can use layout controller (app is in foreground)
      if (Get.isRegistered<LayoutController>()) {
        final layoutController = Get.find<LayoutController>();
        layoutController.changeScreenLayout(const AthkarScreen());
      } else {
        // Use regular navigation (app was closed/background)
        await Get.offAllNamed(AppRouter.kLayoutScreen);
        // Small delay to ensure layout is ready, then navigate to athkar
        await Future.delayed(const Duration(milliseconds: 500), () {
          if (Get.isRegistered<LayoutController>()) {
            final layoutController = Get.find<LayoutController>();
            layoutController.changeScreenLayout(const AthkarScreen());
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error navigating to main athkar screen: $e');
      }
    }
  }

  /// Handle other notification types
  Future<void> _handleOtherNotificationTap(
      ReceivedAction receivedAction) async {
    if (kDebugMode) {
      print('🔔 Handling other notification tap');
    }

    // For now, just navigate to main screen
    // This can be extended for other notification types
    await Get.offAllNamed(AppRouter.kLayoutScreen);
  }

  /// Ensure app is ready for navigation
  Future<void> _ensureAppReady() async {
    // Wait a bit to ensure the app is fully initialized
    await Future.delayed(const Duration(milliseconds: 200));

    // Ensure we're on the main isolate
    if (!Get.isRegistered<LayoutController>()) {
      // App might be starting up, wait a bit more
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  /// Check if app is in foreground
  bool get isAppInForeground {
    try {
      return Get.isRegistered<LayoutController>();
    } catch (e) {
      return false;
    }
  }
}
