import 'package:get/get.dart';
import 'package:salawati/core/utils/app_consts.dart';

enum NumberFormat { western, arabicIndic }

extension NumberFormatExtension on NumberFormat {
  String get name {
    switch (this) {
      case NumberFormat.western:
        return 'Western';
      case NumberFormat.arabicIndic:
        return 'Arabic-Indic';
    }
  }

  String get example {
    switch (this) {
      case NumberFormat.western:
        return '0,1,2,3...';
      case NumberFormat.arabicIndic:
        return '٠,١,٢,٣...';
    }
  }
}

class NumberFormatController extends GetxController {
  static NumberFormatController get instance => Get.find();

  final Rx<NumberFormat> _numberFormat = NumberFormat.western.obs;
  NumberFormat get numberFormat => _numberFormat.value;

  @override
  void onInit() {
    super.onInit();
    _loadPreference();
  }

  void _loadPreference() {
    final savedFormat = cacheMemory.read(NUMBER_FORMAT_PREFERENCE);
    if (savedFormat != null) {
      final format = NumberFormat.values.firstWhere(
        (format) => format.toString() == savedFormat,
        orElse: () => NumberFormat.western,
      );
      _numberFormat.value = format;
    }
  }

  Future<void> setNumberFormat(NumberFormat format) async {
    _numberFormat.value = format;
    await cacheMemory.write(NUMBER_FORMAT_PREFERENCE, format.toString());
    update();

    // Force update all GetBuilder widgets that depend on this controller
    await Get.forceAppUpdate();
  }

  String formatNumber(dynamic number) {
    final numStr = number.toString();

    if (_numberFormat.value == NumberFormat.western) {
      return numStr;
    }

    const westernDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    String result = numStr;
    for (int i = 0; i < westernDigits.length; i++) {
      result = result.replaceAll(westernDigits[i], arabicDigits[i]);
    }

    return result;
  }

  String formatTimeString(String timeString) {
    return formatNumber(timeString);
  }

  String formatDuration(Duration duration) {
    final totalSeconds = duration.inSeconds;
    final hours = (totalSeconds ~/ 3600) % 24;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;

    String formattedTime = '';

    if (_numberFormat.value == NumberFormat.arabicIndic) {
      if (hours == 0) {
        formattedTime =
            '${formatNumber(minutes.toString().padLeft(2, '0'))} د : ${formatNumber(seconds.toString().padLeft(2, '0'))} ث';
      } else {
        formattedTime =
            '${formatNumber(hours.toString())} س : ${formatNumber(minutes.toString().padLeft(2, '0'))} د : ${formatNumber(seconds.toString().padLeft(2, '0'))} ث';
      }
    } else {
      if (hours == 0) {
        formattedTime =
            '${minutes.toString().padLeft(2, '0')}m : ${seconds.toString().padLeft(2, '0')}s';
      } else {
        formattedTime =
            '${hours}h : ${minutes.toString().padLeft(2, '0')}m : ${seconds.toString().padLeft(2, '0')}s';
      }
    }

    return formattedTime;
  }

  String formatCount(int current, int total) {
    return '${formatNumber(current)} / ${formatNumber(total)}';
  }
}
