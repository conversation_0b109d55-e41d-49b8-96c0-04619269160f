import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';

/// Deployment modes
enum DeploymentMode {
  /// Conservative: Keep legacy as default, awesome for opt-in users
  conservative,
  /// Gradual: Percentage-based rollout
  gradual,
  /// Full: Awesome notifications for all users
  full,
  /// Testing: A/B testing mode
  testing,
}

/// User groups for A/B testing
enum UserGroup {
  control,    // Legacy notifications
  treatment,  // Awesome notifications
  beta,       // Beta testers (always awesome)
}

/// Production deployment configuration for awesome_notifications migration
class ProductionDeploymentConfig {
  static const String _deploymentModeKey = 'awesome_notifications_deployment_mode';
  static const String _userGroupKey = 'awesome_notifications_user_group';
  static const String _rolloutPercentageKey = 'awesome_notifications_rollout_percentage';
  static const String _forceAwesomeKey = 'force_awesome_notifications';
  static const String _deploymentVersionKey = 'deployment_version';

  static final GetStorage _storage = GetStorage();

  /// Current deployment configuration
  static DeploymentMode get currentMode {
    final modeString = _storage.read(_deploymentModeKey) ?? 'conservative';
    return DeploymentMode.values.firstWhere(
      (mode) => mode.name == modeString,
      orElse: () => DeploymentMode.conservative,
    );
  }

  /// Set deployment mode
  static Future<void> setDeploymentMode(DeploymentMode mode) async {
    await _storage.write(_deploymentModeKey, mode.name);
    if (kDebugMode) {
      print('🚀 Deployment mode set to: ${mode.name}');
    }
  }

  /// Get user group assignment
  static UserGroup get userGroup {
    final groupString = _storage.read(_userGroupKey);
    if (groupString != null) {
      return UserGroup.values.firstWhere(
        (group) => group.name == groupString,
        orElse: () => _assignUserGroup(),
      );
    }
    return _assignUserGroup();
  }

  /// Assign user to a group (for A/B testing)
  static UserGroup _assignUserGroup() {
    // Use device-specific hash for consistent assignment
    final deviceId = _storage.read('device_id') ?? DateTime.now().millisecondsSinceEpoch.toString();
    final hash = deviceId.hashCode.abs();

    // 50/50 split for A/B testing
    final group = hash % 2 == 0 ? UserGroup.control : UserGroup.treatment;
    _storage.write(_userGroupKey, group.name);

    if (kDebugMode) {
      print('👥 User assigned to group: ${group.name}');
    }

    return group;
  }

  /// Get rollout percentage (0-100)
  static int get rolloutPercentage {
    return _storage.read(_rolloutPercentageKey) ?? 0;
  }

  /// Set rollout percentage
  static Future<void> setRolloutPercentage(int percentage) async {
    final clampedPercentage = percentage.clamp(0, 100);
    await _storage.write(_rolloutPercentageKey, clampedPercentage);
    if (kDebugMode) {
      print('📊 Rollout percentage set to: $clampedPercentage%');
    }
  }

  /// Check if user should use awesome notifications
  static bool shouldUseAwesomeNotifications() {
    // Developer override
    if (_storage.read(_forceAwesomeKey) == true) {
      return true;
    }

    // Check deployment mode
    switch (currentMode) {
      case DeploymentMode.conservative:
        return false; // Default to legacy

      case DeploymentMode.full:
        return true; // All users use awesome

      case DeploymentMode.gradual:
        return _isUserInRollout();

      case DeploymentMode.testing:
        return userGroup == UserGroup.treatment || userGroup == UserGroup.beta;
    }
  }

  /// Check if user is in gradual rollout
  static bool _isUserInRollout() {
    final deviceId = _storage.read('device_id') ?? DateTime.now().millisecondsSinceEpoch.toString();
    final hash = deviceId.hashCode.abs();
    final userPercentile = hash % 100;
    return userPercentile < rolloutPercentage;
  }

  /// Force awesome notifications (for testing/debugging)
  static Future<void> forceAwesomeNotifications(bool force) async {
    await _storage.write(_forceAwesomeKey, force);
    if (kDebugMode) {
      print('🔧 Force awesome notifications: $force');
    }
  }

  /// Get deployment version
  static String get deploymentVersion {
    return _storage.read(_deploymentVersionKey) ?? '1.0.0';
  }

  /// Set deployment version
  static Future<void> setDeploymentVersion(String version) async {
    await _storage.write(_deploymentVersionKey, version);
  }

  /// Reset deployment configuration
  static Future<void> resetConfiguration() async {
    await _storage.remove(_deploymentModeKey);
    await _storage.remove(_userGroupKey);
    await _storage.remove(_rolloutPercentageKey);
    await _storage.remove(_forceAwesomeKey);
    await _storage.remove(_deploymentVersionKey);
    if (kDebugMode) {
      print('🔄 Deployment configuration reset');
    }
  }

  /// Get deployment status summary
  static Map<String, dynamic> getDeploymentStatus() {
    return {
      'mode': currentMode.name,
      'userGroup': userGroup.name,
      'rolloutPercentage': rolloutPercentage,
      'shouldUseAwesome': shouldUseAwesomeNotifications(),
      'deploymentVersion': deploymentVersion,
      'isForced': _storage.read(_forceAwesomeKey) == true,
    };
  }

  /// Production deployment presets
  static Future<void> applyPreset(String preset) async {
    switch (preset.toLowerCase()) {
      case 'conservative':
        await setDeploymentMode(DeploymentMode.conservative);
        await setRolloutPercentage(0);
        break;
      
      case 'beta':
        await setDeploymentMode(DeploymentMode.testing);
        await setRolloutPercentage(10);
        break;
      
      case 'gradual_25':
        await setDeploymentMode(DeploymentMode.gradual);
        await setRolloutPercentage(25);
        break;
      
      case 'gradual_50':
        await setDeploymentMode(DeploymentMode.gradual);
        await setRolloutPercentage(50);
        break;
      
      case 'gradual_75':
        await setDeploymentMode(DeploymentMode.gradual);
        await setRolloutPercentage(75);
        break;
      
      case 'full':
        await setDeploymentMode(DeploymentMode.full);
        await setRolloutPercentage(100);
        break;
      
      default:
        if (kDebugMode) {
          print('❌ Unknown preset: $preset');
        }
    }
  }
}
