/// The Syncfusion Flutter Date Range Picker widget allows users to easily
/// select dates or a range of dates. It has built-in views that allow quick
/// navigation to the desired date.
///
/// To use, import `package:syncfusion_flutter_datepicker/datepicker.dart`.
///
///
/// See also:
/// * [Syncfusion Flutter Date Picker product page](https://www.syncfusion.com/flutter-widgets/flutter-daterangepicker).
/// * [User guide documentation](https://help.syncfusion.com/flutter/daterangepicker/overview).
///

library datepicker;

export 'src/date_picker/date_picker.dart';
export 'src/date_picker/date_picker_manager.dart';
export 'src/date_picker/hijri_date_picker_manager.dart';
