import 'package:flutter/foundation.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import '../../features/settings/data/models/adhan_model.dart';
import '../../core/utils/app_consts.dart';


/// Adapter class to convert between legacy AdhanModel and new PrayerNotificationConfig
class NotificationModelAdapter {

  /// Convert a single AdhanModel to PrayerNotificationConfig
  static PrayerNotificationConfig adhanModelToPrayerConfig(
    String prayerName,
    AdhanModel adhanModel
  ) {
    // Check if global default notification sound is enabled
    final isGlobalDefaultSound = cacheMemory.read<bool>(DEFAULT_NOTIFICATION_SOUND) ?? false;

    if (kDebugMode) {
      print('🔄 Converting AdhanModel to PrayerNotificationConfig for $prayerName');
      print('   - isNotified: ${adhanModel.isNotified}');
      print('   - isPreNotified: ${adhanModel.isPreNotified}');
      print('   - soundIndex: ${adhanModel.soundIndex}');
      print('   - iqamah: ${adhanModel.iqamah}');
      print('   - isSilent: ${adhanModel.isSilent}');
      print('   - isGlobalDefaultSound: $isGlobalDefaultSound');
    }

    // Use default sound if either the individual prayer is set to silent OR global default is enabled
    final useDefaultSound = adhanModel.isSilent || isGlobalDefaultSound;

    if (kDebugMode) {
      print('   - Final useDefaultSound: $useDefaultSound');
    }

    return PrayerNotificationConfig(
      prayerName: prayerName,
      isNotified: adhanModel.isNotified,
      isPreNotified: adhanModel.isPreNotified,
      iqamahMinutes: adhanModel.iqamah?.toInt(),
      soundIndex: adhanModel.soundIndex,
      useDefaultSound: useDefaultSound,
    );
  }

  /// Convert the entire athan notifications map to PrayerNotificationConfig map
  static Map<String, PrayerNotificationConfig> convertAthanNotificationsMap(
    Map<String, AdhanModel> athanMap
  ) {
    if (kDebugMode) {
      print('🔄 Converting entire athan notifications map');
      print('   - Total prayers: ${athanMap.length}');
    }

    final Map<String, PrayerNotificationConfig> configMap = {};

    athanMap.forEach((prayerName, adhanModel) {
      configMap[prayerName] = adhanModelToPrayerConfig(prayerName, adhanModel);
    });

    if (kDebugMode) {
      print('✅ Conversion completed: ${configMap.length} prayer configs created');
    }

    return configMap;
  }

  /// Convert PrayerNotificationConfig back to AdhanModel (for backward compatibility)
  static AdhanModel prayerConfigToAdhanModel(PrayerNotificationConfig config) {
    return AdhanModel(
      title: config.prayerName,
      isNotified: config.isNotified,
      isPreNotified: config.isPreNotified,
      soundIndex: config.soundIndex,
      iqamah: config.iqamahMinutes?.toDouble(),
      isSilent: config.useDefaultSound,
    );
  }

  /// Get prayer times from the current prayer controller
  static PrayerTimes? getCurrentPrayerTimes() {
    try {
      // This will be implemented when we integrate with the prayer controller
      // For now, return null and let the prayer controller handle it
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting current prayer times: $e');
      }
      return null;
    }
  }

  /// Validate prayer notification configuration
  static bool validatePrayerConfig(Map<String, PrayerNotificationConfig> configs) {
    if (configs.isEmpty) {
      if (kDebugMode) {
        print('⚠️ No prayer configurations found');
      }
      return false;
    }

    // Check if at least one prayer is enabled
    final hasEnabledPrayer = configs.values.any((config) => config.isNotified);
    if (!hasEnabledPrayer) {
      if (kDebugMode) {
        print('⚠️ No prayers are enabled for notifications');
      }
      return false;
    }

    if (kDebugMode) {
      print('✅ Prayer configuration validation passed');
      final enabledPrayers = configs.entries
          .where((entry) => entry.value.isNotified)
          .map((entry) => entry.key)
          .toList();
      print('   - Enabled prayers: ${enabledPrayers.join(', ')}');
    }

    return true;
  }

  /// Get prayer name mapping for localization
  static String getPrayerDisplayName(String prayerName, String locale) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return locale == 'ar' ? 'الفجر' : 'Fajr';
      case 'sunrise':
        return locale == 'ar' ? 'الشروق' : 'Sunrise';
      case 'dhuhr':
        return locale == 'ar' ? 'الظهر' : 'Dhuhr';
      case 'asr':
        return locale == 'ar' ? 'العصر' : 'Asr';
      case 'maghrib':
        return locale == 'ar' ? 'المغرب' : 'Maghrib';
      case 'isha':
        return locale == 'ar' ? 'العشاء' : 'Isha';
      default:
        return prayerName;
    }
  }

  /// Debug helper to print configuration details
  static void debugPrintConfigurations(Map<String, PrayerNotificationConfig> configs) {
    if (!kDebugMode) return;

    print('📋 Prayer Notification Configurations:');
    configs.forEach((prayerName, config) {
      print('   $prayerName:');
      print('     - Enabled: ${config.isNotified}');
      print('     - Pre-notification: ${config.isPreNotified}');
      print('     - Sound index: ${config.soundIndex}');
      print('     - Iqamah minutes: ${config.iqamahMinutes}');
      print('     - Use default sound: ${config.useDefaultSound}');
    });
  }
}
