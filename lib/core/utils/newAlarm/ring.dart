// // import 'package:alarm/alarm.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:salawati/core/utils/app_assets.dart';
// import 'package:salawati/core/utils/app_color.dart';

// import '../custom_text.dart';

// class AthanAlarmScreen extends StatelessWidget {
//   const AthanAlarmScreen({required this.alarmSettings, super.key});

//   final AlarmSettings alarmSettings;

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SafeArea(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.spaceAround,
//           children: [
//             CustomText(
//               alarmSettings.notificationSettings.title,
//               style: Theme.of(context).textTheme.titleLarge,
//             ),
//             CustomText(
//               alarmSettings.notificationSettings.body,
//               style: Theme.of(context).textTheme.titleLarge,
//             ),
//             SvgPicture.asset(
//               AppSvgs.kMasjid,
//               // ignore: deprecated_member_use
//               color: AppColor.kOrangeColor,
//               height: 70,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceAround,
//               children: [
//                 // RawMaterialButton(
//                 //   onPressed: () {
//                 //     final now = DateTime.now();
//                 //     Alarm.set(
//                 //       alarmSettings: alarmSettings.copyWith(
//                 //         dateTime: DateTime(
//                 //           now.year,
//                 //           now.month,
//                 //           now.day,
//                 //           now.hour,
//                 //           now.minute,
//                 //         ).add(const Duration(seconds: 1)),
//                 //       ),
//                 //     ).then((_) {
//                 //       if (context.mounted) Navigator.pop(context);
//                 //     });
//                 //   },
//                 //   child: CustomText(
//                 //     'Snooze',
//                 //     style: Theme.of(context).textTheme.titleLarge,
//                 //   ),
//                 // ),
//                 RawMaterialButton(
//                   onPressed: () {
//                     Alarm.stop(alarmSettings.id).then((_) {
//                       if (context.mounted) Navigator.pop(context);
//                     });
//                   },
//                   child: CustomText(
//                     'Stop Athan',
//                     style: Theme.of(context).textTheme.titleLarge,
//                   ),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
