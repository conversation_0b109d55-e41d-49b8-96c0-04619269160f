// import 'dart:io';

// import 'package:alarm/alarm.dart';
// import 'package:flutter/material.dart';
// import 'package:salawati/core/utils/app_assets.dart';

// import '../custom_text.dart';

// class ExampleAlarmEditScreen extends StatefulWidget {
//   const ExampleAlarmEditScreen({super.key, this.alarmSettings});

//   final AlarmSettings? alarmSettings;

//   @override
//   State<ExampleAlarmEditScreen> createState() => _ExampleAlarmEditScreenState();
// }

// class _ExampleAlarmEditScreenState extends State<ExampleAlarmEditScreen> {
//   bool loading = false;

//   late bool creating;
//   late DateTime selectedDateTime;
//   late bool loopAudio;
//   late bool vibrate;
//   late double? volume;
//   late String assetAudio;
//   late String title;
//   late String body;

//   @override
//   void initState() {
//     super.initState();
//     creating = widget.alarmSettings == null;

//     if (creating) {
//       selectedDateTime = DateTime.now().add(const Duration(minutes: 1));
//       selectedDateTime = selectedDateTime.copyWith(second: 0, millisecond: 0);
//       loopAudio = true;
//       vibrate = true;
//       volume = null;
//       assetAudio = AppSounds.kShortAthan;
//     } else {
//       selectedDateTime = widget.alarmSettings!.dateTime;
//       loopAudio = widget.alarmSettings!.loopAudio;
//       vibrate = widget.alarmSettings!.vibrate;
//       volume = widget.alarmSettings!.volume;
//       assetAudio = widget.alarmSettings!.assetAudioPath;
//       title = widget.alarmSettings!.notificationSettings.title;
//       body = widget.alarmSettings!.notificationSettings.body;
//     }
//   }

//   String getDay() {
//     final now = DateTime.now();
//     final today = DateTime(now.year, now.month, now.day);
//     final difference = selectedDateTime.difference(today).inDays;

//     switch (difference) {
//       case 0:
//         return 'Today';
//       case 1:
//         return 'Tomorrow';
//       case 2:
//         return 'After tomorrow';
//       default:
//         return 'In $difference days';
//     }
//   }

//   Future<void> pickTime() async {
//     final res = await showTimePicker(
//       initialTime: TimeOfDay.fromDateTime(selectedDateTime),
//       context: context,
//     );

//     if (res != null) {
//       setState(() {
//         final now = DateTime.now();
//         selectedDateTime = now.copyWith(
//           hour: res.hour,
//           minute: res.minute,
//           second: 0,
//           millisecond: 0,
//           microsecond: 0,
//         );
//         if (selectedDateTime.isBefore(now)) {
//           selectedDateTime = selectedDateTime.add(const Duration(days: 1));
//         }
//       });
//     }
//   }

//   AlarmSettings buildAlarmSettings() {
//     final id = creating
//         ? DateTime.now().millisecondsSinceEpoch % 10000 + 1
//         : widget.alarmSettings!.id;

//     final alarmSettings = AlarmSettings(
//       id: id,
//       dateTime: selectedDateTime,
//       loopAudio: loopAudio,
//       vibrate: vibrate,
//       volume: volume,
//       assetAudioPath: assetAudio,

//       // notificationBody: 'Your alarm ($id) is ringing',

//       warningNotificationOnKill: Platform.isIOS,
//       notificationSettings: NotificationSettings(
//         stopButton: 'Stop',
//         title: title,
//         body: body,
//       ),
//     );
//     return alarmSettings;
//   }

//   void saveAlarm() {
//     if (loading) return;
//     setState(() => loading = true);
//     Alarm.set(alarmSettings: buildAlarmSettings()).then((res) {
//       if (res && mounted) Navigator.pop(context, true);
//       setState(() => loading = false);
//     });
//   }

//   void deleteAlarm() async {
//     await Alarm.stop(widget.alarmSettings!.id).then((res) {
//       if (res && mounted) Navigator.pop(context, true);
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 30),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               TextButton(
//                 onPressed: () => Navigator.pop(context, false),
//                 child: CustomText(
//                   'Cancel',
//                   style: Theme.of(context)
//                       .textTheme
//                       .titleLarge!
//                       .copyWith(color: Colors.blueAccent),
//                 ),
//               ),
//               TextButton(
//                 onPressed: saveAlarm,
//                 child: loading
//                     ? const CircularProgressIndicator()
//                     : CustomText(
//                         'Save',
//                         style: Theme.of(context)
//                             .textTheme
//                             .titleLarge!
//                             .copyWith(color: Colors.blueAccent),
//                       ),
//               ),
//             ],
//           ),
//           Text(
//             getDay(),
//             style: Theme.of(context)
//                 .textTheme
//                 .titleMedium!
//                 .copyWith(color: Colors.blueAccent.withOpacity(0.8)),
//           ),
//           RawMaterialButton(
//             onPressed: pickTime,
//             fillColor: Colors.grey[200],
//             child: Container(
//               margin: const EdgeInsets.all(20),
//               child: Text(
//                 TimeOfDay.fromDateTime(selectedDateTime).format(context),
//                 style: Theme.of(context)
//                     .textTheme
//                     .displayMedium!
//                     .copyWith(color: Colors.blueAccent),
//               ),
//             ),
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Loop alarm audio',
//                 style: Theme.of(context).textTheme.titleMedium,
//               ),
//               Switch(
//                 value: loopAudio,
//                 onChanged: (value) => setState(() => loopAudio = value),
//               ),
//             ],
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Vibrate',
//                 style: Theme.of(context).textTheme.titleMedium,
//               ),
//               Switch(
//                 value: vibrate,
//                 onChanged: (value) => setState(() => vibrate = value),
//               ),
//             ],
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Sound',
//                 style: Theme.of(context).textTheme.titleMedium,
//               ),
//               DropdownButton(
//                 value: assetAudio,
//                 items: const [
//                   DropdownMenuItem<String>(
//                     value: 'assets/sounds/full_athan.wav',
//                     child: Text('Marimba'),
//                   ),
//                   DropdownMenuItem<String>(
//                     value: 'assets/sounds/nokia.mp3',
//                     child: Text('Nokia'),
//                   ),
//                   DropdownMenuItem<String>(
//                     value: 'assets/sounds/mozart.mp3',
//                     child: Text('Mozart'),
//                   ),
//                   DropdownMenuItem<String>(
//                     value: 'assets/sounds/star_wars.mp3',
//                     child: Text('Star Wars'),
//                   ),
//                   DropdownMenuItem<String>(
//                     value: 'assets/sounds/one_piece.mp3',
//                     child: Text('One Piece'),
//                   ),
//                 ],
//                 onChanged: (value) => setState(() => assetAudio = value!),
//               ),
//             ],
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Custom volume',
//                 style: Theme.of(context).textTheme.titleMedium,
//               ),
//               Switch(
//                 value: volume != null,
//                 onChanged: (value) =>
//                     setState(() => volume = value ? 0.5 : null),
//               ),
//             ],
//           ),
//           SizedBox(
//             height: 30,
//             child: volume != null
//                 ? Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Icon(
//                         volume! > 0.7
//                             ? Icons.volume_up_rounded
//                             : volume! > 0.1
//                                 ? Icons.volume_down_rounded
//                                 : Icons.volume_mute_rounded,
//                       ),
//                       Expanded(
//                         child: Slider(
//                           value: volume!,
//                           onChanged: (value) {
//                             setState(() => volume = value);
//                           },
//                         ),
//                       ),
//                     ],
//                   )
//                 : const SizedBox(),
//           ),
//           if (!creating)
//             TextButton(
//               onPressed: deleteAlarm,
//               child: Text(
//                 'Delete Alarm',
//                 style: Theme.of(context)
//                     .textTheme
//                     .titleMedium!
//                     .copyWith(color: Colors.red),
//               ),
//             ),
//           const SizedBox(),
//         ],
//       ),
//     );
//   }
// }
