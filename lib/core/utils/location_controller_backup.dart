// import 'dart:async';
// import 'dart:io';

// import 'package:app_settings/app_settings.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';
// import 'package:logger/logger.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:salawati/core/utils/app_router.dart';
// import 'package:salawati/core/utils/lifecycle_event_handler.dart';
// import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
// import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

// import '../../features/prayer/presentation/controller/prayer_controller.dart';
// import 'app_color.dart';
// import 'app_consts.dart';
// import 'custom_text.dart';

// enum CombinedLocationState { enabled, disabled, checking, denied }

// class LocationController extends GetxController {
//   static LocationController get instance => Get.find();

//   // Add these flags
//   final RxBool _isCheckingPermissions = false.obs;
//   final RxBool isPermissionRequestLocked = false.obs;
//   final Rx<CombinedLocationState> combinedState =
//       CombinedLocationState.disabled.obs;
//   final Rx<Position?> position = Rx<Position?>(null);
//   final _logger = Logger();
//   final RxBool serviceEnabled = false.obs;
//   final Rx<LocationPermission> permissionStatus = LocationPermission.denied.obs;
//   final RxBool isPermissionRequestActive = false.obs;
//   final RxBool _isUpdatingPosition = false.obs;
//   final RxBool locationDialogShown = false.obs;
//   // Add this getter to check valid permissions
//   bool get hasValidPermission {
//     debugPrint('amin hasValidPermission ');
//     return permissionStatus.value == LocationPermission.whileInUse ||
//         permissionStatus.value == LocationPermission.always;
//   }

//   // Add this to track if we have any location access
//   bool get hasLocationAccess =>
//       serviceEnabled.value &&
//       (permissionStatus.value == LocationPermission.whileInUse ||
//           permissionStatus.value == LocationPermission.always);
//   // No stream subscription needed
//   final _permissionCooldown = const Duration(seconds: 2);
//   bool userCancelled = false;
//   @override
//   void onInit() {
//     super.onInit();
//     _initializeLocation();
//     WidgetsBinding.instance.addObserver(
//       LifecycleEventHandler(
//         resumeCallBack: () async {
//           await _handleSettingsReturn();
//         },
//       ),
//     );
//   }

//   // Get current position instead of listening to stream
//   Future<void> startListening() async {
//     try {
//       final position = await getCurrentPosition();
//       await updatePosition(position);
//     } catch (e) {
//       _logger.e("Error getting position: $e");
//     }
//   }

//   // No need for stopListening since we're not using streams

//   Future<void> _initializeLocation() async {
//     debugPrint('amin start _initializeLocation');
//     final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
//     final bool isLocationAuto = cacheMemory.read(IS_LOCATION_AUTO) ?? false;

//     if (isManualLocation || !isLocationAuto) return;

//     await checkStatus();
//     everAll([serviceEnabled, permissionStatus], (_) => _updateState());
//   }

//   // Add this getter to expose the current position
//   Position? get currentPosition => position.value;

//   Future<void> updatePosition(Position newPosition) async {
//     // 1) Don't proceed if we're already updating
//     if (_isUpdatingPosition.value) return;
//     _isUpdatingPosition.value = true;

//     try {
//       position.value = newPosition;
//       try {
//         await Get.find<PrayerController>().updateLocation(
//           newPosition.latitude,
//           newPosition.longitude,
//         );
//       } catch (e, st) {
//         _logger.w('Prayer-time fetch failed, will retry later $e - $st');
//       }

//       // 6) Trigger any UI listeners
//       update();
//     } finally {
//       _isUpdatingPosition.value = false;
//     }
//   }

//   Future<bool> checkServiceStatus() async {
//     serviceEnabled.value = await Geolocator.isLocationServiceEnabled();
//     debugPrint('amin start checkServiceStatus ${serviceEnabled.value}');

//     return serviceEnabled.value;
//   }

//   Future<LocationPermissionResult> requestLocationPermission() async {
//     isPermissionRequestLocked.value = true;
//     try {
//       final status = await Geolocator.requestPermission();

//       return _handlePermissionResult(status);
//     } finally {
//       isPermissionRequestLocked.value = false;
//     }
//   }

//   LocationPermissionResult _handlePermissionResult(LocationPermission status) {
//     switch (status) {
//       case LocationPermission.deniedForever:
//         return LocationPermissionResult.permanentlyDenied;
//       case LocationPermission.denied:
//         return LocationPermissionResult.denied;
//       case LocationPermission.whileInUse:
//       case LocationPermission.always:
//         return LocationPermissionResult.granted;
//       default:
//         return LocationPermissionResult.unknownError;
//     }
//   }

//   // Modify your checkStatus method
//   Future<void> checkStatus() async {
//     debugPrint('amin start checkStatus');
//     if (_isCheckingPermissions.value ||
//         isPermissionRequestActive.value ||
//         isPermissionRequestLocked.value) {
//       return;
//     }

//     await Future.delayed(const Duration(milliseconds: 500));

//     _isCheckingPermissions.value = true;
//     try {
//       serviceEnabled.value = await Geolocator.isLocationServiceEnabled();
//       debugPrint('amin start Geolocator.checkPermission');
//       permissionStatus.value = await Geolocator.checkPermission();
//       debugPrint(
//         'amin start Geolocator.checkPermission ${permissionStatus.value}',
//       );

//       // Remove the automatic permission request from here
//     } finally {
//       _isCheckingPermissions.value = false;
//     }
//   }

//   Future<LocationPermissionResult> handlePermissionFlow() async {
//     debugPrint('Entering handlePermissionFlow');
//     if (isPermissionRequestLocked.value || isPermissionRequestActive.value) {
//       return LocationPermissionResult.pending;
//     }

//     isPermissionRequestLocked.value = true;
//     isPermissionRequestActive.value = true;

//     try {
//       final status = await Geolocator.requestPermission();
//       // _logger.d('Received permission status: $status');

//       // Immediately update permission status
//       permissionStatus.value = status;

//       if (status == LocationPermission.denied) {
//         userCancelled = true;
//         Future.delayed(_permissionCooldown, () => userCancelled = false);
//         return LocationPermissionResult.denied;
//       }

//       // If permission granted, verify services immediately
//       // Handle granted permissions
//       if (status == LocationPermission.whileInUse ||
//           status == LocationPermission.always) {
//         final servicesEnabled = await checkServiceStatus();

//         if (!servicesEnabled) {
//           await openSystemLocationSettings();
//           return LocationPermissionResult.serviceDisabled;
//         }

//         return LocationPermissionResult.granted;
//       }

//       return _handlePermissionResult(status);
//     } finally {
//       isPermissionRequestLocked.value = false;
//       isPermissionRequestActive.value = false;
//     }
//   }

//   // Add this method to explicitly start location services
//   Future<void> initializeLocationServices() async {
//     // _logger.d('Starting location services initialization');
//     debugPrint('amin start initializeLocationServices');
//     await Future.delayed(const Duration(seconds: 1));
//     // _logger.d('After initial delay');

//     final serviceEnabled = await checkServiceStatus();
//     debugPrint('Location service enabled: $serviceEnabled');

//     // if (!serviceEnabled) {
//     //   _logger.w('Showing service disabled dialog');
//     //   await showServiceDisabledDialog();
//     //   return;
//     // }

//     await checkStatus();
//     // _logger.d('Current permission status: ${permissionStatus.value}');

//     if (!hasValidPermission) {
//       debugPrint('No valid permission, starting permission flow');
//       final result = await handlePermissionFlow();
//       debugPrint('Permission flow result: $result');

//       if (result == LocationPermissionResult.granted) {
//         debugPrint('Permission granted, fetching location');
//         await Get.find<PrayerController>().getCustomLocation();
//       }
//     } else {
//       debugPrint('amin Already has valid location permissions');
//       await getCurrentPosition();
//     }
//     update();
//   }
// }

// enum LocationPermissionResult {
//   granted,
//   denied,
//   permanentlyDenied,
//   serviceDisabled,
//   unknownError,
//   pending,
// }
