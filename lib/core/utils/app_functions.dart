// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:adhan/adhan.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_compass/flutter_compass.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' as g;
import 'package:get/get.dart' hide Response;
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:home_widget/home_widget.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mobile_device_identifier/mobile_device_identifier.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path_provider_foundation/path_provider_foundation.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vibration/vibration.dart';

import '../../features/athkar/presentation/controller/athkar_controller.dart';

class AppFunctions {
  static Response fromError(e) {
    Response response = Response(requestOptions: RequestOptions());
    try {
      if (e.response == null) {
        response.statusCode = 405;
        response.statusMessage = 'Unknown Error';
      } else {
        response.statusCode = e.response!.statusCode;
        response.statusMessage = e.response!.data['message'] ??
            e.response!.data['msg'] ??
            e.response!.data['error'] ??
            'Unknown Error';
        showErrorMessage(response.statusMessage.toString());
        logPrint('statusMessage: ${response.statusMessage}');
      }
    } catch (e) {
      response.statusCode = 405;
      response.statusMessage = 'Unknown Error';
    }
    return response;
  }

  static void customSnackbar(String title, String subtitle,
      {Color? backgroundColor, Color? textColor, Widget? icon}) {
    g.Get.rawSnackbar(
      titleText: CustomText(
        title,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
      messageText: CustomText(subtitle, style: TextStyle(color: textColor)),
      margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 24.h),
      barBlur: 16,
      backgroundColor: backgroundColor ?? Colors.white,
      icon: icon,
      snackStyle: SnackStyle.FLOATING,
      snackPosition: SnackPosition.TOP,
      borderRadius: 16,
    );
  }

  static void showSuccessMessage(String message) {
    customSnackbar(
      'Success'.tr,
      message,
      backgroundColor: Colors.green,
      textColor: Colors.white,
      icon: const Icon(
        Icons.check,
        color: Colors.white,
      ),
    );
  }

  static void showInfoMessage(String title, String message) {
    customSnackbar(
      title,
      message,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
      icon: const Icon(
        Icons.info,
        color: Colors.white,
      ),
    );
  }

  static void showWarningMessage(String? title, String message) {
    customSnackbar(
      title ?? 'Warning'.tr,
      message,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
      icon: const Icon(
        Icons.warning,
        color: Colors.white,
      ),
    );
  }

  static void showErrorMessage(String message) {
    customSnackbar(
      'Error'.tr,
      message,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      icon: const Icon(
        Icons.close,
        color: Colors.white,
      ),
    );
  }

  static String formatTime(DateTime dateTime) {
    // Use app's locale for formatting structure (AM/PM labels)
    final String languageCode = Get.locale?.languageCode ?? 'ar';

    var format = DateFormat('h:mm a', languageCode);
    final String formattedTime = format.format(dateTime);

    // Localize numbers based on device's locale
    return _localizeNumbersInString(formattedTime);
  }

  static String _localizeNumbersInString(String input) {
    final String deviceLocale = Get.locale?.languageCode ?? 'ar';
    const westernDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    late List<String> localizedDigits;

    switch (deviceLocale) {
      case 'ar':
        localizedDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        break;
      case 'fa':
        localizedDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        break;
      case 'ur':
        localizedDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        break;
      default:
        return input; // Western numerals
    }

    String result = input;
    for (int i = 0; i < westernDigits.length; i++) {
      result = result.replaceAll(westernDigits[i], localizedDigits[i]);
    }
    return result;
  }

  static String twoDigits(int n) => n.toString().padLeft(2, '0');
  static String oneDigits(int n) => n.toString().padLeft(1, '0');

  /// Localizes numbers based on the device locale
  static String localizeNumber(dynamic number) {
    final String numStr = number.toString();
    // final String locale = cacheMemory.read('lang') ?? 'ar';
    final String locale = NumberLocalization.getDeviceLocale();
    // Only convert for Arabic and Urdu locales
    if (locale == 'ar' || locale == 'ur') {
      return _convertToLocaleDigits(numStr, locale);
    }

    return numStr;
  }

  /// Converts digits to the specified locale format
  static String _convertToLocaleDigits(String numStr, String locale) {
    // Arabic/Hindi numerals mapping
    final arabicNumerals = {
      '0': '٠',
      '1': '١',
      '2': '٢',
      '3': '٣',
      '4': '٤',
      '5': '٥',
      '6': '٦',
      '7': '٧',
      '8': '٨',
      '9': '٩'
    };

    // Use the same mapping for both Arabic and Urdu for now
    // If needed, we can add a separate mapping for Urdu later

    return numStr
        .split('')
        .map((char) => arabicNumerals[char] ?? char)
        .join('');
  }

  static String formatDuration(Duration duration) {
    final String local = NumberLocalization.getDeviceLocale();

    // var local = cacheMemory.read('lang') ?? 'ar';
    var totalSeconds = duration.inSeconds;

    // Calculate hours, wrapping around after 24 hours
    var hours = (totalSeconds ~/ 3600) % 24;

    // Calculate remaining minutes and seconds
    var minutes = (totalSeconds % 3600) ~/ 60;
    var seconds = totalSeconds % 60; // Calculate seconds

    String formattedTime = '';

    if (local == 'ar') {
      formattedTime =
          formatArabic(hours, minutes, seconds); // Pass seconds to formatArabic
    } else {
      formattedTime = formatEnglish(
          hours, minutes, seconds); // Pass seconds to formatEnglish
    }

    // Return a formatted string without using trParams
    return '${getLocalizedText(local, 'time_left')} $formattedTime';
  }

  static String formatArabic(int hours, int minutes, int seconds) {
    if (hours == 0) {
      return '${localizeNumber(twoDigits(minutes))} د : ${localizeNumber(twoDigits(seconds))} ث';
    } else {
      return '${localizeNumber(oneDigits(hours))} س : ${localizeNumber(twoDigits(minutes))} د : ${localizeNumber(twoDigits(seconds))} ث';
    }
  }

  static String formatEnglish(int hours, int minutes, int seconds) {
    if (hours == 0) {
      return '${minutes.toString().padLeft(2, '0')}m : ${seconds.toString().padLeft(2, '0')}s'; // Added seconds here
    } else {
      return '${hours.toString()}h : ${minutes.toString().padLeft(2, '0')}m : ${seconds.toString().padLeft(2, '0')}s';
    }
  }

  static String getLocalizedText(String langCode, String key) {
    switch (langCode) {
      case 'ar':
        return 'الوقت المتبقي';
      case 'fr':
        return 'Temps restant';
      case 'ur':
        return 'باقی وقت';
      default:
        return 'Time left';
    }
  }

  static Duration nextPrayerRemainingTime(PrayerTimes prayerTimes) {
    return nextPrayerTime(prayerTimes)
        .difference(DateTime.now().add(prayerTimes.utcOffset!));
  }

  static String formatTimeAmPMArabic(DateTime dateTime) {
    // Determine AM/PM based on the current locale
    // final String languageCode = Get.locale?.languageCode ?? "ar";
    final String languageCode = NumberLocalization.getDeviceLocale();

    if (languageCode == 'ar') {
      // Arabic AM/PM
      return dateTime.hour < 12 ? 'ص' : 'م';
    } else if (languageCode == 'ur') {
      // Urdu AM/PM
      return dateTime.hour < 12 ? 'ص' : 'ش';
    } else if (languageCode == 'fa') {
      // Persian AM/PM
      return dateTime.hour < 12 ? 'ق.ظ' : 'ب.ظ';
    } else {
      // Default English AM/PM
      return dateTime.hour < 12 ? 'AM' : 'PM';
    }
  }

  static String formatTimeWithoutAMPM(DateTime dateTime) {
    // Create a formatter for the time part
    var timeFormatter = DateFormat('h:mm', 'en_US');

    // Format the time
    var time = timeFormatter.format(dateTime);

    // For Arabic and other RTL languages, we need to localize the numbers
    // final String languageCode = Get.locale?.languageCode ?? "ar";
    final String languageCode = NumberLocalization.getDeviceLocale();
    if (languageCode == 'ar' || languageCode == 'ur' || languageCode == 'fa') {
      // Split the time into parts (hour, minute)
      final RegExp timeRegex = RegExp(r'(\d+):(\d+)');
      final match = timeRegex.firstMatch(time);

      if (match != null) {
        final hour = match.group(1)!;
        final minute = match.group(2)!;

        // Localize the hour and minute
        final localizedHour =
            NumberLocalization.localizeNumber(int.parse(hour));
        final localizedMinute =
            NumberLocalization.localizeNumber(int.parse(minute));

        // Return the localized time
        return '$localizedHour:$localizedMinute';
      }
    }

    return time;
  }

  static Prayer previousPrayer(PrayerTimes prayerTimes) {
    final now = DateTime.now()
        .add(prayerTimes.utcOffset!)
        .subtract(Duration(hours: SettingsController.instance.getSummerTime()))
        .millisecondsSinceEpoch;

    var prayerTimesMap = <int, Prayer>{
      prayerTimes.fajr.millisecondsSinceEpoch: Prayer.fajr,
      prayerTimes.sunrise.millisecondsSinceEpoch: Prayer.sunrise,
      prayerTimes.dhuhr.millisecondsSinceEpoch: Prayer.dhuhr,
      prayerTimes.asr.millisecondsSinceEpoch: Prayer.asr,
      prayerTimes.maghrib.millisecondsSinceEpoch: Prayer.maghrib,
      prayerTimes.isha.millisecondsSinceEpoch: Prayer.isha,
    };
    var prayerTimesList = prayerTimesMap.keys.toList();
    prayerTimesList.add(now);
    prayerTimesList.sort();
    var index = prayerTimesList.indexOf(now);

    if (index == 0) {
      return Prayer.isha; // Previous day's Isha if before Fajr
    } else {
      return prayerTimesMap[prayerTimesList[index - 1]]!;
    }
  }

  static DateTime previousPrayerTime(PrayerTimes prayerTimes) {
    var previous = previousPrayer(prayerTimes);
    var time = getPrayerTime(previous.name, prayerTimes);

    final now = DateTime.now()
        .add(prayerTimes.utcOffset!)
        .subtract(Duration(hours: SettingsController.instance.getSummerTime()));

    // Adjust for previous day's Isha if current time is before Fajr
    if (previous == Prayer.isha && now.isBefore(prayerTimes.fajr)) {
      time = time.subtract(const Duration(days: 1));
    }

    return time;
  }

  static DateTime nextPrayerTime(PrayerTimes prayerTimes) {
    String prayerName = nextPrayer(prayerTimes).name;
    DateTime time = getPrayerTime(prayerName, prayerTimes);
    if (prayerName == 'fajr') {
      time = time.add(const Duration(days: 1));
    }
    return time;
  }

  static Prayer nextPrayer(PrayerTimes prayerTimes) {
    // Get current time in UTC
    final now = DateTime.now()
        .add(prayerTimes.utcOffset!)
        .subtract(Duration(hours: SettingsController.instance.getSummerTime()))
        .millisecondsSinceEpoch;

    Map<int, Prayer> prayerTimesMap = {
      prayerTimes.fajr.millisecondsSinceEpoch: Prayer.fajr,
      prayerTimes.sunrise.millisecondsSinceEpoch: Prayer.sunrise,
      prayerTimes.dhuhr.millisecondsSinceEpoch: Prayer.dhuhr,
      prayerTimes.asr.millisecondsSinceEpoch: Prayer.asr,
      prayerTimes.maghrib.millisecondsSinceEpoch: Prayer.maghrib,
      prayerTimes.isha.millisecondsSinceEpoch: Prayer.isha,
    };
    List<int> prayerTimesList = prayerTimesMap.keys.toList();
    prayerTimesList.add(now);
    prayerTimesList.sort();

    int index = prayerTimesList.indexOf(now);

    Prayer nextPrayer;
    if (index + 1 == prayerTimesList.length) {
      nextPrayer = prayerTimesMap[prayerTimesList[0]]!;
    } else {
      nextPrayer = prayerTimesMap[prayerTimesList[index + 1]]!;
    }

    return nextPrayer;
  }

  static SunnahPrayer nextPrayerForSunnah(
      PrayerTimes prayerTimes, SunnahTimes sunnahTimes) {
    // Get current time in UTC
    final now = DateTime.now()
        .add(prayerTimes.utcOffset!)
        .subtract(Duration(hours: SettingsController.instance.getSummerTime()))
        .millisecondsSinceEpoch;

    Map<int, SunnahPrayer> prayerTimesMap = {
      sunnahTimes.middleOfTheNight.millisecondsSinceEpoch:
          SunnahPrayer.MiddleOfTheNight,
      sunnahTimes.lastThirdOfTheNight.millisecondsSinceEpoch:
          SunnahPrayer.LastThirdOfTheNight,
    };
    List<int> prayerTimesList = prayerTimesMap.keys.toList();
    prayerTimesList.add(now);
    prayerTimesList.sort();

    int index = prayerTimesList.indexOf(now);

    SunnahPrayer nextPrayer;
    if (index + 1 == prayerTimesList.length) {
      nextPrayer = prayerTimesMap[prayerTimesList[0]]!;
    } else {
      nextPrayer = prayerTimesMap[prayerTimesList[index + 1]]!;
    }

    return nextPrayer;
  }

  static DateTime getPrayerTime(String prayerName, PrayerTimes prayerTimes) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return prayerTimes.fajr;
      case 'sunrise':
        return prayerTimes.sunrise;
      case 'dhuhr':
        return prayerTimes.dhuhr;
      case 'asr':
        return prayerTimes.asr;
      case 'maghrib':
        return prayerTimes.maghrib;
      case 'isha':
        return prayerTimes.isha;
      default:
        throw ArgumentError('Invalid prayer name: $prayerName');
    }
  }

  static DateTime getSunnahPrayerTime(
      String prayerName, SunnahTimes sunnahPrayer) {
    switch (prayerName.toLowerCase()) {
      case 'middle of the night':
        return sunnahPrayer.middleOfTheNight;
      case 'last third of the night':
        return sunnahPrayer.lastThirdOfTheNight;
      default:
        throw ArgumentError('Invalid Sunnah Prayer name: $prayerName');
    }
  }

  static void preventRotateScreen() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  static void copyTextToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
  }

  static void shareText(BuildContext context, String text) {
    final box = context.findRenderObject() as RenderBox?;
    Share.share(
      text,
      sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
    );
  }

  static Future<String> getAccessToken() async {
    String? tokenId;
    await GoogleSignIn().signOut();
    final GoogleSignInAccount? googleAccount = await GoogleSignIn().signIn();
    final GoogleSignInAuthentication? googleAuth =
        await googleAccount?.authentication;
    return tokenId ?? googleAuth!.accessToken ?? '-1';
  }

  static Future<Map<String, dynamic>> getAppleAccessToken() async {
    Map<String, dynamic>? userData = {};

    final appleCredential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    String email = "";
    String name = "";
    var storage = GetStorage();
    if (appleCredential.email != null) {
      email = appleCredential.email!;
      await storage.write('apple_email', email);
    } else {
      email = storage.read('apple_email') ?? "";
    }
    if (appleCredential.givenName != null &&
        appleCredential.familyName != null) {
      name = "${appleCredential.givenName} ${appleCredential.familyName}";
      await storage.write('apple_name', name);
    } else {
      name = storage.read('apple_name') ?? "";
    }
    userData = {
      'accessToken': appleCredential.identityToken,
      'email': email,
      'name': name,
    };
    // debugPrint((await SignInWithApple.getKeychainCredential()).toString());
    return userData;
  }

  static Future<String?> getDeviceToken() async {
    final mobileDeviceIdentifier = await MobileDeviceIdentifier().getDeviceId();
    return mobileDeviceIdentifier;
  }

  static String ibadahTypeIntoNoun(String type) {
    switch (type) {
      case 'daily':
        return 'day';
      case 'weekly':
        return 'week';
      case 'monthly':
        return 'month';
      case 'annually':
        return 'year';
    }
    return 'day';
  }

  static Future<void> vibrate(bool isShort) async {
    if (isShort) {
      await HapticFeedback.heavyImpact();
    } else {
      await HapticFeedback.vibrate();
    }
  }

  static Future<void> playSound() async {
    var controller = Get.find<AthkarController>();
    if (controller.deviceVibration.value) {
      final player = AudioPlayer();
      try {
        await player.setAsset('assets/sounds/ding.mp3');
        await player.play();
      } on PlayerException catch (e) {
        // iOS/macOS: maps to NSError.code
        // Android: maps to ExoPlayerException.type
        // Web: maps to MediaError.code
        // Linux/Windows: maps to PlayerErrorCode.index
        debugPrint("Error code: ${e.code}");
        // iOS/macOS: maps to NSError.localizedDescription
        // Android: maps to ExoPlaybackException.getMessage()
        // Web/Linux: a generic message
        // Windows: MediaPlayerError.message
        debugPrint("Error message: ${e.message}");
      } on PlayerInterruptedException catch (e) {
        // This call was interrupted since another audio source was loaded or the
        // player was stopped or disposed before this audio source could complete
        // loading.
        debugPrint("Connection aborted: ${e.message}");
      } catch (e) {
        // Fallback for all other errors
        debugPrint('An error occured: $e');
      }
    }
  }

  static Future<void> newVibrate() async {
    var controller = Get.find<AthkarController>();
    if (controller.deviceVibration.value) {
      await Vibration.vibrate(duration: 100);
    }
  }

  static Future<void> initializeDateLocales() async {
    await Future.wait([
      initializeDateFormatting('fr', null),
      initializeDateFormatting('ar', null),
      initializeDateFormatting('ur', null),
      initializeDateFormatting('en', null),
    ]);
  }

  static Future<bool> showExitPopup(context) async {
    return await g.Get.dialog(AlertDialog(
          contentPadding: EdgeInsets.all(16.w),
          backgroundColor: Theme.of(context).primaryColor,
          content: CustomText("Are you sure to close the app?"),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () => Navigator.of(context).pop(false),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CustomText(
                      'cancel',
                    ),
                  ),
                ),
                8.horizontalSpace,
                InkWell(
                  onTap: () => Navigator.of(context).pop(true),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CustomText(
                      'ok',
                    ),
                  ),
                ),
              ],
            )
          ],
        )) ??
        false;
  }

  static Future<BitmapDescriptor> customMarker() {
    return BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(size: Size(5, 5)),
      'assets/images/mosque_marker.png',
    );
  }

  static Future<bool> hasCompassSensor() async {
    try {
      CompassEvent? event = await FlutterCompass.events
          ?.firstWhere((event) => event.heading != null);

      if (event != null) {
        double userHeading = event.heading!;
        if (userHeading == 0.0) {
          return false;
        }
      }
    } catch (e) {
      return false;
    }
    return true;
  }

  static Future<void> rateApp() async {
    String playStoreRateUrl = '$playStoreUrl&reviewstatus=ALL';
    String appStoreRateUrl = '$appStoreUrl?action=write-review';
    if (Platform.isAndroid) {
      if (await canLaunch(playStoreRateUrl)) {
        await launch(playStoreRateUrl);
      } else {
        throw 'Could not launch $playStoreUrl';
      }
    } else if (Platform.isIOS) {
      if (await canLaunch(appStoreRateUrl)) {
        await launch(appStoreRateUrl);
      } else {
        throw 'Could not launch $appStoreUrl';
      }
    } else {
      throw 'Unsupported platform';
    }
  }

  static Future<void> shareApp(BuildContext context) async {
    String text = "share text".trParams({
      'android_link': playStoreUrl,
      'ios_link': appStoreUrl,
    });
    shareText(context, text);
  }

  static Future<String> renderFlutterWidgetAsHomeWidget(
    Widget widget, {
    required String key,
    Size logicalSize = const Size(200, 200),
    double pixelRatio = 1,
    Duration delay = const Duration(seconds: 1),
  }) async {
    ScreenshotController screenshotController = ScreenshotController();

    var image = await screenshotController.captureFromWidget(
      widget,
      pixelRatio: pixelRatio,
      targetSize: logicalSize,
      delay: delay,
    );
    try {
      late final String? directory;

      if (Platform.isIOS) {
        final PathProviderFoundation provider = PathProviderFoundation();
        assert(
          HomeWidget.groupId != null,
          'No groupId defined. Did you forget to call `HomeWidget.setAppGroupId`',
        );
        directory = await provider.getContainerPath(
          appGroupIdentifier: HomeWidget.groupId!,
        );
      } else {
        // coverage:ignore-end
        directory = (await getApplicationSupportDirectory()).path;
      }

      final String path = '$directory/home_widget/$key.png';
      final File file = File(path);
      if (!await file.exists()) {
        await file.create(recursive: true);
      }
      await file.writeAsBytes(image.buffer.asUint8List());

      // Save the filename to UserDefaults if a key was provided
      await HomeWidget.saveWidgetData<String>(key, path);

      return path;
    } catch (e) {
      throw Exception('Failed to save screenshot to app group container: $e');
    }
  }

  static Future<void> contact() async {
    final Uri params = Uri(
      scheme: 'mailto',
      path: SUPPORT_EMAIL,
    );

    var url = params.toString();
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }
}

/// Utility class for localizing numbers based on device locale
class NumberLocalization {
  // Flag to force Western numerals regardless of locale
  static bool useWesternNumerals = false;

  /// Get the device locale for number formatting
  static String getDeviceLocale() {
    // Get the device's locale
    final String deviceLocale = Platform.localeName.split('_')[0];

    if (useWesternNumerals) {
      return 'en';
    }

    // Check if the device's locale uses non-Western numerals
    if (['ar', 'fa', 'ur'].contains(deviceLocale)) {
      return deviceLocale;
    }

    // Default to Western numerals if not in the list
    return 'en';
  }

  /// Convert a number to a localized string based on the device locale
  static String localizeNumber(num number, {int? decimalPlaces}) {
    try {
      // Get the current locale from the device
      final String localeCode = getDeviceLocale();

      // Format the number based on the locale
      if (decimalPlaces != null) {
        return _formatWithDecimalPlaces(number, localeCode, decimalPlaces);
      } else {
        return _formatNumber(number, localeCode);
      }
    } catch (e) {
      debugPrint('Error localizing number: $e');
      return number.toString();
    }
  }

  /// Format a number with a specific number of decimal places
  static String _formatWithDecimalPlaces(
      num number, String languageCode, int decimalPlaces) {
    // Create a number format with the specified decimal places
    final NumberFormat formatter =
        _getNumberFormatter(languageCode, decimalPlaces);
    return formatter.format(number);
  }

  /// Format a number without specifying decimal places
  static String _formatNumber(num number, String languageCode) {
    // For integers, don't show decimal places
    if (number is int) {
      return _getNumberFormatter(languageCode, 0).format(number);
    }

    // For doubles, show up to 2 decimal places if needed
    final double doubleValue = number.toDouble();
    if (doubleValue == doubleValue.roundToDouble()) {
      return _getNumberFormatter(languageCode, 0).format(doubleValue);
    } else {
      return _getNumberFormatter(languageCode, 2).format(doubleValue);
    }
  }

  /// Get a number formatter for the specified locale and decimal places
  static NumberFormat _getNumberFormatter(
      String languageCode, int decimalPlaces) {
    // Define the pattern based on decimal places
    String pattern = '#,##0';
    if (decimalPlaces > 0) {
      pattern += '.';
      for (int i = 0; i < decimalPlaces; i++) {
        pattern += '0';
      }
    }

    // Create the formatter with the appropriate locale
    switch (languageCode) {
      case 'ar':
        return NumberFormat(pattern, 'ar');
      case 'ur':
        return NumberFormat(pattern, 'ur');
      case 'fa':
        return NumberFormat(pattern, 'fa');
      default:
        return NumberFormat(pattern, 'en');
    }
  }

  /// Format a time duration in minutes and seconds (MM:SS)
  static String formatDuration(Duration duration) {
    final String localeCode = getDeviceLocale();
    final int minutes = duration.inMinutes;
    final int seconds = duration.inSeconds % 60;

    final String minutesStr = _formatWithDecimalPlaces(minutes, localeCode, 0);
    final String secondsStr = seconds < 10
        ? '0${_formatWithDecimalPlaces(seconds, localeCode, 0)}'
        : _formatWithDecimalPlaces(seconds, localeCode, 0);

    return '$minutesStr:$secondsStr';
  }

  /// Localize a time string in format "HH:MM"
  static String localizeTimeString(String timeString) {
    final String locale = getDeviceLocale();

    // If not using Arabic, Urdu, or Persian, return as is
    if (locale != 'ar' && locale != 'ur' && locale != 'fa') {
      return timeString;
    }

    // Split the time string into hours and minutes
    final parts = timeString.split(':');
    if (parts.length != 2) return timeString;

    // Localize each part
    final localizedHour = localizeNumber(int.parse(parts[0]));
    final localizedMinute = localizeNumber(int.parse(parts[1]));

    // Return the localized time string
    return '$localizedHour:$localizedMinute';
  }
}
