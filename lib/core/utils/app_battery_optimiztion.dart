import 'dart:io';
import 'package:disable_battery_optimization/disable_battery_optimization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:salawati/core/utils/app_consts.dart';

class AppBatteryOptimization {
  static final RxBool isBatteryOptimizationEnabled = false.obs;

  static Future<bool> isBatteryOptimizationAndAutoStartEnabled() async {
    if (Platform.isAndroid) {
      try {
        // Step 1: Check battery optimization permission status
        var status = await Permission.ignoreBatteryOptimizations.status;
        if (status.isDenied || status.isPermanentlyDenied) {
          debugPrint('Battery optimization permission is denied.');
          return false; // Return false if permission is denied
        }

        // Step 2: Check battery optimization state
        bool? isBatteryOptimizationDisabled =
            await DisableBatteryOptimization.isBatteryOptimizationDisabled;
        bool isBatteryOptimizationEnabled = !isBatteryOptimizationDisabled!;

        // Step 3: Check auto-start state
        bool? isAutoStartEnabled =
            await DisableBatteryOptimization.isAutoStartEnabled;

        // Debug prints
        debugPrint(
            'Battery optimization disabled: $isBatteryOptimizationDisabled');
        debugPrint(
            'Battery optimization enabled: $isBatteryOptimizationEnabled');
        debugPrint('Auto-start enabled: $isAutoStartEnabled');

        // Return true if either battery optimization is disabled or auto-start is enabled
        return isBatteryOptimizationEnabled || isAutoStartEnabled!;
      } catch (e) {
        debugPrint(
            'Error checking battery optimization and auto-start state: $e');
        return false;
      }
    }
    return false;
  }

  static Future<void> handleBatteryOptimizationSwitch(bool value) async {
    if (value) {
      // Step 1: Check and request battery optimization permission
      var status = await Permission.ignoreBatteryOptimizations.status;
      if (status.isDenied) {
        status = await Permission.ignoreBatteryOptimizations.request();
        if (status.isPermanentlyDenied) {
          _showErrorSnackBar(
              'Please enable battery optimization permissions manually in settings.'
                  .tr);
          return; // Do not turn on the switch if permission is denied
        }
      }

      // Step 2: Initialize auto-start and battery optimization settings
      await initAutoStart();

      // Step 3: Save the state in GetStorage based on the real-time state
      isBatteryOptimizationEnabled.value =
          await isBatteryOptimizationAndAutoStartEnabled();
      await cacheMemory.write(
          iS_BATTERY_OPTIMIZATION_ENABLED, isBatteryOptimizationEnabled.value);
    } else {
      // Step 4: Save the state in GetStorage
      await cacheMemory.write(iS_BATTERY_OPTIMIZATION_ENABLED, value);
    }
  }

  static Future<void> initAutoStart() async {
    if (Platform.isAndroid) {
      try {
        // Step 1: Request ignore battery optimizations permission
        await _handleBatteryOptimizationPermission();

        // Step 2: Check and enable auto-start
        await _handleAutoStartSettings();

        // Save the state in GetStorage
        await cacheMemory.write(iS_BATTERY_OPTIMIZATION_ENABLED, true);
      } on PlatformException catch (e) {
        debugPrint('Error: $e');
        _showErrorSnackBar(
            'Failed to configure battery optimization settings.'.tr);
        // Save the state in GetStorage as false if it fails
        await cacheMemory.write(iS_BATTERY_OPTIMIZATION_ENABLED, false);
      }
    }
  }

  static Future<void> _handleBatteryOptimizationPermission() async {
    try {
      var status = await Permission.ignoreBatteryOptimizations.status;
      if (status.isDenied) {
        status = await Permission.ignoreBatteryOptimizations.request();
        if (status.isPermanentlyDenied) {
          _showErrorSnackBar(
              'Please enable battery optimization permissions manually in settings.'
                  .tr);
        }
      }
      debugPrint('Battery optimization permission status: $status');
    } catch (e) {
      debugPrint('Error handling battery optimization permission: $e');
      rethrow;
    }
  }

  static Future<void> _handleAutoStartSettings() async {
    try {
      bool? isAutoStartEnabled =
          await DisableBatteryOptimization.isAutoStartEnabled;
      debugPrint(
          "Auto start is ${isAutoStartEnabled! ? "Enabled" : "Disabled"}");
      if (!isAutoStartEnabled) {
        await DisableBatteryOptimization.showEnableAutoStartSettings(
          "Enable Auto Start".tr,
          "Please enable auto-start for the app.".tr,
        );
      }
    } catch (e) {
      debugPrint('Error handling auto-start settings: $e');
      rethrow;
    }
  }

  static void _showErrorSnackBar(String message) {
    // Get.snackbar(
    //   'Error'.tr, // Title
    //   message.tr, // Message
    //   snackPosition: SnackPosition.BOTTOM, // Position of the snackbar
    //   duration: const Duration(seconds: 5), // Duration to display
    // );
  }
}
