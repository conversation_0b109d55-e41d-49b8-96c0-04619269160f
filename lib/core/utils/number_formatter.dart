import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:salawati/core/providers/number_format_provider.dart';

class NumberFormatter {
  static String formatNumber(dynamic number, WidgetRef ref) {
    final numberFormat = ref.watch(numberFormatProvider);
    final notifier = ref.read(numberFormatProvider.notifier);
    return notifier.formatNumber(number);
  }

  static String formatTimeString(String timeString, WidgetRef ref) {
    final numberFormat = ref.watch(numberFormatProvider);
    final notifier = ref.read(numberFormatProvider.notifier);
    return notifier.formatTimeString(timeString);
  }

  static String formatDuration(Duration duration, WidgetRef ref) {
    final numberFormat = ref.watch(numberFormatProvider);
    final notifier = ref.read(numberFormatProvider.notifier);

    final totalSeconds = duration.inSeconds;
    final hours = (totalSeconds ~/ 3600) % 24;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;

    String formattedTime = '';

    if (numberFormat == NumberFormat.arabicIndic) {
      if (hours == 0) {
        formattedTime =
            '${notifier.formatNumber(minutes.toString().padLeft(2, '0'))} د : ${notifier.formatNumber(seconds.toString().padLeft(2, '0'))} ث';
      } else {
        formattedTime =
            '${notifier.formatNumber(hours.toString())} س : ${notifier.formatNumber(minutes.toString().padLeft(2, '0'))} د : ${notifier.formatNumber(seconds.toString().padLeft(2, '0'))} ث';
      }
    } else {
      if (hours == 0) {
        formattedTime =
            '${minutes.toString().padLeft(2, '0')}m : ${seconds.toString().padLeft(2, '0')}s';
      } else {
        formattedTime =
            '${hours}h : ${minutes.toString().padLeft(2, '0')}m : ${seconds.toString().padLeft(2, '0')}s';
      }
    }

    return formattedTime;
  }

  static String formatCount(int current, int total, WidgetRef ref) {
    final notifier = ref.read(numberFormatProvider.notifier);
    return '${notifier.formatNumber(current)} / ${notifier.formatNumber(total)}';
  }

  static String formatSimpleNumber(dynamic number, WidgetRef ref) {
    final notifier = ref.read(numberFormatProvider.notifier);
    return notifier.formatNumber(number);
  }
}
