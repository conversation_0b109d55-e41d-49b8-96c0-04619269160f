// ignore_for_file: constant_identifier_names, non_constant_identifier_names
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:salawati/core/utils/app_color.dart';

final List<Color> colors = [
  AppColor.kScaffoldColor,
  const Color(0xff2F3A37),
  const Color(0xff5E5B56),
  const Color(0xff3A1352),
  const Color(0xff49508C),
  const Color(0xff3C607A),
  const Color(0xff153194),
  const Color(0xff3F425E),
  const Color(0xff1D1E22),
  const Color(0xff215D61),
];

const String baseUrl = "https://salawati.smart-fingers.com/api/";
const String imageBaseUrl = "https://salawati.smart-fingers.com";
const String GOOGLE_API_KEY = "AIzaSyBjkXcq2bTHPwuEEkShxGTdRJsUQ5GnwYI";
// const String GOOGLE_API_KEY = "";

const String TOKEN = "token";
const String PROFILE = "profile";

//endpoints
const String SIGN_IN_WITH_GOOGLE = "sign_in_with_google";
const String SIGN_IN_WITH_APPLE = "sign_in_with_apple";
const String LOGOUT_END_POINT = 'logout';
const String GET_USER_IBADAT_END_POINT = 'get_user_ibadat';
const String ADD_USER_IBADAH_END_POINT = 'add_user_ibadah';

void logPrint(String message) {
  if (!kReleaseMode) {
    debugPrint(message);
  }
}

final cacheMemory = GetStorage();
RegExp doubleRegExp = RegExp(r'^\d+\.?\d{0,2}');
double kRadius = 10.r;
Options options() {
  return Options(
    headers: {
      'Authorization': 'Bearer ${cacheMemory.read(TOKEN)}',
      "content-type": "application/json",
      "Accept": "application/json",
    },
  );
}

List<String> prayers = [FAJR, SUNRISE, DHUHR, ASR, MAGHRIB, ISHA];
List<String> sunnahPrayers = [MIDDLEOFTHENIGHT, LASTTHIRDOFTHENIGHT];

enum SunnahPrayer { MiddleOfTheNight, LastThirdOfTheNight }

const String FAJR = 'Fajr';
const String DHUHR = 'Dhuhr';
const String SUNRISE = 'Sunrise';
const String ASR = 'Asr';
const String MAGHRIB = 'Maghrib';
const String ISHA = 'Isha';
const String MIDDLEOFTHENIGHT = 'Middle Of The Night';
const String LASTTHIRDOFTHENIGHT = 'Last Third Of The Night';

const String IS_SUMMER_TIME_AUTO = 'is_summer_time_auto';
const String SUMMER_TIME_HOUR = 'summer_time_hour';
const String IS_LOCATION_AUTO = 'is_location_auto';
const String IS_CALCULATION_METHOD_AUTO = 'is_calculation_method_auto';
const String IS_MATHHAB_HANAFI = 'is_mathhab_hanafi';
const String CALCULATION_METHOD = 'calculation_method';
const String CALCULATION_METHOD_TITLE = 'calculation_method_title';

const String LONGITUDE = 'longitude';
const String LATITUDE = 'latitude';
const String HIGH_LATITUDE = 'HIGH_LATITUDE';
const String DATE_CORRECTION_ON = 'isDateCorrectionOn';
const String DATE_CORRECTION_VALUE = 'DateCorrectionValue';
const String TASBEEH_LIST = 'tasbeehat_list';
const String SENSOR = 'sensor';
const String INSTALL_DATE = 'install_date';
String MANUAL_CORRECTION(title) => 'manual$title';
String ADHAN_NOTIFCATIONS(title) => 'adhan_notifications$title';
String ADHAN_SOUND = 'adhan_sound';
String DEFAULT_NOTIFICATION_SOUND = 'default_notification_sound';
const double KAABA_LATITUDE = 21.422500;
const double KAABA_LONGITUDE = 39.826167;

const String SUPPORT_EMAIL = '<EMAIL>';
const String IS_HASIB_ALNAFS_NOTIFICATION_ON = 'is_Hasb_AlNafs_Notification_On';
const String IS_HADEETH_NOTIFICATION_ON = 'is_hadeeth_Notification_On';
const String IS_EVENTS_NOTIFICATION_ON = 'is_events_Notification_On';
const String IS_MORNING_ATHKAR_NOTIFICATION_ON =
    'is_morning_athkar_Notification_On';
const String IS_EVENING_ATHKAR_NOTIFICATION_ON =
    'is_evening_athkar_Notification_On';
const String IS_SOUND_ATHKAR_NOTIFICATION_ON =
    'is_sound_athkar_Notification_On';
const String iS_BATTERY_OPTIMIZATION_ENABLED =
    'is_Battery_Optimization_Enabled';
const String CITY = 'city';
const String COUNTRY = 'country';
const String START_TIME_KEY = 'start_time';
const String END_TIME_KEY = 'end_time';
const String DAILY_NOTIFICATIONS_PAYLOAD = 'dailyNotifications';
const String SOUND_NOTIFICATIONS_PAYLOAD = 'soundNotifications';
const String ATHAN_NOTIFICATIONS_PAYLOAD = 'athanNotifications';
const String NOTIFICATIONS_WARNING_PAYLOAD = 'notificationsWarning';
const String USER_SET_LOCATION = 'user_set_location';
const String PENDING_AUTO_LOCATION = 'pending_auto_location';



enum ZakahType { money, gold, silver }

Map<ZakahType, String> ZakahTypeLabels = {
  ZakahType.money: 'Money Zakah',
  ZakahType.gold: 'Gold Zakah',
  ZakahType.silver: 'Silver Zakah'
};

String playStoreUrl =
    'https://play.google.com/store/apps/details?id=com.salawati.app';
String appStoreUrl = 'https://apps.apple.com/app/id6503213568';
const String packageName = 'com.salawati.app';
