// import 'dart:async';
// import 'dart:io';

// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';
// import 'package:logger/logger.dart';
// import 'package:salawati/features/location/data/services/location_service.dart';

// import 'app_consts.dart';

// // This enum is kept for backward compatibility
// enum CombinedLocationState { enabled, disabled, checking, denied }

// // This class is a wrapper around LocationService for backward compatibility
// class LocationController extends GetxController {
//   static LocationController get instance => Get.find();

//   // Dependencies
//   final _logger = Logger();
//   final LocationService _locationService = Get.find<LocationService>();

//   // Reactive state for backward compatibility
//   final Rx<CombinedLocationState> combinedState = CombinedLocationState.disabled.obs;
//   final Rx<Position?> position = Rx<Position?>(null);
//   final RxBool isPermissionRequestLocked = false.obs;
//   final RxBool serviceEnabled = false.obs;
//   final Rx<LocationPermission> permissionStatus = LocationPermission.denied.obs;
//   final RxBool isPermissionRequestActive = false.obs;
//   final RxBool _isUpdatingPosition = false.obs;
//   final RxBool locationDialogShown = false.obs;
  
//   // Getters
//   bool get hasValidPermission => _locationService.hasValidPermission;
//   bool get hasLocationAccess => _locationService.hasLocationAccess;
  
//   // For backward compatibility
//   final _permissionCooldown = const Duration(seconds: 2);
//   bool userCancelled = false;

//   @override
//   void onInit() {
//     super.onInit();
//     _initializeController();
//   }
  
//   Future<void> _initializeController() async {
//     // Sync state with LocationService
//     serviceEnabled.value = _locationService.serviceEnabled.value;
//     permissionStatus.value = _locationService.permissionStatus.value;
    
//     // Set up listeners to keep state in sync
//     ever(_locationService.serviceEnabled, (value) => serviceEnabled.value = value);
//     ever(_locationService.permissionStatus, (value) => permissionStatus.value = value);
    
//     // Update combined state whenever service or permission changes
//     everAll([serviceEnabled, permissionStatus], (_) => _updateCombinedState());
//   }
  
//   void _updateCombinedState() {
//     if (serviceEnabled.value && hasValidPermission) {
//       combinedState.value = CombinedLocationState.enabled;
//     } else if (permissionStatus.value == LocationPermission.denied ||
//                permissionStatus.value == LocationPermission.deniedForever) {
//       combinedState.value = CombinedLocationState.disabled;
//     } else {
//       combinedState.value = CombinedLocationState.checking;
//     }
//   }

//   // Forward methods to LocationService
//   Future<void> startListening() async {
//     try {
//       final position = await getCurrentPosition();
//       if (position != null) {
//         await updatePosition(position);
//       }
//     } catch (e) {
//       _logger.e("Error getting position: $e");
//     }
//   }

//   // Add this getter to expose the current position
//   Position? get currentPosition => position.value;

//   Future<void> updatePosition(Position newPosition) async {
//     if (_isUpdatingPosition.value) return;
//     _isUpdatingPosition.value = true;
    
//     try {
//       // Update the LocationService
//       await _locationService.updateLocation(newPosition);
      
//       // Update our local state
//       position.value = newPosition;
      
//       // Trigger UI updates
//       update();
//     } catch (e) {
//       _logger.e("Error updating position: $e");
//     } finally {
//       _isUpdatingPosition.value = false;
//     }
//   }

//   Future<bool> checkServiceStatus() async {
//     return await _locationService.checkServiceStatus();
//   }

//   Future<LocationPermissionResult> requestLocationPermission() async {
//     isPermissionRequestLocked.value = true;
//     try {
//       final result = await _locationService.requestLocationPermission();
//       return result;
//     } finally {
//       isPermissionRequestLocked.value = false;
//     }
//   }

//   Future<void> checkStatus() async {
//     await _locationService.checkLocationStatus();
//   }

//   Future<LocationPermissionResult> handlePermissionFlow() async {
//     if (isPermissionRequestLocked.value || isPermissionRequestActive.value) {
//       return LocationPermissionResult.pending;
//     }

//     isPermissionRequestLocked.value = true;
//     isPermissionRequestActive.value = true;

//     try {
//       final result = await _locationService.handlePermissionFlow();
//       return result;
//     } finally {
//       isPermissionRequestLocked.value = false;
//       isPermissionRequestActive.value = false;
//     }
//   }
  
//   Future<Position?> getCurrentPosition() async {
//     return await _locationService.getCurrentPosition();
//   }
  
//   Future<void> handleLocationToggle(bool enabled) async {
//     await _locationService.toggleAutoLocation(enabled);
//   }
  
//   Future<void> openSystemLocationSettings() async {
//     await Geolocator.openLocationSettings();
//   }
  
//   Future<void> initializeLocationServices() async {
//     await _locationService.initializeLocationServices();
//   }
  
//   // Simple dialog methods for backward compatibility
//   Future<void> showErrorDialog(String message) async {
//     Get.snackbar(
//       'Error'.tr,
//       message,
//       duration: const Duration(seconds: 3),
//     );
//   }

//   Future<void> showPermanentDenialDialog() async {
//     Get.snackbar(
//       'Location Permission'.tr,
//       'Location permission permanently denied. Please enable in settings.'.tr,
//       duration: const Duration(seconds: 3),
//     );
//   }

//   Future<void> showPermissionDeniedDialog() async {
//     Get.snackbar(
//       'Location Permission'.tr,
//       'Location permission denied.'.tr,
//       duration: const Duration(seconds: 3),
//     );
//   }

//   Future<void> showLocationRequirementDialog() async {
//     if (locationDialogShown.value) return;
    
//     final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
//     if (!isManualLocation && !hasLocationAccess) {
//       locationDialogShown.value = true;
//       try {
//         Get.snackbar(
//           'Location Required'.tr,
//           'Location services are required for accurate prayer times.'.tr,
//           duration: const Duration(seconds: 5),
//         );
//       } finally {
//         locationDialogShown.value = false;
//       }
//     }
//   }
// }

// // Keep this enum for backward compatibility
// enum LocationPermissionResult {
//   granted,
//   denied,
//   permanentlyDenied,
//   serviceDisabled,
//   unknownError,
//   pending,
// }
