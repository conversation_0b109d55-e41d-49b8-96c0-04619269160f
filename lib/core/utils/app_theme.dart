import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'app_consts.dart';

class AppTheme extends GetxController {
  static AppTheme get instance => Get.find();

  final RxInt themeIndex = RxInt(cacheMemory.read('theme') ?? 0);
  late ThemeData theme;

  @override
  void onInit() {
    super.onInit();
    _initializeTheme();
    ever(themeIndex, _updateTheme);
  }

  void _initializeTheme() {
    try {
      final index = themeIndex.value.clamp(0, colors.length - 1);
      theme = ThemeData(
        scaffoldBackgroundColor: colors[index],
        primaryColor: colors[index],
        textTheme: Typography.englishLike2018.apply(fontSizeFactor: 1),
      );
      Get.changeTheme(theme);
    } catch (e) {
      theme = ThemeData.light();
      debugPrint('Theme initialization error: $e');
    }
  }

  void _updateTheme(int index) {
    theme = theme.copyWith(
      scaffoldBackgroundColor: colors[index],
      primaryColor: colors[index],
    );
    Get.changeTheme(theme);
    cacheMemory.write('theme', index);
  }

  Future<void> setTheme(int colorIndex) async {
    themeIndex.value = colorIndex;
  }
}
