// import 'dart:async';
// import 'dart:io';

// import 'package:app_settings/app_settings.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';
// import 'package:logger/logger.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:salawati/core/utils/app_router.dart';
// import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
// import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

// import '../../features/prayer/presentation/controller/prayer_controller.dart';
// import 'app_color.dart';
// import 'app_consts.dart';
// import 'custom_text.dart';

// enum CombinedLocationState { enabled, disabled, checking, denied }

// class LocationController extends GetxController {
//   static LocationController get instance => Get.find();

//   // Add these flags
//   final RxBool _isCheckingPermissions = false.obs;
//   final RxBool isPermissionRequestLocked = false.obs;
//   final Rx<CombinedLocationState> combinedState =
//       CombinedLocationState.disabled.obs;
//   final Rx<Position?> position = Rx<Position?>(null);
//   final _logger = Logger();
//   final RxBool serviceEnabled = false.obs;
//   final Rx<LocationPermission> permissionStatus = LocationPermission.denied.obs;
//   final RxBool isPermissionRequestActive = false.obs;
//   final RxBool _isUpdatingPosition = false.obs;
//   final RxBool locationDialogShown = false.obs;
//   // Add this getter to check valid permissions
//   bool get hasValidPermission {
//     debugPrint('amin hasValidPermission ');
//     return permissionStatus.value == LocationPermission.whileInUse ||
//         permissionStatus.value == LocationPermission.always;
//   }

//   // Add this to track if we have any location access
//   bool get hasLocationAccess =>
//       serviceEnabled.value &&
//       (permissionStatus.value == LocationPermission.whileInUse ||
//           permissionStatus.value == LocationPermission.always);

//   final _permissionCooldown = const Duration(seconds: 2);
//   bool userCancelled = false;

//   @override
//   void onInit() {
//     super.onInit();
//     _initializeLocation();
//     WidgetsBinding.instance.addObserver(
//       LifecycleEventHandler(
//         resumeCallBack: () async {
//           await _handleSettingsReturn();
//         },
//       ),
//     );
//   }

//   StreamSubscription<Position>? _posSub;

//   Stream<Position> getCurrentPositionStream() {
//     LocationSettings locationSettings;

//     if (defaultTargetPlatform == TargetPlatform.android) {
//       locationSettings = AndroidSettings(
//         accuracy: LocationAccuracy.high,
//       );
//     } else if (defaultTargetPlatform == TargetPlatform.iOS ||
//         defaultTargetPlatform == TargetPlatform.macOS) {
//       locationSettings = AppleSettings(
//         accuracy: LocationAccuracy.high,
//         activityType: ActivityType.fitness,
//         distanceFilter: 100,
//         pauseLocationUpdatesAutomatically: true,
//         showBackgroundLocationIndicator: false,
//       );
//     } else {
//       locationSettings = const LocationSettings(
//         accuracy: LocationAccuracy.high,
//         distanceFilter: 100,
//       );
//     }

//     Stream<Position> positionStream =
//         Geolocator.getPositionStream(locationSettings: locationSettings);

//     return positionStream;
//   }

//   void startListening() {
//     _posSub = getCurrentPositionStream().listen((pos) {
//       updatePosition(pos);
//     }, onError: (e) {
//       _logger.e("Error streaming position: $e");
//     });
//   }

//   void stopListening() {
//     _posSub?.cancel();
//   }

//   Future<void> _initializeLocation() async {
//     print('amin _initializeLocation');
//     final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
//     final bool isLocationAuto = cacheMemory.read(IS_LOCATION_AUTO) ?? false;

//     if (isManualLocation || !isLocationAuto) return;

//     await checkStatus();
//     everAll([serviceEnabled, permissionStatus], (_) => _updateState());
//   }

//   // Add this getter to expose the current position
//   Position? get currentPosition => position.value;

//   Future<void> updatePosition(Position newPosition) async {
//     // 1) Don’t proceed if we’re already updating
//     if (_isUpdatingPosition.value) return;
//     _isUpdatingPosition.value = true;

//     try {
//       // Check if position actually changed
//       if (position.value != null &&
//           position.value!.latitude == newPosition.latitude &&
//           position.value!.longitude == newPosition.longitude) {
//         return;
//       }

//       position.value = newPosition;
//       try {
//         await Get.find<PrayerController>().updateLocation(
//           newPosition.latitude,
//           newPosition.longitude,
//         );
//       } catch (e, st) {
//         _logger.w('Prayer-time fetch failed, will retry later $e - $st');
//         // Optionally enqueue a retry or show a toast to the user
//       }

//       // 6) Trigger any UI listeners
//       update();
//     } finally {
//       _isUpdatingPosition.value = false;
//     }
//   }

//   Future<bool> checkServiceStatus() async {
//     serviceEnabled.value = await Geolocator.isLocationServiceEnabled();
//     print('amin checkServiceStatus ${serviceEnabled.value}');

//     return serviceEnabled.value;
//   }

//   Future<LocationPermissionResult> requestLocationPermission() async {
//     if (isPermissionRequestLocked.value) {
//       _logger.w('Permission request already in progress');
//       return LocationPermissionResult.pending;
//     }

//     isPermissionRequestLocked.value = true;
//     try {
//       final status = await Geolocator.requestPermission();
//       return _handlePermissionResult(status);
//     } finally {
//       isPermissionRequestLocked.value = false;
//     }
//   }

//   LocationPermissionResult _handlePermissionResult(LocationPermission status) {
//     switch (status) {
//       case LocationPermission.deniedForever:
//         return LocationPermissionResult.permanentlyDenied;
//       case LocationPermission.denied:
//         return LocationPermissionResult.denied;
//       case LocationPermission.whileInUse:
//       case LocationPermission.always:
//         return LocationPermissionResult.granted;
//       default:
//         return LocationPermissionResult.unknownError;
//     }
//   }

// // Modify your checkStatus method
//   Future<void> checkStatus() async {
//     print('amin checkStatus');
//     if (_isCheckingPermissions.value ||
//         isPermissionRequestActive.value ||
//         isPermissionRequestLocked.value) {
//       return;
//     }

//     await Future.delayed(const Duration(milliseconds: 500));

//     _isCheckingPermissions.value = true;
//     try {
//       serviceEnabled.value = await Geolocator.isLocationServiceEnabled();
//       print('amin start Geolocator.checkPermission');
//       permissionStatus.value = await Geolocator.checkPermission();
//       print('amin start Geolocator.checkPermission ${permissionStatus.value}');

//       // Remove the automatic permission request from here
//     } finally {
//       _isCheckingPermissions.value = false;
//     }
//   }

//   Future<LocationPermissionResult> handlePermissionFlow() async {
//     debugPrint('Entering handlePermissionFlow');
//     if (isPermissionRequestLocked.value || isPermissionRequestActive.value) {
//       return LocationPermissionResult.pending;
//     }

//     isPermissionRequestLocked.value = true;
//     isPermissionRequestActive.value = true;

//     try {
//       final status = await Geolocator.requestPermission();
//       // _logger.d('Received permission status: $status');

//       // Immediately update permission status
//       permissionStatus.value = status;

//       if (status == LocationPermission.denied) {
//         userCancelled = true;
//         Future.delayed(_permissionCooldown, () => userCancelled = false);
//         return LocationPermissionResult.denied;
//       }

//       // If permission granted, verify services immediately
//       // Handle granted permissions
//       if (status == LocationPermission.whileInUse ||
//           status == LocationPermission.always) {
//         final servicesEnabled = await checkServiceStatus();

//         if (!servicesEnabled) {
//           await openSystemLocationSettings();
//           return LocationPermissionResult.serviceDisabled;
//         }

//         return LocationPermissionResult.granted;
//       }

//       return _handlePermissionResult(status);
//     } finally {
//       isPermissionRequestLocked.value = false;
//       isPermissionRequestActive.value = false;
//     }
//   }

//   // Add this method to explicitly start location services
//   Future<void> initializeLocationServices() async {
//     // _logger.d('Starting location services initialization');
//     print('amin initializeLocationServices');
//     await Future.delayed(const Duration(seconds: 1));
//     // _logger.d('After initial delay');

//     final serviceEnabled = await checkServiceStatus();
//     debugPrint('Location service enabled: $serviceEnabled');

//     if (!serviceEnabled) {
//       _logger.w('Showing service disabled dialog');
//       await showServiceDisabledDialog();
//       return;
//     }

//     await checkStatus();
//     // _logger.d('Current permission status: ${permissionStatus.value}');

//     if (!hasValidPermission) {
//       _logger.d('No valid permission, starting permission flow');
//       final result = await handlePermissionFlow();
//       _logger.d('Permission flow result: $result');

//       if (result == LocationPermissionResult.granted) {
//         _logger.d('Permission granted, fetching location');
//         await Get.find<PrayerController>().getCustomLocation();
//       }
//     } else {
//       _logger.d('Already has valid location permissions');
//       await getCurrentPosition();
//     }
//     update();
//   }

//   Future<void> handleLocationToggle(bool enabled) async {
//     if (isPermissionRequestActive.value) return;
//     isPermissionRequestActive.value = true;
//     try {
//       if (!enabled) return position.value = null;

//       // if (!serviceEnabled.value) {
//       //   await showServiceDisabledDialog();
//       //   return;
//       // }

//       if (permissionStatus.value == LocationPermission.denied ||
//           permissionStatus.value == LocationPermission.deniedForever) {
//         await handlePermissionFlow();
//       }

//       if (permissionStatus.value == LocationPermission.whileInUse ||
//           permissionStatus.value == LocationPermission.always) {
//         print('amin position.value ${position.value}');
//         position.value = await getCurrentPosition();
//       }
//     } finally {
//       isPermissionRequestActive.value = false;
//     }
//   }

//   Future<Position> getCurrentPosition() async {
//     print('amin start getCurrentPosition');
//     // 1) Now you can safely try to get the actual location
//     LocationSettings locationSettings;
//     // try {
//     print('amin getCurrentPosition 1');
//     if (defaultTargetPlatform == TargetPlatform.android) {
//       locationSettings = AndroidSettings(
//         accuracy: LocationAccuracy.medium,
//         forceLocationManager: true,
//         distanceFilter: 300,
//         intervalDuration: const Duration(seconds: 10),
//         timeLimit: const Duration(seconds: 60),
//       );
//     } else if (defaultTargetPlatform == TargetPlatform.iOS ||
//         defaultTargetPlatform == TargetPlatform.macOS) {
//       print('amin getCurrentPosition 2');

//       locationSettings = AppleSettings(
//         accuracy: LocationAccuracy.high,
//         activityType: ActivityType.fitness,
//         distanceFilter: 100,
//         pauseLocationUpdatesAutomatically: true,
//         showBackgroundLocationIndicator: false,
//       );
//     } else {
//       print('amin getCurrentPosition 3');

//       locationSettings = const LocationSettings(
//         accuracy: LocationAccuracy.high,
//         distanceFilter: 100,
//       );
//     }
//     var pos =
//         await Geolocator.getCurrentPosition(locationSettings: locationSettings);
//     print('amin pos $pos');
//     return pos;
//     // } catch (e) {
//     //   _logger.e('amin Error getting position: $e');
//     //   // throw LocationFetchException('amin Failed to get current position'.tr);
//     // }
//   }

//   void _updateState() {
//     if (serviceEnabled.value &&
//         (permissionStatus.value == LocationPermission.whileInUse ||
//             permissionStatus.value == LocationPermission.always)) {
//       combinedState.value = CombinedLocationState.enabled;
//     } else if (permissionStatus.value == LocationPermission.denied ||
//         permissionStatus.value == LocationPermission.deniedForever) {
//       combinedState.value = CombinedLocationState.disabled;
//     } else {
//       combinedState.value = CombinedLocationState.checking;
//     }
//     if (Get.isRegistered<PrayerController>()) {
//       try {
//         Get.find<PrayerController>().checkLocationRequirements();
//       } catch (e) {
//         _logger.e('Error updating prayer controller: $e');
//       }
//     }
//     if (Platform.isAndroid) {
//       if (Get.isRegistered<HomeWidgetController>()) {
//         try {
//           Get.find<HomeWidgetController>().onInit();
//         } catch (e) {
//           _logger.e('Error updating HomeWidgetController: $e');
//         }
//       }
//     }
//   }

//   Future<void> openSystemLocationSettings() async {
//     try {
//       if (Get.isDialogOpen!) Get.back();

//       // Declare observer first
//       late final LifecycleEventHandler observer;

//       // Initialize after declaration
//       observer = LifecycleEventHandler(
//         resumeCallBack: () async {
//           await _handleSettingsReturn();
//           WidgetsBinding.instance.removeObserver(observer);
//         },
//       );

//       WidgetsBinding.instance.addObserver(observer);

//       if (Platform.isIOS) {
//         await openAppSettings();
//       } else {
//         await AppSettings.openAppSettings(type: AppSettingsType.location);
//       }
//     } catch (e) {
//       _logger.e('Error opening settings: $e');
//     }
//   }

//   Future<void> _handleSettingsReturn() async {
//     await checkStatus();
//     _updateState();

//     if (combinedState.value == CombinedLocationState.enabled) {
//       // Check for pending auto-location request
//       final bool pendingRequest =
//           cacheMemory.read(PENDING_AUTO_LOCATION) ?? false;

//       if (pendingRequest) {
//         await cacheMemory.write(PENDING_AUTO_LOCATION, false);
//         await cacheMemory.write(IS_LOCATION_AUTO, true);

//         // Update settings controller state
//         Get.find<SettingsController>().isLocationAuto.value = true;

//         try {
//           await Get.find<PrayerController>().getCustomLocation();
//         } catch (e) {
//           _logger.e('Post-enable location fetch error: $e');
//         }
//       }
//     }
//     // } else {
//     //   await showServiceDisabledDialog();
//     // }
//   }

//   Future<bool> showServiceDisabledDialog() async {
//     final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
//     if (!isManualLocation) {
//       var result = await _showDialog<bool>(
//           title: 'location_disabled'.tr,
//           content: 'location_services_required'.tr,
//           actions: [_cancelAction, _settingsAction]);
//       return result ?? false;
//     }
//     return false;
//   }

//   Future<void> showLocationRequirementDialog() async {
//     if (locationDialogShown.value) return;
//     final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;

//     if (!isManualLocation && !hasLocationAccess) {
//       locationDialogShown.value = true;
//       try {
//         await _showDialog(
//           title: 'position_failed'.tr,
//           content: 'location_services_required'.tr,
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Get.back();
//                 Get.toNamed(AppRouter.kSettingsLocationScreen);
//               },
//               child: CustomText('openSettings'.tr),
//             ),
//             _cancelAction,
//           ],
//         );
//       } finally {
//         locationDialogShown.value = false;
//       }
//     }
//   }

//   // Future<void> _showLocationDialog({
//   //   required String titleKey,
//   //   required String contentKey,
//   // }) async {
//   //   if (locationDialogShown.value) return;
//   //   locationDialogShown.value = true;

//   //   await Get.dialog(
//   //     AlertDialog(
//   //       title: Text(titleKey.tr),
//   //       content: Text(contentKey.tr),
//   //       actions: [
//   //         TextButton(
//   //           onPressed: () {
//   //             Get.back();
//   //             AppSettings.openAppSettings();
//   //           },
//   //           child: Text('Enable'.tr),
//   //         ),
//   //         TextButton(
//   //           onPressed: Get.back,
//   //           child: Text('Cancel'.tr),
//   //         ),
//   //       ],
//   //     ),
//   //   );

//   //   locationDialogShown.value = false;
//   // }

//   Future<void> showPermanentDenialDialog() async => _showDialog(
//       title: 'locationServicesDisabled'.tr,
//       content:
//           'Location permissions not granted. Please enable them in app settings.'
//               .tr,
//       actions: [_cancelAction, _settingsAction]);

//   Future<void> showPermissionDeniedDialog() async => _showDialog(
//       title: 'locationPermissionsDenied'.tr,
//       content:
//           'Location permissions not granted. Please enable them in app settings.'
//               .tr,
//       actions: [_cancelAction, _settingsAction]);
//   Future<void> showErrorDialog(String message) async => _showDialog(
//         title: 'Error'.tr,
//         content: message,
//         actions: [
//           TextButton(
//             onPressed: Get.back,
//             child: CustomText('ok'.tr),
//           ),
//         ],
//       );

//   Future<T?> _showDialog<T>({
//     required String title,
//     required String content,
//     required List<Widget> actions,
//   }) async {
//     Completer<T?> completer = Completer();

//     WidgetsBinding.instance.addPostFrameCallback((_) async {
//       final result = await Get.dialog<T>(
//         AlertDialog(
//           backgroundColor: AppColor.kScaffoldColor,
//           title: CustomText(title),
//           content: CustomText(content),
//           actions: actions,
//         ),
//         barrierDismissible: false,
//       );
//       completer.complete(result);
//     });

//     return completer.future;
//   }

//   TextButton get _settingsAction => TextButton(
//         onPressed: openSystemLocationSettings,
//         child: CustomText('allowNow'.tr),
//       );

//   TextButton get _cancelAction => TextButton(
//         onPressed: Get.back,
//         child: CustomText('cancel'.tr),
//       );
// }

// enum LocationPermissionResult {
//   granted,
//   denied,
//   permanentlyDenied,
//   serviceDisabled,
//   unknownError,
//   pending
// }

// // Lifecycle observer class
// class LifecycleEventHandler extends WidgetsBindingObserver {
//   final AsyncCallback? resumeCallBack;

//   LifecycleEventHandler({this.resumeCallBack});

//   @override
//   Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
//     if (state == AppLifecycleState.resumed) {
//       if (resumeCallBack != null) await resumeCallBack!();
//     }
//   }
// }
