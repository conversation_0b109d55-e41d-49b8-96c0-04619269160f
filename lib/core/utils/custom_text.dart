// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'app_color.dart';

class CustomText extends Text {
  CustomText(
    String data, {
    Key? key,
    TextAlign? textAlign,
    TextDirection? textDirection,
    Locale? locale,
    bool? softWrap,
    TextOverflow? overflow,
    double? textScaleFactor,
    int? maxLines,
    double? height,
    String? semanticsLabel,
    TextWidthBasis? textWidthBasis,
    TextStyle? style,
  }) : super(
          data.tr,
          key: key,
          textAlign: textAlign,
          textDirection: textDirection,
          locale: locale,
          softWrap: softWrap,
          overflow: overflow,
          textScaleFactor: textScaleFactor,
          maxLines: maxLines,
          semanticsLabel: semanticsLabel,
          textWidthBasis: textWidthBasis,
          style: style != null
              ? style.copyWith(
                  color: style.color ?? AppColor.kWhiteColor,
                  fontSize: style.fontSize ?? 15,
                  fontFamily: style.fontFamily ?? 'Tajawal',
                  height: height,
                  letterSpacing: 0,
                )
              : TextStyle(
                  color: AppColor.kWhiteColor,
                  // fontSize: 15.sp,
                  fontSize: 15,
                  height: height,
                  letterSpacing: 0,
                  fontFamily: 'Tajawal',
                ),
        );
}
