// import 'package:adhan/adhan.dart';
// import 'package:alarm/alarm.dart';
// import 'package:flutter/foundation.dart';
// import 'package:home_widget/home_widget.dart';
// import 'package:salawati/core/utils/alarm/alarm_permission.dart';
// import 'package:salawati/core/utils/app_consts.dart';
// import 'package:salawati/features/settings/data/models/adhan_model.dart';

// class AppAlarms {
//   static Future<void> cancelAllAlarms() async {
//     await Alarm.stopAll();
//     debugPrint('amin total stop all alarms');
//   }

//   static Future<void> initAlarms() async {
//     AlarmPermissions.checkNotificationPermission();
//     AlarmPermissions.checkAndroidScheduleExactAlarmPermission();
//     Alarm.init();
//     Alarm.checkAlarm();
//   }

//   static List<AlarmSettings> loadAlarms() {
//     final alarms = Alarm.getAlarms();
//     alarms.sort((a, b) => a.dateTime.isBefore(b.dateTime) ? -1 : 1);
//     return alarms;
//   }

//   static Future<void> setPrayerTimeAlarms({
//     required Prayer prayer,
//     DateTime? prayerTime,
//     DateTime? dayDate,
//   }) async {
//     final id = generatePrayerId(prayer: prayer, dayDate: dayDate);
//     var alarm = Alarm.getAlarm(id);
//     if (alarm == null) {}
//   }

//   static int generatePrayerId({required Prayer prayer, DateTime? dayDate}) {
//     dayDate ??= DateTime.now();
//     return int.parse(
//       "${dayDate.year}${dayDate.month.toString().padLeft(2, '0')}${dayDate.day.toString().padLeft(2, '0')}${prayer.index.toString().padLeft(2, '0')}",
//     );
//   }

//   static AdhanModel getAdhanNotification(String title) => AdhanModel.fromMap(
//         title,
//         cacheMemory.read(
//           ADHAN_NOTIFCATIONS(title),
//         ),
//       );
//   static Future<AdhanModel> getAdhanNotificationAsync(String title) async =>
//       AdhanModel.fromMap(
//         title,
//         await HomeWidget.getWidgetData(ADHAN_NOTIFCATIONS(title)),
//       );
//   static void saveAthanNotification(
//     AdhanModel adhanModel,
//   ) {
//     cacheMemory.write(ADHAN_NOTIFCATIONS(adhanModel.title), adhanModel.toMap());
//   }

//   static Future<void> saveAthanNotificationToWidgetData(
//       AdhanModel adhanModel) async {
//     await HomeWidget.saveWidgetData(
//       ADHAN_NOTIFCATIONS(adhanModel.title),
//       adhanModel.toMap(),
//     );
//   }
// }
