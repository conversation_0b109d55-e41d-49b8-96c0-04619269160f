import 'package:get_storage/get_storage.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:salawati/core/utils/app_consts.dart';

enum NumberFormat { western, arabicIndic }

extension NumberFormatExtension on NumberFormat {
  String get name {
    switch (this) {
      case NumberFormat.western:
        return 'Western';
      case NumberFormat.arabicIndic:
        return 'Arabic-Indic';
    }
  }

  String get example {
    switch (this) {
      case NumberFormat.western:
        return '0,1,2,3...';
      case NumberFormat.arabicIndic:
        return '٠,١,٢,٣...';
    }
  }
}

class NumberFormatNotifier extends StateNotifier<NumberFormat> {
  NumberFormatNotifier() : super(NumberFormat.western) {
    _loadPreference();
  }

  final GetStorage _storage = GetStorage();

  void _loadPreference() {
    final savedFormat = _storage.read(NUMBER_FORMAT_PREFERENCE);
    if (savedFormat != null) {
      state = NumberFormat.values.firstWhere(
        (format) => format.toString() == savedFormat,
        orElse: () => NumberFormat.western,
      );
    }
  }

  Future<void> setNumberFormat(NumberFormat format) async {
    state = format;
    await _storage.write(NUMBER_FORMAT_PREFERENCE, format.toString());
  }

  String formatNumber(dynamic number) {
    final numStr = number.toString();

    if (state == NumberFormat.western) {
      return numStr;
    }

    const westernDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    String result = numStr;
    for (int i = 0; i < westernDigits.length; i++) {
      result = result.replaceAll(westernDigits[i], arabicDigits[i]);
    }

    return result;
  }

  String formatTimeString(String timeString) {
    return formatNumber(timeString);
  }
}

final numberFormatProvider =
    StateNotifierProvider<NumberFormatNotifier, NumberFormat>(
  (ref) => NumberFormatNotifier(),
);
