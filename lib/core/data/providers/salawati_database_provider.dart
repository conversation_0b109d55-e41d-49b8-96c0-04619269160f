import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/athkar/data/models/athkar_category_model.dart';
import 'package:salawati/features/athkar/data/models/athkar_model.dart';
import 'package:sqflite/sqflite.dart';

class SalawatiDatabaseProvider {
  static Database? _db;
  static Database get db {
    if (_db == null) {
      throw StateError('Field \'db\' has not been initialized. Call init() first.');
    }
    return _db!;
  }

  static bool get isInitialized => _db != null && _db!.isOpen;

  static const String athkarTableName = 'adhkars';
  static const String athkarCategoryTableName = 'adhkar_categories';
  static const String databaseName = 'salawati.sqlite';

  static Future<void> init() async {
    if (isInitialized) return; // Already initialized
    await ensureDatabaseExists();
    await openDB();
  }

  static Future<void> ensureDatabaseExists() async {
    int version = 1;

    int currentVersion = cacheMemory.read('salawatiDatabaseVersion') ?? -1;
    if (version == currentVersion) {
      if (kDebugMode) {
        debugPrint("Using existing database for salawati");
      }
      return;
    }
    unawaited(cacheMemory.write('salawatiDatabaseVersion', version));
    var path = join(await getDatabasesPath(), databaseName);
    var exists = await databaseExists(path);
    if (exists) {
      await File(path).delete();
      try {
        await Directory(await getDatabasesPath()).create(recursive: true);
      } catch (_) {}
    }
    ByteData data = await rootBundle.load(join('assets/dbs', databaseName));
    List<int> bytes =
        data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);

    await File(path).writeAsBytes(bytes, flush: true);
  }

  static Future<void> openDB() async {
    try {
      _db = await openDatabase(
        join(await getDatabasesPath(), databaseName),
      );
      if (kDebugMode) {
        debugPrint('Salawati database opened successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error opening Salawati database: $e');
      }
      rethrow;
    }
  }

  static Future<void> closeDB() async {
    if (_db != null && _db!.isOpen) {
      await _db!.close();
      _db = null;
    }
  }

  static Future<List<AthkarCategoryModel>> getAllAthkarsData() async {
    // Ensure database is initialized
    if (!isInitialized) {
      if (kDebugMode) {
        debugPrint('Salawati database not initialized, attempting to initialize...');
      }
      try {
        await init();
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Failed to initialize Salawati database: $e');
        }
        return [];
      }
    }

    var stopWatch = Stopwatch()..start();
    List<Map<String, dynamic>> categoriesMaps =
        await db.query(athkarCategoryTableName);
    var categories = List.generate(categoriesMaps.length, (i) {
      return AthkarCategoryModel.fromJson(categoriesMaps[i]);
    });
    stopWatch.stop();
    debugPrint("time to get categories ${stopWatch.elapsedMilliseconds} ms");
    stopWatch.reset();
    stopWatch.start();
    List<Map<String, dynamic>> athkarsMaps = await db.query(athkarTableName);
    var athkars = List.generate(athkarsMaps.length, (i) {
      return AthkarModel.fromJson(athkarsMaps[i]);
    });
    stopWatch.stop();
    debugPrint("time to get athkars ${stopWatch.elapsedMilliseconds} ms");
    stopWatch.reset();
    stopWatch.start();
    for (int i = 0; i < categories.length; i++) {
      categories[i].athkar = athkars
          .where((element) => element.categoryId == categories[i].id)
          .toList();
    }
    stopWatch.stop();
    debugPrint(
        "time to add athkars to categories ${stopWatch.elapsedMilliseconds} ms");
    stopWatch.reset();
    // create categories tree
    var parents =
        categories.where((element) => element.parentId == null).toList();

    for (int i = 0; i < parents.length; i++) {
      var children = categories
          .where((element) => element.parentId == parents[i].id)
          .toList();
      parents[i].subCategories = children;
    }
    stopWatch.stop();
    debugPrint(
        "time to create categories tree ${stopWatch.elapsedMilliseconds} ms");
    stopWatch.reset();
    return parents;
  }

  static Future<List<AthkarModel>> searchAthkar(
    String query,
  ) async {
    // Ensure database is initialized
    if (!isInitialized) {
      if (kDebugMode) {
        debugPrint('Salawati database not initialized, attempting to initialize...');
      }
      try {
        await init();
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Failed to initialize Salawati database: $e');
        }
        return [];
      }
    }

    // Use the existing database connection instead of opening a new one
    List<Map<String, dynamic>> results = await db.query(
      athkarTableName,
      where: 'text LIKE ?',
      whereArgs: ['%$query%'],
    );

    return results.map((item) => AthkarModel.fromJson(item)).toList();
  }
}
