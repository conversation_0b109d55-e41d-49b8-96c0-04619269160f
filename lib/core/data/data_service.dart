import 'package:dio/dio.dart';
import 'package:get/get_connect/http/src/status/http_status.dart';
import 'package:salawati/core/data/data_state.dart';
import 'package:salawati/core/utils/app_functions.dart';

import '../utils/app_consts.dart';

class DataService {
  static late Dio _dio;
  static init() => _dio = Dio();

  static Future<Response> get({
    required String url,
    Map<String, dynamic>? queryParameters,
    data,
  }) async {
    logPrint('get $url param: $queryParameters headers ${options().headers}');
    try {
      Response response = await _dio.get(
        url,
        options: options(),
        queryParameters: queryParameters,
        data: data,
      );
      // logPrint('response: $response');
      return response;
    } catch (e) {
      Response error = AppFunctions.fromError(e);
      logPrint('Error: $e');
      return error;
    }
  }

  static Future<Response> post(
      {required String endPoint, required data}) async {
    FormData jsonData = FormData.fromMap(data);
    logPrint('post body: $data');
    try {
      Response response = await _dio.post(baseUrl + endPoint,
          data: jsonData, options: options());
      // logPrint('response: $response');
      return response;
    } catch (e) {
      Response error = AppFunctions.fromError(e);
      logPrint('Error: $e');
      return error;
    }
  }

  static Future<DataState<T>> dataRepoRequest<T>(
      {required Response response, required Function fromJson}) async {
    try {
      if (response.statusCode == HttpStatus.ok) {
        final object = fromJson(response.data['data'] ??
            response.data['predictions'] ??
            response.data);
        return DataSuccess(object as T);
      }
      return DataFailed(response);
    } catch (e) {
      AppFunctions.showErrorMessage(e.toString());
      return DataFailed(Response(
        statusMessage: e.toString(),
        requestOptions: RequestOptions(),
      ));
    }
  }
}
