import 'package:salawati/core/utils/app_consts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/app_color.dart';

class CustomChildrenGradientButton extends StatelessWidget {
  final List<Widget> children;
  final List<Color>? colors;
  final void Function()? onTap;
  const CustomChildrenGradientButton(
      {super.key, required this.children, this.colors, this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(kRadius / 1.5),
          gradient: LinearGradient(
            colors: colors ??
                [
                  AppColor.kLightBlueColor,
                  AppColor.kLightBlueColor,
                ],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: children,
        ),
      ),
    );
  }
}
