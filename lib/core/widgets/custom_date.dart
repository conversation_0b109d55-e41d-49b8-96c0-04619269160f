import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hijri/hijri_calendar.dart';
import 'package:intl/intl.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';

import '../../features/settings/presentation/controller/settings_controller.dart';

class CustomDate extends GetView<SettingsController> {
  const CustomDate({
    super.key,
    this.isHijry = false,
    this.fontSize,
    this.dateTime,
    this.withDay = true,
    this.color,
  });
  final bool isHijry;
  final double? fontSize;
  final DateTime? dateTime;
  final bool withDay;
  final Color? color;

  // Helper method to localize numbers based on device locale
  String _localizeNumber(String number) {
    final String locale = cacheMemory.read('lang') ?? 'ar';

    // Only convert for Arabic and Urdu locales
    if (locale == 'ar' || locale == 'ur') {
      return _convertToLocaleDigits(number, locale);
    }

    return number;
  }

  // Converts digits to the specified locale format
  String _convertToLocaleDigits(String numStr, String locale) {
    // Arabic/Hindi numerals mapping
    final arabicNumerals = {
      '0': '٠',
      '1': '١',
      '2': '٢',
      '3': '٣',
      '4': '٤',
      '5': '٥',
      '6': '٦',
      '7': '٧',
      '8': '٨',
      '9': '٩'
    };

    // Use the same mapping for both Arabic and Urdu for now
    return numStr
        .split('')
        .map((char) => arabicNumerals[char] ?? char)
        .join('');
  }

  // Localize numbers in a date string while preserving non-numeric characters
  String _localizeNumbersInDate(String dateStr) {
    final String locale = cacheMemory.read('lang') ?? 'ar';

    // Only convert for Arabic and Urdu locales
    if (locale != 'ar' && locale != 'ur') {
      return dateStr;
    }

    // Use regex to find all numbers in the string
    final RegExp numRegex = RegExp(r'\d+');
    return dateStr.replaceAllMapped(numRegex, (match) {
      return _convertToLocaleDigits(match.group(0)!, locale);
    });
  }

  @override
  Widget build(BuildContext context) {
    /// Applies the date correction value from SettingsController
    DateTime applyDateCorrection(DateTime date) {
      final correction = controller.dateCorrectionValue;
      return date.add(Duration(days: correction.value));
    }

    return GetBuilder<SettingsController>(builder: (controller) {
      return isHijry
          ? Builder(builder: (_) {
              String locale = cacheMemory.read('lang') ?? 'ar';
              HijriCalendar.setLocal(locale == 'ar' ? 'ar' : 'en');
              HijriCalendar hijriToday = HijriCalendar.fromDate(
                  applyDateCorrection(dateTime ?? DateTime.now()));
              // Localize the Hijri date numbers
              String localizedDay =
                  AppFunctions.localizeNumber(hijriToday.hDay.toString());
              String localizedYear =
                  AppFunctions.localizeNumber(hijriToday.hYear.toString());

              return CustomText(
                '$localizedDay ${hijriToday.longMonthName}, $localizedYear',
                style: TextStyle(
                  fontSize: fontSize,
                  color: color,
                ),
              );
            })
          : Builder(builder: (context) {
              final today = applyDateCorrection(dateTime ?? DateTime.now());
              String format = withDay ? 'EEEE, d MMMM yyyy' : 'd MMMM yyyy';
              String formattedDate =
                  DateFormat(format, cacheMemory.read('lang') ?? 'ar')
                      .format(today);

              // Localize the numbers in the date string using the new system
              String localizedDate =
                  _localizeNumbersInDateString(formattedDate);

              return CustomText(
                localizedDate,
                style: TextStyle(
                  color: color ??
                      AppColor.kWhiteColor.withAlpha(179), // ~0.7 opacity
                  fontSize: fontSize,
                ),
              );
            });
    });
  }
}

class CustomDateDayOnly extends GetView<SettingsController> {
  const CustomDateDayOnly({
    super.key,
    this.isHijry = false,
    this.fontSize,
    this.dateTime,
    this.withDay = true,
    this.color,
  });
  final bool isHijry;
  final double? fontSize;
  final DateTime? dateTime;
  final bool withDay;
  final Color? color;

  // Helper method to localize numbers based on device locale
  String _localizeNumber(String number) {
    final String locale = cacheMemory.read('lang') ?? 'ar';

    // Only convert for Arabic and Urdu locales
    if (locale == 'ar' || locale == 'ur') {
      return _convertToLocaleDigits(number, locale);
    }

    return number;
  }

  // Converts digits to the specified locale format
  String _convertToLocaleDigits(String numStr, String locale) {
    // Arabic/Hindi numerals mapping
    final arabicNumerals = {
      '0': '٠',
      '1': '١',
      '2': '٢',
      '3': '٣',
      '4': '٤',
      '5': '٥',
      '6': '٦',
      '7': '٧',
      '8': '٨',
      '9': '٩'
    };

    // Use the same mapping for both Arabic and Urdu for now
    return numStr
        .split('')
        .map((char) => arabicNumerals[char] ?? char)
        .join('');
  }

  // Localize numbers in a date string while preserving non-numeric characters
  String _localizeNumbersInDate(String dateStr) {
    final String locale = cacheMemory.read('lang') ?? 'ar';

    // Only convert for Arabic and Urdu locales
    if (locale != 'ar' && locale != 'ur') {
      return dateStr;
    }

    // Use regex to find all numbers in the string
    final RegExp numRegex = RegExp(r'\d+');
    return dateStr.replaceAllMapped(numRegex, (match) {
      return _convertToLocaleDigits(match.group(0)!, locale);
    });
  }

  @override
  Widget build(BuildContext context) {
    /// Applies the date correction value from SettingsController
    DateTime applyDateCorrection(DateTime date) {
      final correction = controller.dateCorrectionValue;
      return date.add(Duration(days: correction.value));
    }

    return GetBuilder<SettingsController>(builder: (controller) {
      return Builder(builder: (context) {
        final today = applyDateCorrection(dateTime ?? DateTime.now());
        String format = 'EEEE';
        String formattedDate =
            DateFormat(format, cacheMemory.read('lang') ?? 'ar').format(today);

        // Localize the day name if it contains any numbers
        String localizedDate = _localizeNumbersInDate(formattedDate);

        return CustomText(
          localizedDate,
          style: TextStyle(
            color: color ?? AppColor.kWhiteColor.withAlpha(179), // ~0.7 opacity
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
          ),
        );
      });
    });
  }
}
