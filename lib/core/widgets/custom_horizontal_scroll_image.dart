import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/widgets/shimmer.dart';

class CustomHorizontalScrollImage extends StatelessWidget {
  const CustomHorizontalScrollImage(
      {super.key, required this.imageUrl, this.height});

  final String imageUrl;
  final double? height;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
          height: height?.h,
          width: context.width,
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            placeholder: (context, url) => SizedBox(
              height: 200.h,
              child: const CustomShimmer(),
            ),
            fit: BoxFit.fill,
          )),
    );
  }
}
