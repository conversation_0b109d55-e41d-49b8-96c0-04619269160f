import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

class CustomLottieApi extends StatelessWidget {
  const CustomLottieApi({super.key, this.height, required this.lottieApi});
  final double? height;
  final LottieApi lottieApi;
  @override
  Widget build(BuildContext context) {
    return Center(
      child: LottieBuilder.asset("assets/json/${lottieApiParser(lottieApi)}.json",
          height: height == null ? 60.h : height!.h),
    );
  }
}

enum LottieApi { error, loading, empty }
 lottieApiParser(LottieApi lottieApi) => lottieApi.toString().split('.')[1];