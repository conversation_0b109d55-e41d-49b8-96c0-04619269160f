import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart' as shimmer;

class CustomShimmer extends StatelessWidget {
  final double? radius;

  const CustomShimmer({super.key, this.radius});

  @override
  Widget build(BuildContext context) {
    return shimmer.Shimmer.fromColors(
      baseColor: Colors.grey.withOpacity(0.2),
      highlightColor: Colors.grey[300]!.withOpacity(0.5),
      direction: shimmer.ShimmerDirection.ltr,
      child: Container(
          decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(radius ?? 20))),
    );
  }
}
