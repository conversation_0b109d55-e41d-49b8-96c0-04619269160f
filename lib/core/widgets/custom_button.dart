import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';

class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.onTap,
    required this.label,
    this.icon,
    this.color,
    this.fontSize,
    this.iconSize,
    this.width,
    this.height,
  });
  final void Function()? onTap;
  final String label;
  final IconData? icon;
  final Color? color;
  final double? iconSize;
  final double? fontSize;
  final double? height;
  final double? width;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: height ?? 55.h,
        width: width,
        decoration: BoxDecoration(
          color: color ?? Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(kRadius),
        ),
        clipBehavior: Clip.antiAlias,
        child: Row(
          children: [
            const Spacer(),
            if (icon != null)
              Icon(
                icon,
                color: Theme.of(context).canvasColor,
                size: iconSize,
              ),
            if (icon != null) 4.horizontalSpace,
            FittedBox(
              child: CustomText(
                label,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: fontSize ?? 20.sp,
                ),
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
