import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';

// ignore: must_be_immutable
class CustomTextField extends StatefulWidget {
  CustomTextField({
    this.onChanged,
    this.controller,
    this.autofillHints,
    this.autoFocus = false,
    this.obscureText = false,
    this.textInputAction,
    this.focusNode,
    this.prefixIconData,
    this.suffixIcon,
    this.hintText,
    this.labelText,
    this.errorText,
    this.helperText,
    this.showLabelAboveTextField = false,
    this.floatingLabelBehavior = FloatingLabelBehavior.auto,
    this.fillColor,
    this.accentColor,
    this.textColor,
    this.borderRadius = 6,
    this.validator,
    this.showError = true,
    this.verticalPadding = 18,
    this.horizontalPadding = 12,
    super.key,
    this.keyboardType,
    this.onFieldSubmitted,
  });

  final Function(String)? onChanged;
  final TextEditingController? controller;
  final Iterable<String>? autofillHints;
  final bool autoFocus;
  final bool obscureText;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final Widget? prefixIconData;
  final Widget? suffixIcon;

  final String? hintText;
  final String? labelText;
  final String? errorText;
  final TextInputType? keyboardType;

  /// Text placed below the text field
  final String? helperText;
  final bool showLabelAboveTextField;
  final FloatingLabelBehavior floatingLabelBehavior;
  final Color? fillColor;
  final Color? accentColor;
  final Color? textColor;
  final double borderRadius;
  String? Function(String?)? validator;
  final bool showError;
  final double verticalPadding;
  final double horizontalPadding;
  final ValueChanged<String>? onFieldSubmitted;

  @override
  State<CustomTextField> createState() => _LoginScreentate();
}

class _LoginScreentate extends State<CustomTextField> {
  late FocusNode focusNode;
  late bool hasFocus;

  @override
  void initState() {
    super.initState();
    hasFocus = false;

    focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    TextStyle? buildLabelStyle() {
      if (hasFocus) {
        return TextStyle(color: AppColor.kLightBlueColor, fontSize: 12.sp);
      } else {
        return TextStyle(color: Colors.white, fontSize: 12.sp);
      }
    }

    return TextSelectionTheme(
      data: TextSelectionThemeData(
        selectionColor:
            widget.accentColor?.withOpacity(.33) ?? AppColor.kLightBlueColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.labelText != null && widget.showLabelAboveTextField) ...[
            CustomText(widget.labelText!,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: hasFocus ? AppColor.kLightBlueColor : Colors.white,
                )),
            const SizedBox(height: 6),
          ],
          TextFormField(
            validator: widget.validator ??
                (val) {
                  if (val == null || val.isEmpty) {
                    return 'This Field is Required'.tr;
                  }
                  return null;
                },
            controller: widget.controller,
            autofillHints: widget.autofillHints,
            keyboardType: widget.keyboardType,
            autofocus: widget.autoFocus,
            onChanged: widget.onChanged,
            onFieldSubmitted: widget.onFieldSubmitted,
            style: TextStyle(
              color: widget.textColor ?? AppColor.kWhiteColor,
              fontSize: 15.sp,
              fontFamily: 'Tajawal',
            ),
            cursorColor: widget.textColor ?? AppColor.kWhiteColor,
            obscureText: widget.obscureText,
            textInputAction: widget.textInputAction,
            decoration: InputDecoration(
              errorStyle: TextStyle(fontSize: 10.sp, color: AppColor.kRedColor),
              contentPadding: EdgeInsets.symmetric(
                  vertical: widget.verticalPadding,
                  horizontal: widget.horizontalPadding),
              isDense: true,
              hintText: widget.hintText?.tr,
              hintStyle: TextStyle(
                fontFamily: 'Tajawal',
                color:
                    (widget.textColor ?? AppColor.kWhiteColor).withOpacity(.45),
              ),
              labelText:
                  widget.showLabelAboveTextField ? null : widget.labelText,
              labelStyle: buildLabelStyle(),
              floatingLabelBehavior: widget.floatingLabelBehavior,
              fillColor: widget.fillColor,
              filled: widget.fillColor != null,
              // focusedBorder: buildFocusedBorder(),
              // enabledBorder: buildEnabledBorder(),
              border: InputBorder.none,
              prefixIcon: widget.prefixIconData != null
                  ? Padding(
                      padding:
                          const EdgeInsetsDirectional.only(start: 12, end: 12),
                      child: widget.prefixIconData,
                    )
                  : null,
              prefixIconConstraints:
                  const BoxConstraints(minHeight: 24, minWidth: 24),
              suffixIcon: Padding(
                  padding: const EdgeInsetsDirectional.only(start: 12, end: 12),
                  child: widget.suffixIcon),
              suffixIconConstraints:
                  const BoxConstraints(minHeight: 24, minWidth: 24),
            ),
          ),
          if (widget.helperText != null) ...[
            SizedBox(height: 6.w),
            CustomText(
              widget.helperText!,
              style: TextStyle(
                color: Colors.grey[600],
              ),
            )
          ]
        ],
      ),
    );
  }
}
