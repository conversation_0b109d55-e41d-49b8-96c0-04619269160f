import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';

class CustomAnimatedDropdown<T> extends StatelessWidget {
  const CustomAnimatedDropdown({
    super.key,
    this.items,
    this.hintText,
    this.onChanged,
    this.hintBuilder,
    this.listItemBuilder,
    this.closedFillColor,
    this.expandedFillColor,
    this.headerBuilder,
    this.expandedHeaderPadding,
    this.initialItem,
    this.newTextStyle,
  });
  final EdgeInsets? expandedHeaderPadding;
  final Color? closedFillColor;
  final Color? expandedFillColor;
  final List<T>? items;
  final String? hintText;
  final T? initialItem;
  final dynamic Function(T?)? onChanged;
  final Widget Function(BuildContext, String, bool)? hintBuilder;
  final Widget Function(BuildContext context, T item, bool isSelected,
      VoidCallback onItemSelect)? listItemBuilder;
  final Widget Function(BuildContext, T, bool)? headerBuilder;
  final TextStyle? newTextStyle;
  @override
  Widget build(BuildContext context) {
    TextStyle textStyle = newTextStyle ??
        TextStyle(
          color: AppColor.kWhiteColor.withOpacity(0.45),
          fontSize: 15.sp,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        );
    return CustomDropdown<T>(
      initialItem: initialItem,
      expandedHeaderPadding: expandedHeaderPadding,
      hintText: hintText,
      items: items,
      onChanged: onChanged,
      excludeSelected: false,
      hintBuilder: hintBuilder,
      listItemBuilder: listItemBuilder,
      headerBuilder: headerBuilder,
      decoration: CustomDropdownDecoration(
        closedFillColor: closedFillColor ?? AppColor.kRectangleColor,
        expandedFillColor: expandedFillColor ?? AppColor.kWhiteColor,
        hintStyle: textStyle,
        listItemStyle: textStyle,
        headerStyle:
            textStyle.copyWith(color: AppColor.kOrangeColor.withOpacity(0.45)),
        listItemDecoration: ListItemDecoration(
          selectedColor: AppColor.kGreyColor.withOpacity(0.5),
        ),
        closedSuffixIcon: Transform.rotate(
            angle: -3.14 / 2, child: SvgPicture.asset(AppSvgs.kArrow)),
        expandedSuffixIcon: Transform.rotate(
            angle: 3.14 / 2, child: SvgPicture.asset(AppSvgs.kArrow)),
        expandedShadow: [
          BoxShadow(
            blurRadius: 15,
            spreadRadius: 15,
            color: AppColor.kGreyColor.withOpacity(0.05),
          ),
        ],
      ),
    );
  }
}
