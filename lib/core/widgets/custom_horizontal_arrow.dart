// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:salawati/core/utils/app_assets.dart';

class CustomHorizontalArrow extends StatelessWidget {
  const CustomHorizontalArrow({
    super.key,
    this.color,
    this.isBack = false,
  });
  final bool isBack;
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return Transform.flip(
        flipX: isBack
            ? Directionality.of(context) != TextDirection.ltr
            : Directionality.of(context) == TextDirection.ltr,
        child: SvgPicture.asset(
          AppSvgs.kArrow,
          // ignore: deprecated_member_use
          color: color,
        ));
  }
}
