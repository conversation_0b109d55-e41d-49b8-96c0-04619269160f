import 'package:flutter/material.dart';

class SmoothEdgesContainer extends StatelessWidget {
  const SmoothEdgesContainer({
    super.key,
    required this.borderRadius,
    this.child,
    this.width,
    this.height,
    this.decoration,
    this.alignment,
    this.padding,
    this.margin,
    this.color,
  });
  final BorderRadiusGeometry borderRadius;
  final Widget? child;
  final double? width;
  final double? height;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: ShapeBorderClipper(
        shape: ContinuousRectangleBorder(
          borderRadius: borderRadius,
        ),
      ),
      child: Container(
        margin: margin,
        padding: padding,
        alignment: alignment,
        width: width,
        height: height,
        color: color,
        decoration: decoration,
        child: child,
      ),
    );
  }
}
