// // background_tasks.dart
// import 'dart:async';
// import 'dart:io';

// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:home_widget/home_widget.dart';
// import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
// import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
// import 'package:workmanager/workmanager.dart';
// import 'package:adhan/adhan.dart';

// class BackgroundTasks {
//   static const String prayerUpdateTask = 'prayer_times_update';
//   static const String widgetUpdateTask = 'widget_update';
//   static const String notificationTask = 'athan_notifications';

//   static void initialize() {
//     Workmanager().initialize(
//       callbackDispatcher,
//       isInDebugMode: kDebugMode,
//     );

//     _registerTasks();
//   }

//   static void _registerTasks() {
//     Workmanager().registerPeriodicTask(
//       prayerUpdateTask,
//       prayerUpdateTask,
//       frequency: const Duration(hours: 1),
//       constraints: Constraints(
//         networkType: NetworkType.not_required,
//         requiresBatteryNotLow: true,
//         requiresStorageNotLow: true,
//       ),
//     );

//     Workmanager().registerPeriodicTask(
//       widgetUpdateTask,
//       widgetUpdateTask,
//       frequency: const Duration(minutes: 15),
//       existingWorkPolicy: ExistingWorkPolicy.replace,
//     );
//   }
// }

// @pragma('vm:entry-point')
// void callbackDispatcher() {
//   Workmanager().executeTask((taskName, inputData) async {
//     final logger = BackgroundLogger();
//     // final stopwatch = Stopwatch()..start();

//     try {
//       logger.log('🚀 Starting background task: $taskName');
//       // await _initializeBackgroundServices();
//       await _handlePrayerTimesUpdate(logger);
//       return true;
//       // switch (taskName) {
//       //   case BackgroundTasks.prayerUpdateTask:
//       //     return await _handlePrayerTimesUpdate(logger);
//       //   // case BackgroundTasks.widgetUpdateTask:
//       //   //   return await _handleWidgetUpdate(logger);

//       //   default:
//       //     logger.log('⚠️ Unknown task: $taskName');
//       //     return false;
//       // }
//     } catch (e, stack) {
//       logger.error('❌ Task failed: ${e.toString()}', stack);
//       return false;
//     }
//     // } finally {
//     //   logger.log('✅ Task completed in ${stopwatch.elapsedMilliseconds}ms');
//     //   await _cleanupResources();
//     //   stopwatch.stop();
//     // }
//   });
// }

// // Future<void> _initializeBackgroundServices() async {
// //   await GetStorage.init();
// //   await HomeWidget.setAppGroupId('group.your.app.prayertimes');
// // }

// // Future<void> _cleanupResources() async {
// //   Get.reset();
// // }

// Future<bool> _handlePrayerTimesUpdate(BackgroundLogger logger) async {
//   try {
//     await PrayerTimesHomeWidget.updatePrayerTimeWidget(
//         Get.find<PrayerController>());
//     return true;
//   } catch (e, stack) {
//     logger.error('Prayer times update failed', stack);
//     return false;
//   }
// }

// class BackgroundLogger {
//   void log(String message) {
//     if (kDebugMode) {
//       debugPrint('🔵 [Background] $message');
//     }
//   }

//   void error(String message, StackTrace stack) {
//     if (kDebugMode) {
//       debugPrint('''
// 🔴 [Background Error] $message
// 📜 Stack Trace:
// $stack
//       ''');
//     }
//   }
// }
