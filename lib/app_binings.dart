import 'package:get/get.dart';
import 'package:salawati/core/utils/app_locale.dart';
import 'package:salawati/core/utils/app_theme.dart';
import 'package:salawati/features/athkar/presentation/controller/athkar_controller.dart';
import 'package:salawati/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
import 'package:salawati/features/ibadah/presentation/cubit/ibadah_cubit.dart';
import 'package:salawati/features/layout/presentation/controller/layout_controller.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/mosque/presentation/cubit/mosque_cubit.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/prayer/presentation/widgets/new_widget.dart';
import 'package:salawati/features/quran/presentation/cubit/quran_cubit.dart';
import 'package:salawati/features/settings/data/repo/settings_repo.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/cubit/settings_cubit.dart';
import 'package:salawati/features/settings/presentation/screens/notification/settings_notification_screen.dart';
import 'package:salawati/features/splash/presentation/controller/splash_controller.dart';

class AppBindings extends Bindings {
  @override
  void dependencies() {
    // Core system services (permanent)
    _initCoreServices();

    // Application controllers
    _initControllers();

    // Feature cubits
    _initCubits();

    // Repository layer
    _initRepositories();
  }

  void _initCoreServices() {
    Get.put<AppTheme>(AppTheme(), permanent: true);
    Get.put<AppLocale>(AppLocale(), permanent: true);
  }

  void _initControllers() async {
    Get.put<LocationService>(LocationService(), permanent: true);
    Get.put<PrayerController>(PrayerController(), permanent: true);
    Get.put<HomeWidgetController>(HomeWidgetController(), permanent: true);

    Get.put<SettingsController>(
        SettingsController(prayerController: Get.find()),
        permanent: true);
    Get.put<PrayerDataService>(PrayerDataService(), permanent: true);
    // Get.put<PrayerWidgetController>(PrayerWidgetController(), permanent: true);

    // Finally register LayoutController
    Get.lazyPut<LayoutController>(
      () => LayoutController(),
      fenix: true,
    );

    // Splash screen
    Get.lazyPut<SplashController>(
      () => SplashController(),
      fenix: true,
    );

    // Athkar feature
    Get.lazyPut<AthkarController>(
      () => AthkarController(),
      fenix: true,
    );

    // Notification permissions
    Get.lazyPut<NotificationPermissionController>(
      () => NotificationPermissionController(),
      fenix: true,
    );
  }

  void _initCubits() {
    // Authentication
    Get.lazyPut<AuthCubit>(
      () => AuthCubit(),
      fenix: true,
    );

    // Quran feature
    Get.lazyPut<QuranCubit>(
      () => QuranCubit(),
      fenix: true,
    );

    // Ibadah feature
    Get.lazyPut<IbadahCubit>(
      () => IbadahCubit(),
      fenix: true,
    );

    // Mosque feature
    Get.lazyPut<MosqueCubit>(
      () => MosqueCubit(),
      fenix: true,
    );
  }

  void _initRepositories() {
    // Settings repository
    Get.lazyPut<SettingsSearchRepo>(
      () => SettingsSearchRepo(),
      fenix: true,
    );

    // Settings search controller (depends on repo)
    Get.lazyPut<SettingsSearchController>(
      () => SettingsSearchController(),
      fenix: true,
    );
  }
}
