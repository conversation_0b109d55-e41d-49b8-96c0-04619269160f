import Flutter
import GoogleMaps
import SwiftyJSON
import UIKit
import UserNotifications
//import alarm
import home_widget
import workmanager

//import flutter_background_service_ios

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {

    GeneratedPluginRegistrant.register(with: self)
    UNUserNotificationCenter.current().delegate = self

    WorkmanagerPlugin.setPluginRegistrantCallback { registry in

      GeneratedPluginRegistrant.register(with: registry)
    }

    //    SwiftAlarmPlugin.registerBackgroundTasks()

    //    SwiftFlutterBackgroundServicePlugin.taskIdentifier = "dev.flutter.background.refresh"

    // In AppDelegate.application method
    // WorkmanagerPlugin.registerBGProcessingTask(withIdentifier: "salawati.refreshWidget")
    WorkmanagerPlugin.registerBGProcessingTask(withIdentifier: "salawati.refreshWidget2")

    // Register a periodic task in iOS 13+
    // WorkmanagerPlugin.registerPeriodicTask(
    //   withIdentifier: "salawati.iOSBackgroundAppRefresh", frequency: NSNumber(value: 20 * 60))
    WorkmanagerPlugin.registerPeriodicTask(
      withIdentifier: "salawati.iOSBackgroundAppRefresh2", frequency: NSNumber(value: 20 * 60))

    // Additional configurations for workmanager and home widgets
    if #available(iOS 17, *) {
      HomeWidgetBackgroundWorker.setPluginRegistrantCallback { registry in
        GeneratedPluginRegistrant.register(with: registry)
      }
    }

    GMSServices.provideAPIKey("AIzaSyDeKM4NfLsu5s1k7OiKqYe5zufVkC149RU")

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  override func userNotificationCenter(
    _ center: UNUserNotificationCenter, willPresent notification: UNNotification,
    withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
  ) {
    completionHandler([.banner, .sound])
  }
}
