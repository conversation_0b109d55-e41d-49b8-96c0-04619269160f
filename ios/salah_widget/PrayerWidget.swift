import SwiftUI
import WidgetKit
import os

#if canImport(AppIntents)
    import AppIntents
#endif

// MARK: - Logger Extension
extension Logger {
    static let widgetLogger = Logger(subsystem: "com.salawati.prayertime", category: "Widget")
}

// MARK: - iOS 17 Interactive Intent
@available(iOS 17, *)
struct RefreshWidgetIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource = "Refresh Widget"
    static var description: IntentDescription? = "Refreshes the widget content."

    func perform() async throws -> some IntentResult {
        Logger.widgetLogger.log("🟢 RefreshWidgetIntent.perform() called. Reloading all timelines.")
        WidgetCenter.shared.reloadAllTimelines()
        return .result()
    }
}

// MARK: - Environment & Localization

struct UserLanguageKey: EnvironmentKey {
    static let defaultValue: String? = nil
}

extension EnvironmentValues {
    var userLanguage: String? {
        get { self[UserLanguageKey.self] }
        set { self[UserLanguageKey.self] = newValue }
    }
}

struct LocalizedStrings {
    enum Language: String {
        case english = "en"
        case arabic = "ar"
    }

    static var current: Language {
        if let code = Locale.current.languageCode, code == "ar" { return .arabic }
        return .english
    }

    static let strings: [String: [Language: String]] = [
        "Next Prayer": [.english: "Next Prayer", .arabic: "الصلاة القادمة"],
        "No Prayer Times Available": [
            .english: "No Prayer Times Available", .arabic: "لا يوجد مواعيد صلاة متاحة",
        ],
        "No Prayer": [.english: "No Prayer", .arabic: "لا صلاة"],
        "Language:": [.english: "Language:", .arabic: "اللغة:"],
        "Location:": [.english: "Location:", .arabic: "الموقع:"],
        "Updated:": [.english: "Updated:", .arabic: "آخر فتح للتطبيق:"],
        "Prayer Times": [.english: "Prayer Times", .arabic: "مواعيد الصلاة"],
        "All Prayers": [.english: "All Prayers", .arabic: "جميع الصلوات"],
        "Prayer Times - Full": [.english: "Prayer Times - Full", .arabic: "مواعيد الصلاة - كاملة"],
        "Extra Medium Prayer Widget": [
            .english: "Extra Medium Prayer Widget", .arabic: "ودجت الصلاة متوسطة الإضافية",
        ],
        "Next Prayer Description": [
            .english: "Shows only the next prayer with a progress circle and remaining time.",
            .arabic: "يعرض الصلاة القادمة فقط مع دائرة التقدم والوقت المتبقي.",
        ],
        "All Prayers Description": [
            .english: "Displays all prayer times.",
            .arabic: "يعرض جميع أوقات الصلاة.",
        ],
        "Prayer Times Description": [
            .english: "Displays upcoming prayer times and a countdown.",
            .arabic: "يعرض أوقات الصلاة القادمة والعد التنازلي.",
        ],
        "Prayer Times - Full Description": [
            .english: "Displays the current day, upcoming prayer times and a countdown.",
            .arabic: "يعرض اليوم الحالي، أوقات الصلاة القادمة والعد التنازلي.",
        ],
        "Extra Medium Prayer Widget Description": [
            .english:
                "Displays next prayer details and a progress indicator between current and next prayer.",
            .arabic: "يعرض تفاصيل الصلاة القادمة ومؤشر التقدم بين الصلاة الحالية والقادمة.",
        ],
        "Prayer Times List": [
            .english: "Prayer Times List", .arabic: "قائمة أوقات الصلاة",
        ],
        "A list of all prayer times with full dates.": [
            .english: "A list of all prayer times with full dates.",
            .arabic: "يعرض قائمة بجميع أوقات الصلاة مع التواريخ الكاملة.",
        ],
    ]

    static func string(for key: String, language: String? = nil) -> String {
        let lang: Language = {
            if let language = language, let value = Language(rawValue: language) { return value }
            return current
        }()
        return strings[key]?[lang] ?? key
    }

    static func prayerName(_ label: String, language: String?) -> String {
        guard let lang = language, lang.lowercased() == "ar" else { return label }
        let translations: [String: String] = [
            "Fajr": "الفجر",
            "Dhuhr": "الظهر",
            "Asr": "العصر",
            "Maghrib": "المغرب",
            "Isha": "العشاء",
            "Middle Of The Night": "منتصف الليل",
            "Last Third Of The Night": "الثلث الأخير",
        ]
        return translations[label] ?? label
    }
}

struct LocalizedText: View {
    let key: String
    @Environment(\.userLanguage) private var language
    var body: some View {
        Text(LocalizedStrings.string(for: key, language: language))
            .multilineTextAlignment(language == "ar" ? .trailing : .leading)
            .onAppear {
                Logger.widgetLogger.log("LocalizedText onAppear: \(key)")
            }
    }
}

struct PrayerText: View {
    let label: String
    @Environment(\.userLanguage) private var language
    var body: some View {
        Text(LocalizedStrings.prayerName(label, language: language))
            .multilineTextAlignment(language == "ar" ? .trailing : .leading)
            .onAppear {
                Logger.widgetLogger.log("PrayerText onAppear: \(label)")
            }
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        Logger.widgetLogger.log("Color init(hex:) called with \(hex)")
        var hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        if hex.lowercased().hasPrefix("0x") {
            hex = String(hex.dropFirst(2))
        }
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a: UInt64
        let r: UInt64
        let g: UInt64
        let b: UInt64
        switch hex.count {
        case 3:
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6:
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8:
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 255, 255, 255)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255)
    }
}

// MARK: - Widget Constants

struct WidgetConstants {
    static var currentLanguage: String = "ar"
    static let backgroundGradient = Color(hex: "0xff262a47")

    static let timeFormatter: DateFormatter = {
        Logger.widgetLogger.log("WidgetConstants.timeFormatter created.")
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        if currentLanguage.lowercased() == "ar" {
            formatter.locale = Locale(identifier: "ar")
            formatter.amSymbol = "ص"
            formatter.pmSymbol = "م"
        }
        return formatter
    }()

    static let timeFullFormatter: DateFormatter = {
        Logger.widgetLogger.log("WidgetConstants.timeFullFormatter created.")
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        if currentLanguage.lowercased() == "ar" {
            formatter.locale = Locale(identifier: "ar")
            formatter.amSymbol = "ص"
            formatter.pmSymbol = "م"
        }
        return formatter
    }()

    static func dayFormatter(forLanguage language: String?) -> DateFormatter {
        Logger.widgetLogger.log("WidgetConstants.dayFormatter(forLanguage:) called.")
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        if language == "ar" { formatter.locale = Locale(identifier: "ar") }
        return formatter
    }

    static func fullDateFormatter(forLanguage language: String?) -> DateFormatter {
        Logger.widgetLogger.log("WidgetConstants.fullDateFormatter(forLanguage:) called.")
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM d, yyyy"
        if language == "ar" { formatter.locale = Locale(identifier: "ar") }
        return formatter
    }

    static let componentsFormatter: DateComponentsFormatter = {
        Logger.widgetLogger.log("WidgetConstants.componentsFormatter created.")
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        formatter.zeroFormattingBehavior = .pad
        return formatter
    }()
}

extension View {
    func widgetBackground() -> some View {
        Logger.widgetLogger.log("View.widgetBackground() called.")
        return self.background(WidgetConstants.backgroundGradient)
    }
}

// MARK: - Data Models

struct PrayerTime: TimelineEntry, Codable, Identifiable {
    let id: UUID
    let date: Date
    let label: String
    let time: Date
    let iconName: String
    let timezoneOffset: Int

    init(date: Date, label: String, time: Date, iconName: String, timezoneOffset: Int) {
        Logger.widgetLogger.log("Creating PrayerTime for label \(label) at \(time)")
        self.id = UUID()
        self.date = date
        self.label = label
        self.time = time
        self.iconName = iconName
        self.timezoneOffset = timezoneOffset
    }
}

struct PrayerPayload: Codable {
    let userLanguage: String
    let userLocation: String
    let updatedAt: Date
    let prayers: [PrayerTime]
}

// Our main timeline entry model.
struct PrayerTimelineEntry: TimelineEntry {
    let date: Date
    let prayers: [PrayerTime]
    let currentPrayer: PrayerTime?
    let nextPrayer: PrayerTime?
    let progress: Double
    let remainingTime: TimeInterval
    let userLanguage: String?
    let userLocation: String?
    let updatedAt: Date?

    static func placeholder() -> PrayerTimelineEntry {
        Logger.widgetLogger.log("Creating placeholder PrayerTimelineEntry.")
        return PrayerTimelineEntry(
            date: Date(),
            prayers: [],
            currentPrayer: nil,
            nextPrayer: nil,
            progress: 0,
            remainingTime: 0,
            userLanguage: "en",
            userLocation: "",
            updatedAt: nil
        )
    }
    var layoutDirection: LayoutDirection {
        userLanguage == "ar" ? .rightToLeft : .leftToRight
    }
    var currentDayPrayers: [PrayerTime] {
        let calendar = Calendar.current
        let day = date
        return prayers.filter { calendar.isDate($0.date, inSameDayAs: day) }
    }
}

// MARK: - Prayer Calculator (Shared)

struct PrayerCalculator {
    private let prayers: [PrayerTime]
    private let currentDate: Date

    init(prayers: [PrayerTime], currentDate: Date = Date()) {
        Logger.widgetLogger.log("Initializing PrayerCalculator with \(prayers.count) prayers.")
        self.prayers = prayers.sorted { $0.time < $1.time }
        self.currentDate = currentDate
    }

    var currentPrayer: PrayerTime? {
        prayers.last { $0.time <= currentDate }
    }

    var nextPrayer: PrayerTime? {
        guard let current = currentPrayer,
            let index = prayers.firstIndex(where: { $0.id == current.id })
        else {
            return prayers.first
        }
        let nextIndex = prayers.index(after: index)
        if nextIndex < prayers.count {
            return prayers[nextIndex]
        } else {
            return prayers.first { $0.time > current.time }
        }
    }

    var progress: Double {
        guard let current = currentPrayer, let next = nextPrayer else { return 0 }
        let total = next.time.timeIntervalSince(current.time)
        let elapsed = currentDate.timeIntervalSince(current.time)
        return min(max(elapsed / total, 0), 1)
    }

    var remainingTime: TimeInterval {
        nextPrayer.map { max(0, $0.time.timeIntervalSince(currentDate)) } ?? 0
    }
}

// MARK: - Fallback Provider (iOS 16) - TimelineProvider

struct PrayerProviderFallback: TimelineProvider {
    func placeholder(in context: Context) -> PrayerTimelineEntry {
        Logger.widgetLogger.log("🔵 Fallback placeholder(in:) called.")
        return PrayerTimelineEntry.placeholder()
    }

    func getSnapshot(in context: Context, completion: @escaping (PrayerTimelineEntry) -> Void) {
        Logger.widgetLogger.log("🔵 Fallback getSnapshot(in:) called.")
        let entry = loadCurrentEntry()
        completion(entry)
    }

    func getTimeline(
        in context: Context, completion: @escaping (Timeline<PrayerTimelineEntry>) -> Void
    ) {
        Logger.widgetLogger.log("🔵 Fallback getTimeline(in:) called.")
        let entry = loadCurrentEntry()
        let timeline = Timeline(
            entries: [entry], policy: .after(Date().addingTimeInterval(15 * 60)))
        completion(timeline)
    }

    private func loadCurrentEntry() -> PrayerTimelineEntry {
        let payload = loadPrayerData()
        let now = Date()
        let calculator = PrayerCalculator(prayers: payload.prayers, currentDate: now)
        return PrayerTimelineEntry(
            date: now,
            prayers: payload.prayers,
            currentPrayer: calculator.currentPrayer,
            nextPrayer: calculator.nextPrayer,
            progress: calculator.progress,
            remainingTime: calculator.remainingTime,
            userLanguage: payload.userLanguage,
            userLocation: payload.userLocation,
            updatedAt: payload.updatedAt
        )
    }

    private func loadPrayerData() -> PrayerPayload {
        Logger.widgetLogger.log("🟠 Fallback loadPrayerData() called.")
        guard let userDefaults = UserDefaults(suiteName: "group.salawati_prayer_time"),
            let jsonString = userDefaults.string(forKey: "prayerDataNew"),
            let data = jsonString.data(using: .utf8)
        else {
            Logger.widgetLogger.log("🟠 No fallback data found. Returning empty payload.")
            return PrayerPayload(
                userLanguage: "en", userLocation: "", updatedAt: Date(), prayers: [])
        }
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .millisecondsSince1970
            let payload = try decoder.decode(PrayerPayload.self, from: data)
            Logger.widgetLogger.log("🟠 Fallback loaded \(payload.prayers.count) prayers.")
            return PrayerPayload(
                userLanguage: payload.userLanguage,
                userLocation: payload.userLocation,
                updatedAt: payload.updatedAt,
                prayers: payload.prayers.sorted { $0.time < $1.time }
            )
        } catch {
            Logger.widgetLogger.log("🟠 Fallback decode error: \(error.localizedDescription)")
            return PrayerPayload(
                userLanguage: "en", userLocation: "", updatedAt: Date(), prayers: [])
        }
    }
}

// MARK: - iOS 17 Provider - AppIntentTimelineProvider

#if canImport(AppIntents)
    @available(iOS 17, *)
    struct PrayerAppIntentProvider: AppIntentTimelineProvider {
        typealias Intent = RefreshWidgetIntent
        typealias Entry = PrayerTimelineEntry

        func placeholder(in context: Context) -> PrayerTimelineEntry {
            Logger.widgetLogger.log("🔴 AppIntent placeholder(in:) called.")
            return PrayerTimelineEntry.placeholder()
        }

        func snapshot(for configuration: RefreshWidgetIntent, in context: Context) async
            -> PrayerTimelineEntry
        {
            Logger.widgetLogger.log("🔴 AppIntent snapshot(for:) called.")
            return await loadCurrentEntry()
        }

        func timeline(for configuration: RefreshWidgetIntent, in context: Context) async
            -> Timeline<PrayerTimelineEntry>
        {
            Logger.widgetLogger.log("🔴 AppIntent timeline(for:) called.")
            let entry = await loadCurrentEntry()
            let timeline = Timeline(
                entries: [entry], policy: .after(Date().addingTimeInterval(15 * 60)))
            return timeline
        }

        private func loadCurrentEntry() async -> PrayerTimelineEntry {
            let payload = loadPrayerData()
            let now = Date()
            let calculator = PrayerCalculator(prayers: payload.prayers, currentDate: now)
            return PrayerTimelineEntry(
                date: now,
                prayers: payload.prayers,
                currentPrayer: calculator.currentPrayer,
                nextPrayer: calculator.nextPrayer,
                progress: calculator.progress,
                remainingTime: calculator.remainingTime,
                userLanguage: payload.userLanguage,
                userLocation: payload.userLocation,
                updatedAt: payload.updatedAt
            )
        }

        private func loadPrayerData() -> PrayerPayload {
            Logger.widgetLogger.log("🟠 AppIntent loadPrayerData() called.")
            guard let userDefaults = UserDefaults(suiteName: "group.salawati_prayer_time"),
                let jsonString = userDefaults.string(forKey: "prayerDataNew"),
                let data = jsonString.data(using: .utf8)
            else {
                Logger.widgetLogger.log("🟠 AppIntent: No data found. Returning empty payload.")
                return PrayerPayload(
                    userLanguage: "en", userLocation: "", updatedAt: Date(), prayers: [])
            }
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .millisecondsSince1970
                let payload = try decoder.decode(PrayerPayload.self, from: data)
                Logger.widgetLogger.log("🟠 AppIntent loaded \(payload.prayers.count) prayers.")
                return PrayerPayload(
                    userLanguage: payload.userLanguage,
                    userLocation: payload.userLocation,
                    updatedAt: payload.updatedAt,
                    prayers: payload.prayers.sorted { $0.time < $1.time }
                )
            } catch {
                Logger.widgetLogger.log("🟠 AppIntent decode error: \(error.localizedDescription)")
                return PrayerPayload(
                    userLanguage: "en", userLocation: "", updatedAt: Date(), prayers: [])
            }
        }
    }
#endif

struct NextPrayerSmallWidgetFallbackView: View {
    var entry: PrayerTimelineEntry

    var body: some View {
        let now = Date()
        ZStack {
            Color.clear.widgetBackground().ignoresSafeArea()
            if entry.prayers.isEmpty {
                VStack {
                    Text("Loading...")
                        .foregroundColor(.white)
                    Text("Open the app to refresh")
                        .foregroundColor(.white.opacity(0.7))
                }
            } else {

                ZStack {
                    Color.clear.widgetBackground().ignoresSafeArea()
                    VStack(alignment: .center, spacing: 4) {
                        if let nextPrayer = entry.nextPrayer {
                            ProgressCircle(progress: entry.progress, icon: nextPrayer.iconName)
                            Text(
                                LocalizedStrings.prayerName(
                                    nextPrayer.label, language: entry.userLanguage)
                            )
                            .font(.headline)
                            .foregroundColor(.white)
                            .lineLimit(1)
                            Text(WidgetConstants.timeFormatter.string(from: nextPrayer.time))
                                .font(.subheadline)
                                .foregroundColor(.white)
                            Text(timerInterval: now...nextPrayer.time)
                                .environment(
                                    \.locale,
                                    Locale(
                                        identifier: (entry.userLanguage ?? "en").lowercased()
                                            == "ar"
                                            ? "ar" : "en")
                                )
                                .font(.subheadline)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .accessibilityLabel("Time remaining until next prayer")
                        } else {
                            LocalizedText(key: "No Prayer")
                                .foregroundColor(.white)
                        }
                    }
                    .padding()
                    .accessibilityElement(children: .combine)
                }
                .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
            }
        }
        .onAppear {
            Logger.widgetLogger.log("NextPrayerSmallWidgetFallbackView onAppear.")
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
    }
}

struct MediumPrayerWidgetFallbackView: View {
    var entry: PrayerTimelineEntry

    var body: some View {
        ZStack {
            Color.clear.widgetBackground().ignoresSafeArea()
            if entry.prayers.isEmpty {
                VStack {
                    Text("Loading...")
                        .foregroundColor(.white)
                    Text("Open the app to refresh")
                        .foregroundColor(.white.opacity(0.7))
                }
            } else {

                ZStack {
                    Color.clear.widgetBackground()
                    VStack {

                        PrayerProgressHeader(
                            currentPrayer: entry.currentPrayer,
                            nextPrayer: entry.nextPrayer,
                            progress: entry.progress,
                            remainingTime: entry.remainingTime,
                            language: entry.userLanguage
                        )
                        .accessibilityElement(children: .combine)
                        Spacer()
                        PrayerTimelineRow(
                            entry: entry, language: entry.userLanguage
                        )
                        .frame(height: 60)
                        Spacer()

                    }
                    .padding()
                }
                .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)

            }
        }
        .onAppear {
            Logger.widgetLogger.log("MediumPrayerWidgetFallbackView onAppear.")
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
    }
}

#if canImport(AppIntents)
    @available(iOS 17, *)
    struct NextPrayerSmallWidgetInteractiveView: View {
        var entry: PrayerTimelineEntry

        var body: some View {
            let now = Date()

            ZStack {
                Color.clear.widgetBackground().ignoresSafeArea()
                if entry.prayers.isEmpty {
                    VStack {
                        Text("Loading...")
                            .foregroundColor(.white)
                        Button(intent: RefreshWidgetIntent()) {
                            Text("Refresh")
                        }
                        .buttonStyle(.borderedProminent)
                    }
                } else {

                    ZStack {
                        Color.clear.widgetBackground().ignoresSafeArea()
                        VStack(alignment: .center, spacing: 4) {
                            if let nextPrayer = entry.nextPrayer {
                                ProgressCircle(progress: entry.progress, icon: nextPrayer.iconName)
                                Text(
                                    LocalizedStrings.prayerName(
                                        nextPrayer.label, language: entry.userLanguage)
                                )
                                .font(.headline)
                                .foregroundColor(.white)
                                .lineLimit(1)
                                Text(WidgetConstants.timeFormatter.string(from: nextPrayer.time))
                                    .font(.subheadline)
                                    .foregroundColor(.white)
                                Text(timerInterval: now...nextPrayer.time)
                                    .environment(
                                        \.locale,
                                        Locale(
                                            identifier: (entry.userLanguage ?? "en").lowercased()
                                                == "ar"
                                                ? "ar" : "en")
                                    )
                                    .font(.subheadline)
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.center)
                                    .accessibilityLabel("Time remaining until next prayer")
                            } else {
                                LocalizedText(key: "No Prayer")
                                    .foregroundColor(.white)
                            }
                        }
                        .padding()
                        .accessibilityElement(children: .combine)
                    }
                    .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
                    .environment(\.userLanguage, entry.userLanguage)
                    .environment(\.layoutDirection, entry.layoutDirection)

                }
            }
            .onAppear {
                Logger.widgetLogger.log("NextPrayerSmallWidgetInteractiveView onAppear.")
            }
            .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        }
    }

    @available(iOS 17, *)
    struct MediumPrayerWidgetInteractiveView: View {
        var entry: PrayerTimelineEntry

        var body: some View {
            ZStack {
                Color.clear.widgetBackground().ignoresSafeArea()
                if entry.prayers.isEmpty {
                    VStack {
                        Text("Loading...")
                            .foregroundColor(.white)
                        Button(intent: RefreshWidgetIntent()) {
                            Text("Refresh")
                        }
                        .buttonStyle(.borderedProminent)
                    }
                } else {
                    ZStack {
                        Color.clear.widgetBackground()
                        VStack {

                            PrayerProgressHeader(
                                currentPrayer: entry.currentPrayer,
                                nextPrayer: entry.nextPrayer,
                                progress: entry.progress,
                                remainingTime: entry.remainingTime,
                                language: entry.userLanguage
                            )
                            .accessibilityElement(children: .combine)
                            Spacer()
                            PrayerTimelineRow(
                                entry: entry, language: entry.userLanguage
                            )
                            .frame(height: 60)
                            Spacer()

                        }
                        .padding()
                    }
                    .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
                    .environment(\.userLanguage, entry.userLanguage)
                    .environment(\.layoutDirection, entry.layoutDirection)
                }
            }
            .onAppear {
                Logger.widgetLogger.log("MediumPrayerWidgetInteractiveView onAppear.")
            }
            .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        }
    }
#endif

// MARK: - Widget Configurations

// Fallback Widgets for iOS 16
struct NextPrayerSmallWidgetFallback: Widget {
    let kind: String = "NextPrayerSmallWidgetFallback"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProviderFallback()) { entry in
            NextPrayerSmallWidgetFallbackView(entry: entry)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Next Prayer"))
        .description(LocalizedStrings.string(for: "Next Prayer Description"))
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}

struct MediumPrayerWidgetFallback: Widget {
    let kind: String = "MediumPrayerWidgetFallback"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProviderFallback()) { entry in
            MediumPrayerWidgetFallbackView(entry: entry)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times"))
        .description(LocalizedStrings.string(for: "Prayer Times Description"))
        .supportedFamilies([.systemMedium])
        .contentMarginsDisabled()
    }
}

#if canImport(AppIntents)
    @available(iOS 17, *)
    struct NextPrayerSmallWidgetInteractive: Widget {
        let kind: String = "NextPrayerSmallWidgetInteractive"
        var body: some WidgetConfiguration {
            AppIntentConfiguration(
                kind: kind,
                intent: RefreshWidgetIntent.self,
                provider: PrayerAppIntentProvider()
            ) { entry in
                NextPrayerSmallWidgetInteractiveView(entry: entry)
            }
            .configurationDisplayName(LocalizedStrings.string(for: "Next Prayer"))
            .description(LocalizedStrings.string(for: "Next Prayer Description"))
            .supportedFamilies([.systemSmall])
            .contentMarginsDisabled()
        }
    }

    @available(iOS 17, *)
    struct MediumPrayerWidgetInteractive: Widget {
        let kind: String = "MediumPrayerWidgetInteractive"
        var body: some WidgetConfiguration {
            AppIntentConfiguration(
                kind: kind,
                intent: RefreshWidgetIntent.self,
                provider: PrayerAppIntentProvider()
            ) { entry in
                MediumPrayerWidgetInteractiveView(entry: entry)
            }
            .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times"))
            .description(LocalizedStrings.string(for: "Prayer Times Description"))
            .supportedFamilies([.systemMedium])
            .contentMarginsDisabled()
        }
    }
#endif

// MARK: - Widget Bundle

@main
struct SalahWidgetBundle: WidgetBundle {
    @WidgetBundleBuilder
    var body: some Widget {
        // #if canImport(AppIntents)
        //     if #available(iOSApplicationExtension 17, *) {
        //         NextPrayerSmallWidgetInteractive()  // iOS 17+ via @available
        //         MediumPrayerWidgetInteractive()

        //     }

        //     if #available(iOSApplicationExtension 17, *) {
        //     }
        // #endif
        NextPrayerSmallWidgetFallback()  // Fallback for older iOS
        MediumPrayerWidgetFallback()
    }
}

struct ProgressCircle: View {
    let progress: Double
    let icon: String
    @Environment(\.layoutDirection) var layoutDirection
    var body: some View {
        ZStack {
            Circle().stroke(Color.white.opacity(0.3), lineWidth: 4)
            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(Color.white, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                .rotationEffect(layoutDirection == .rightToLeft ? .degrees(90) : .degrees(-90))
                .animation(.linear(duration: 1), value: progress)
            Image(systemName: icon)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
        }
        .frame(width: 35, height: 35)
        .accessibilityElement(children: .ignore)
    }
}

struct PrayerTimeTile: View {
    let prayer: PrayerTime
    let language: String?
    var body: some View {
        VStack {
            ZStack {
                Circle().fill(Color.white.opacity(0.2))
                Image(systemName: prayer.iconName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            .frame(width: 30, height: 30)
            Text(WidgetConstants.timeFormatter.string(from: prayer.time))
                .font(.caption2.weight(.bold))
                .foregroundColor(.white)
            Text(LocalizedStrings.prayerName(prayer.label, language: language))
                .font(.caption2.weight(.bold))
                .foregroundColor(.white.opacity(0.9))
                .lineLimit(2)
                .minimumScaleFactor(0.9)
        }
        .accessibilityElement(children: .combine)
    }
}

struct PrayerProgressHeader: View {
    let currentPrayer: PrayerTime?
    let nextPrayer: PrayerTime?
    let progress: Double
    let remainingTime: TimeInterval
    let language: String?

    var body: some View {
        GeometryReader { geometry in
            let availableWidth = geometry.size.width
            let now: Date = Date()
            HStack(spacing: availableWidth * 0.03) {
                ProgressCircle(progress: progress, icon: currentPrayer?.iconName ?? "clock")
                VStack(alignment: .leading, spacing: 2) {
                    LocalizedText(key: "Next Prayer")
                        .font(availableWidth < 150 ? .caption : .subheadline)
                        .foregroundColor(.white.opacity(0.8))
                        .minimumScaleFactor(0.7)
                    Text(LocalizedStrings.prayerName(nextPrayer?.label ?? "--", language: language))
                        .font(.subheadline.weight(.semibold))
                        .foregroundColor(.white)
                        .lineLimit(1)
                        .minimumScaleFactor(0.6)
                }
                Spacer()
                VStack(alignment: .trailing, spacing: 2) {
                    if let nextTime = nextPrayer?.time {
                        Text(timerInterval: now...nextTime)
                            .environment(
                                \.locale,
                                Locale(
                                    identifier: (language ?? "en").lowercased() == "ar"
                                        ? "ar" : "en")
                            )
                            .font(.subheadline.bold())
                            .foregroundColor(.white)
                            .multilineTextAlignment(.trailing)
                        Text(WidgetConstants.timeFormatter.string(from: nextTime))
                            .font(.subheadline.weight(.bold))
                            .foregroundColor(.white)
                            .lineLimit(1)
                    }
                }
            }
            .padding(.horizontal, availableWidth * 0.05)
        }
    }
}

struct PrayerTimelineRow: View {
    let entry: PrayerTimelineEntry
    let language: String?

    var body: some View {
        // Corrected: Filter out the NEXT prayer instead of current
        let filteredPrayers = entry.currentDayPrayers.filter { $0.id != entry.nextPrayer?.id }

        HStack(spacing: 12) {
            ForEach(filteredPrayers) { prayer in
                PrayerTimeTile(prayer: prayer, language: language)

            }
        }
        .padding(.horizontal, 3)
    }
}
