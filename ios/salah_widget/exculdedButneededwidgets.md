
struct LargePrayerWidget: Widget {
    let kind: String = "LargePrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            SalahWidgetEntryView(entry: entry, isHidden: false)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times - Full"))
        .description(LocalizedStrings.string(for: "Prayer Times - Full Description"))
        .supportedFamilies([.systemLarge])
        .contentMarginsDisabled()
    }
}



struct LargeScrollablePrayerWidget: Widget {
    let kind: String = "LargeScrollablePrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            LargeScrollablePrayerWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times List"))
        .description(LocalizedStrings.string(for: "A list of all prayer times with full dates."))
        .supportedFamilies([.systemLarge])
        .contentMarginsDisabled()
    }
}


struct ExtraMediumPrayerWidgetView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        let now = Date()
        ZStack {
            Color.clear.widgetBackground().ignoresSafeArea()
            VStack(spacing: 20) {
                VStack(spacing: 8) {
                    HStack {
                        ProgressCircle(
                            progress: entry.progress, icon: entry.nextPrayer?.iconName ?? "clock"
                        )
                        .accessibilityElement(children: .ignore)
                        .accessibilityLabel("Next Prayer Progress")
                        VStack(alignment: .leading) {
                            LocalizedText(key: "Next Prayer")
                                .font(.subheadline.bold())
                                .foregroundColor(.white.opacity(0.8))
                            Text(
                                LocalizedStrings.prayerName(
                                    entry.nextPrayer?.label ?? "--", language: entry.userLanguage)
                            )
                            .font(.title2.bold())
                            .foregroundColor(.white)
                            .lineLimit(1)
                        }
                        Spacer()
                        VStack(alignment: .trailing) {
                            if let nextTime = entry.nextPrayer?.time {
                                Text(timerInterval: now...nextTime)
                                    .font(.subheadline.bold())
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.trailing)
                                Text(WidgetConstants.timeFormatter.string(from: nextTime))
                                    .font(.subheadline.bold())
                                    .foregroundColor(.white)
                            }
                        }
                    }
                    Spacer()
                    HStack {
                        if let currentPrayer = entry.currentPrayer {
                            VStack {
                                Image(systemName: currentPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        currentPrayer.label, language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                        GeometryReader { geo in
                            ZStack(alignment: .leading) {
                                Capsule().fill(Color.white.opacity(0.3))
                                Capsule()
                                    .fill(Color.white)
                                    .frame(width: geo.size.width * CGFloat(entry.progress))
                            }
                        }
                        .frame(height: 8)
                        if let nextPrayer = entry.nextPrayer {
                            VStack {
                                Image(systemName: nextPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        nextPrayer.label, language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding()
            }
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
    }
}



struct ExtraMediumPrayerWidget: Widget {
    let kind: String = "ExtraMediumPrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            ExtraMediumPrayerWidgetView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Extra Medium Prayer Widget"))
        .description(LocalizedStrings.string(for: "Extra Medium Prayer Widget Description"))
        .supportedFamilies([.systemMedium])
        .contentMarginsDisabled()
    }
}




struct LargeScrollablePrayerWidgetEntryView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        let now: Date = Date()
        VStack(alignment: .center, spacing: 12) {
            if let firstPrayer = entry.prayers.first {
                Text(
                    WidgetConstants.fullDateFormatter(forLanguage: entry.userLanguage).string(
                        from: firstPrayer.date)
                )
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal)
                .multilineTextAlignment(.center)
            }
            ForEach(entry.prayers.prefix(5)) { prayer in
                HStack(alignment: .top, spacing: 4) {
                    ZStack {
                        Circle().stroke(Color.white.opacity(0.3), lineWidth: 4)
                        Circle()
                            .stroke(Color.white, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                            .rotationEffect(.degrees(90))
                        Image(systemName: prayer.iconName)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    Spacer()
                    Text(LocalizedStrings.prayerName(prayer.label, language: entry.userLanguage))
                        .font(.headline)
                        .foregroundColor(.white)
                    Spacer()
                    Text(WidgetConstants.timeFullFormatter.string(from: prayer.time))
                        .font(.subheadline)
                        .foregroundColor(.white)
                    Spacer()
                }
                .padding(.vertical, 4)
                Divider().background(Color.white.opacity(0.3))
            }
        }
        .padding()
        .widgetBackground()
        .ignoresSafeArea()
    }
}





extension Color {
    init(hex: String) {
        var hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        if hex.lowercased().hasPrefix("0x") {
            hex = String(hex.dropFirst(2))
        }
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a: UInt64
        let r: UInt64
        let g: UInt64
        let b: UInt64
        switch hex.count {
        case 3:  // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6:  // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8:  // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 255, 255, 255)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255)
    }
}




struct AllPrayersSmallWidgetEntryView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        ZStack {
            Color.clear.widgetBackground()
            VStack(alignment: .leading, spacing: 8) {
                let currentDayPrayers = entry.currentDayPrayers
                if currentDayPrayers.isEmpty {
                    Text(LocalizedStrings.string(for: "No Prayer Times Available", language: entry.userLanguage))
                        .foregroundColor(.white)
                } else {
                    ForEach(currentDayPrayers) { prayer in
                        HStack {
                            Image(systemName: prayer.time <= Date() ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(.white)
                            PrayerText(label: prayer.label)
                                .font(.caption)
                                .foregroundColor(.white)
                            Spacer()
                            Text(WidgetConstants.timeFormatter.string(from: prayer.time))
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                        .padding(4)
                        .background(
                            prayer.id == entry.currentPrayer?.id ?
                                Color(hex: "0xffFF8E5E") : Color.clear
                        )
                        .accessibilityElement(children: .combine)
                    }
                }
            }
            .padding()
        }
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
    }
}




struct ExtraMediumPrayerWidgetView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        let now = Date()
        ZStack {
            Color.clear.widgetBackground().ignoresSafeArea()
            VStack(spacing: 20) {
                VStack(spacing: 8) {
                    HStack {
                        ProgressCircle(
                            progress: entry.progress, icon: entry.nextPrayer?.iconName ?? "clock"
                        )
                        .accessibilityElement(children: .ignore)
                        .accessibilityLabel("Next Prayer Progress")
                        VStack(alignment: .leading) {
                            LocalizedText(key: "Next Prayer")
                                .font(.subheadline.bold())
                                .foregroundColor(.white.opacity(0.8))
                            Text(
                                LocalizedStrings.prayerName(
                                    entry.nextPrayer?.label ?? "--", language: entry.userLanguage)
                            )
                            .font(.title2.bold())
                            .foregroundColor(.white)
                            .lineLimit(1)
                        }
                        Spacer()
                        VStack(alignment: .trailing) {
                            if let nextTime = entry.nextPrayer?.time {
                                Text(timerInterval: now...nextTime)
                                    .environment(
                                        \.locale,
                                        Locale(
                                            identifier: (entry.userLanguage ?? "en").lowercased()
                                                == "ar" ? "ar" : "en")
                                    )
                                    .font(.subheadline.bold())
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.trailing)
                                Text(WidgetConstants.timeFormatter.string(from: nextTime))
                                    .font(.subheadline.bold())
                                    .foregroundColor(.white)
                            }
                        }
                    }
                    Spacer()
                    HStack {
                        if let currentPrayer = entry.currentPrayer {
                            VStack {
                                Image(systemName: currentPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        currentPrayer.label, language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                        GeometryReader { geo in
                            ZStack(alignment: .leading) {
                                Capsule().fill(Color.white.opacity(0.3))
                                Capsule()
                                    .fill(Color.white)
                                    .frame(width: geo.size.width * CGFloat(entry.progress))
                            }
                        }
                        .frame(height: 8)
                        if let nextPrayer = entry.nextPrayer {
                            VStack {
                                Image(systemName: nextPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        nextPrayer.label, language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding()
            }
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
    }
}

struct AllPrayersSmallWidget: Widget {
    let kind: String = "AllPrayersSmallWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            AllPrayersSmallWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "All Prayers"))
        .description(LocalizedStrings.string(for: "All Prayers Description"))
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}


struct AllPrayersSmallWidgetEntryView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        ZStack {
            Color.clear.widgetBackground()
            VStack(alignment: .leading, spacing: 8) {
                let currentDayPrayers = entry.currentDayPrayers
                if currentDayPrayers.isEmpty {
                    Text(
                        LocalizedStrings.string(
                            for: "No Prayer Times Available", language: entry.userLanguage)
                    )
                    .foregroundColor(.white)
                } else {
                    ForEach(currentDayPrayers) { prayer in
                        HStack {
                            Image(
                                systemName: prayer.time <= Date()
                                    ? "checkmark.circle.fill" : "circle"
                            )
                            .foregroundColor(.white)
                            PrayerText(label: prayer.label)
                                .font(.caption)
                                .foregroundColor(.white)
                            Spacer()
                            Text(WidgetConstants.timeFormatter.string(from: prayer.time))
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                        .padding(4)
                        .background(
                            prayer.id == entry.currentPrayer?.id
                                ? Color(hex: "0xffFF8E5E") : Color.clear
                        )
                        .accessibilityElement(children: .combine)
                    }
                }
            }
            .padding()
        }
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
    }
}



struct LargePrayerWidget: Widget {
    let kind: String = "LargePrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            SalahWidgetEntryView(entry: entry, isHidden: false)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times - Full"))
        .description(LocalizedStrings.string(for: "Prayer Times - Full Description"))
        .supportedFamilies([.systemLarge])
        .contentMarginsDisabled()
    }
}

struct ExtraMediumPrayerWidget: Widget {
    let kind: String = "ExtraMediumPrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            ExtraMediumPrayerWidgetView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Extra Medium Prayer Widget"))
        .description(LocalizedStrings.string(for: "Extra Medium Prayer Widget Description"))
        .supportedFamilies([.systemMedium])
        .contentMarginsDisabled()
    }
}

struct LargeScrollablePrayerWidget: Widget {
    let kind: String = "LargeScrollablePrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            LargeScrollablePrayerWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times List"))
        .description(LocalizedStrings.string(for: "A list of all prayer times with full dates."))
        .supportedFamilies([.systemLarge])
        .contentMarginsDisabled()
    }
}




struct AllPrayersSmallWidget: Widget {
    let kind: String = "AllPrayersSmallWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            AllPrayersSmallWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "All Prayers"))
        .description(LocalizedStrings.string(for: "All Prayers Description"))
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}



struct ExtraMediumPrayerWidgetView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        let now = Date()
        ZStack {
            Color.clear.widgetBackground().ignoresSafeArea()
            VStack(spacing: 20) {
                VStack(spacing: 8) {
                    HStack {
                        ProgressCircle(
                            progress: entry.progress, icon: entry.nextPrayer?.iconName ?? "clock"
                        )
                        .accessibilityElement(children: .ignore)
                        .accessibilityLabel("Next Prayer Progress")
                        VStack(alignment: .leading) {
                            LocalizedText(key: "Next Prayer")
                                .font(.subheadline.bold())
                                .foregroundColor(.white.opacity(0.8))
                            Text(
                                LocalizedStrings.prayerName(
                                    entry.nextPrayer?.label ?? "--", language: entry.userLanguage)
                            )
                            .font(.title2.bold())
                            .foregroundColor(.white)
                            .lineLimit(1)
                        }
                        Spacer()
                        VStack(alignment: .trailing) {
                            if let nextTime = entry.nextPrayer?.time {
                                Text(timerInterval: now...nextTime)
                                    .environment(
                                        \.locale,
                                        Locale(
                                            identifier: (entry.userLanguage ?? "en").lowercased()
                                                == "ar" ? "ar" : "en")
                                    )
                                    .font(.subheadline.bold())
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.trailing)
                                Text(WidgetConstants.timeFormatter.string(from: nextTime))
                                    .font(.subheadline.bold())
                                    .foregroundColor(.white)
                            }
                        }
                    }
                    Spacer()
                    HStack {
                        if let currentPrayer = entry.currentPrayer {
                            VStack {
                                Image(systemName: currentPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        currentPrayer.label, language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                        GeometryReader { geo in
                            ZStack(alignment: .leading) {
                                Capsule().fill(Color.white.opacity(0.3))
                                Capsule()
                                    .fill(Color.white)
                                    .frame(width: geo.size.width * CGFloat(entry.progress))
                            }
                        }
                        .frame(height: 8)
                        if let nextPrayer = entry.nextPrayer {
                            VStack {
                                Image(systemName: nextPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        nextPrayer.label, language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding()
            }
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
    }
}



struct MetadataView: View {
    let language: String?
    let location: String?
    let updatedAt: Date?

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                if let location = location {
                    HStack {
                        LocalizedText(key: "Location:")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                        Text(location)
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                Spacer()
                if let updatedAt = updatedAt {
                    HStack {
                        LocalizedText(key: "Updated:")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                        Text(WidgetConstants.timeFormatter.string(from: updatedAt))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .frame(height: 60)
    }
}


struct DateHeaderView: View {
    let prayerDate: Date?
    @Environment(\.userLanguage) private var language
    var body: some View {
        if let date = prayerDate {
            HStack {
                Text(WidgetConstants.dayFormatter(forLanguage: language).string(from: date))
                    .font(.headline)
                    .foregroundColor(.white)
                Text(WidgetConstants.fullDateFormatter(forLanguage: language).string(from: date))
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 16)
        }
    }
}