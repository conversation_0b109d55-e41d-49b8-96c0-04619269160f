import SwiftUI
import WidgetKit

// MARK: - Environment & Localization

struct UserLanguageKey: EnvironmentKey {
    static let defaultValue: String? = nil
}
extension EnvironmentValues {
    var userLanguage: String? {
        get { self[UserLanguageKey.self] }
        set { self[UserLanguageKey.self] = newValue }
    }
}
struct LocalizedStrings {
    enum Language: String {
        case english = "en"
        case arabic = "ar"
    }

    static var current: Language {
        if let code = Locale.current.languageCode, code == "ar" { return .arabic }
        return .english
    }

    static let strings: [String: [Language: String]] = [
        "Next Prayer": [.english: "Next Prayer", .arabic: "الصلاة القادمة"],
        "No Prayer Times Available": [
            .english: "No Prayer Times Available", .arabic: "لا يوجد مواعيد صلاة متاحة",
        ],
        "No Prayer": [.english: "No Prayer", .arabic: "لا صلاة"],
        "Language:": [.english: "Language:", .arabic: "اللغة:"],
        "Location:": [.english: "Location:", .arabic: "الموقع:"],
        "Updated:": [.english: "Updated:", .arabic: "آخر فتح للتطبيق:"],
        "Prayer Times": [.english: "Prayer Times", .arabic: "مواعيد الصلاة"],
        "All Prayers": [.english: "All Prayers", .arabic: "جميع الصلوات"],
        "Prayer Times - Full": [.english: "Prayer Times - Full", .arabic: "مواعيد الصلاة - كاملة"],
        "Extra Medium Prayer Widget": [
            .english: "Extra Medium Prayer Widget", .arabic: "ودجت الصلاة متوسطة الإضافية",
        ],
        "Next Prayer Description": [
            .english: "Shows only the next prayer with a progress circle and remaining time.",
            .arabic: "يعرض الصلاة القادمة فقط مع دائرة التقدم والوقت المتبقي.",
        ],
        "All Prayers Description": [
            .english: "Displays all prayer times.",
            .arabic: "يعرض جميع أوقات الصلاة.",
        ],
        "Prayer Times Description": [
            .english: "Displays upcoming prayer times and a countdown.",
            .arabic: "يعرض أوقات الصلاة القادمة والعد التنازلي.",
        ],
        "Prayer Times - Full Description": [
            .english: "Displays the current day, upcoming prayer times and a countdown.",
            .arabic: "يعرض اليوم الحالي، أوقات الصلاة القادمة والعد التنازلي.",
        ],
        "Extra Medium Prayer Widget Description": [
            .english:
                "Displays next prayer details and a progress indicator between current and next prayer.",
            .arabic: "يعرض تفاصيل الصلاة القادمة ومؤشر التقدم بين الصلاة الحالية والقادمة.",
        ],
    ]

    static func string(for key: String, language: String? = nil) -> String {
        let lang: Language = {
            if let language = language, let value = Language(rawValue: language) { return value }
            return current
        }()
        return strings[key]?[lang] ?? key
    }

    static func prayerName(_ label: String, language: String?) -> String {
        guard let lang = language, lang.lowercased() == "ar" else { return label }
        let translations: [String: String] = [
            "Fajr": "الفجر",
            "Dhuhr": "الظهر",
            "Asr": "العصر",
            "Maghrib": "المغرب",
            "Isha": "العشاء",
        ]
        return translations[label] ?? label
    }
}

struct LocalizedText: View {
    let key: String
    @Environment(\.userLanguage) private var language
    var body: some View {
        Text(LocalizedStrings.string(for: key, language: language))
            .multilineTextAlignment(language == "ar" ? .trailing : .leading)
    }
}

struct PrayerText: View {
    let label: String
    @Environment(\.userLanguage) private var language
    var body: some View {
        Text(LocalizedStrings.prayerName(label, language: language))
            .multilineTextAlignment(language == "ar" ? .trailing : .leading)
    }
}
extension Color {
    init(hex: String) {
        // Remove non-alphanumeric characters and any "0x" prefix.
        var hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        if hex.lowercased().hasPrefix("0x") {
            hex = String(hex.dropFirst(2))
        }

        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a: UInt64
        let r: UInt64
        let g: UInt64
        let b: UInt64

        switch hex.count {
        case 3:  // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6:  // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8:  // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            // Fallback color: white (or choose another default)
            (a, r, g, b) = (255, 255, 255, 255)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

struct WidgetConstants {
    static var currentLanguage: String = "ar"
    static let backgroundGradient = LinearGradient(
        gradient: Gradient(colors: [Color(hex: "0xff262a47"), Color(hex: "0xff262a47")]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    static var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        if currentLanguage.lowercased() == "ar" {
            formatter.locale = Locale(identifier: "ar")
            formatter.amSymbol = "ص"
            formatter.pmSymbol = "م"
        }
        return formatter
    }
    static var timeFullFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        if currentLanguage.lowercased() == "ar" {
            formatter.locale = Locale(identifier: "ar")
            formatter.amSymbol = "ص"
            formatter.pmSymbol = "م"
        }
        return formatter
    }
    static func dayFormatter(forLanguage language: String?) -> DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        if language == "ar" { formatter.locale = Locale(identifier: "ar") }
        return formatter
    }
    static func fullDateFormatter(forLanguage language: String?) -> DateFormatter {
        let formatter: DateFormatter = DateFormatter()
        formatter.dateFormat = "MMMM d, yyyy"
        if language == "ar" { formatter.locale = Locale(identifier: "ar") }
        return formatter
    }
    static let componentsFormatter: DateComponentsFormatter = {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        formatter.zeroFormattingBehavior = .pad
        return formatter
    }()
}

extension View {
    func widgetBackground() -> some View {
        self.background(WidgetConstants.backgroundGradient)
    }
}

// MARK: - Data Models

struct PrayerTime: TimelineEntry, Codable, Identifiable {
    let id: UUID
    let date: Date
    let label: String
    let time: Date
    let iconName: String
    let timezoneOffset: Int

    init(date: Date, label: String, time: Date, iconName: String, timezoneOffset: Int) {
        self.id = UUID()
        self.date = date
        self.label = label
        self.time = time
        self.iconName = iconName
        self.timezoneOffset = timezoneOffset
    }

    enum CodingKeys: String, CodingKey {
        case id, date, label, time, iconName, timezoneOffset
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decodeIfPresent(UUID.self, forKey: .id) ?? UUID()
        self.date = try container.decodeIfPresent(Date.self, forKey: .date) ?? Date()
        self.label = try container.decode(String.self, forKey: .label)
        self.time = try container.decode(Date.self, forKey: .time)
        self.iconName = try container.decode(String.self, forKey: .iconName)
        self.timezoneOffset = try container.decode(Int.self, forKey: .timezoneOffset)
    }
}

struct PrayerPayload: Codable {
    let userLanguage: String
    let userLocation: String
    let updatedAt: Date
    let prayers: [PrayerTime]

    enum CodingKeys: String, CodingKey {
        case userLanguage, userLocation, updatedAt, prayers
    }
}

// MARK: - Timeline Entry

struct PrayerTimelineEntry: TimelineEntry {
    let date: Date
    let prayers: [PrayerTime]
    let currentPrayer: PrayerTime?
    let nextPrayer: PrayerTime?
    let nextPrayers: [PrayerTime]
    let progress: Double
    let remainingTime: TimeInterval
    let userLanguage: String?
    let userLocation: String?
    let updatedAt: Date?
    let currentPrayerDate: Date?

    var layoutDirection: LayoutDirection {
        userLanguage == "ar" ? .rightToLeft : .leftToRight
    }

    static func placeholder() -> PrayerTimelineEntry {
        let payload: PrayerPayload = PrayerProvider.generateMockPayload()

        // 1. Take only the first 5 prayers from the payload
        let firstFivePrayers = Array(payload.prayers.prefix(5))

        // 2. Create PrayerCalculator using the first 5 prayers
        let calculator = PrayerCalculator(prayers: firstFivePrayers)

        return PrayerTimelineEntry(
            date: Date(),
            // 3. Use the first 5 prayers for the entry
            prayers: firstFivePrayers,
            currentPrayer: calculator.currentPrayer,
            nextPrayer: calculator.nextPrayer,
            nextPrayers: calculator.nextFourPrayers,  // This will now be based on the first 5
            progress: calculator.progress,
            remainingTime: calculator.remainingTime,
            userLanguage: payload.userLanguage,
            userLocation: payload.userLocation,
            updatedAt: payload.updatedAt,
            currentPrayerDate: calculator.currentPrayer?.date
        )
    }
}

// MARK: - Prayer Calculator

struct PrayerCalculator {
    private let prayers: [PrayerTime]
    private let currentDate: Date

    init(prayers: [PrayerTime], currentDate: Date = Date()) {
        // Ensure the prayers are sorted by time.
        self.prayers = prayers.sorted { $0.time < $1.time }
        self.currentDate = currentDate
    }

    var currentPrayer: PrayerTime? {
        // Returns the latest prayer that has started (or now if equal) before currentDate.
        return prayers.last { $0.time <= currentDate }
    }

    var nextPrayer: PrayerTime? {
        guard let current = currentPrayer,
            let index = prayers.firstIndex(where: { $0.id == current.id })
        else {
            return prayers.first  // Return first prayer if no current prayer
        }

        let nextIndex = prayers.index(after: index)
        if nextIndex < prayers.count {
            return prayers[nextIndex]
        } else {
            // Handle multi-day data: return next day's Fajr
            return prayers.first { $0.time > current.time }
        }
    }

    var nextFourPrayers: [PrayerTime] {
        guard let next = nextPrayer,
            let startIndex = prayers.firstIndex(where: { $0.id == next.id })
        else { return [] }
        let remaining = prayers[startIndex...]
        let shortfall = max(0, 5 - remaining.count)
        return Array(remaining.prefix(5)) + Array(prayers.prefix(shortfall))
    }

    var progress: Double {
        guard let current = currentPrayer, let next = nextPrayer else { return 0 }
        let total = next.time.timeIntervalSince(current.time)
        let elapsed = currentDate.timeIntervalSince(current.time)
        return min(max(elapsed / total, 0), 1)
    }

    var remainingTime: TimeInterval {
        nextPrayer.map { max(0, $0.time.timeIntervalSince(currentDate)) } ?? 0
    }
}

// MARK: - Timeline Provider

struct PrayerProvider: TimelineProvider {
    func placeholder(in context: Context) -> PrayerTimelineEntry {
        let payload: PrayerPayload = PrayerProvider.generateMockPayload()
        let calculator = PrayerCalculator(prayers: payload.prayers, currentDate: Date())
        return PrayerTimelineEntry(
            date: Date(),
            prayers: payload.prayers,
            currentPrayer: calculator.currentPrayer,
            nextPrayer: calculator.nextPrayer,
            nextPrayers: calculator.nextFourPrayers,
            progress: calculator.progress,
            remainingTime: calculator.remainingTime,
            userLanguage: payload.userLanguage,
            userLocation: payload.userLocation,
            updatedAt: payload.updatedAt,
            currentPrayerDate: calculator.currentPrayer?.date
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (PrayerTimelineEntry) -> Void) {
        completion(placeholder(in: context))
    }
    func getTimeline(
        in context: Context, completion: @escaping (Timeline<PrayerTimelineEntry>) -> Void
    ) {
        let now = Date()
        Task {
            let payload = await PrayerProvider.loadPrayerData()
            WidgetConstants.currentLanguage = payload.userLanguage

            // Always include an entry for the current time
            let currentCalculator = PrayerCalculator(prayers: payload.prayers, currentDate: now)
            let currentEntry = PrayerTimelineEntry(
                date: now,
                prayers: payload.prayers,
                currentPrayer: currentCalculator.currentPrayer,
                nextPrayer: currentCalculator.nextPrayer,
                nextPrayers: currentCalculator.nextFourPrayers,
                progress: currentCalculator.progress,
                remainingTime: currentCalculator.remainingTime,
                userLanguage: payload.userLanguage,
                userLocation: payload.userLocation,
                updatedAt: payload.updatedAt,
                currentPrayerDate: currentCalculator.currentPrayer?.date
            )

            // Generate entries for upcoming prayer times
            let upcomingPrayers = payload.prayers.filter { $0.time > now }
            let prayerEntries = upcomingPrayers.map { prayer in
                let calculator = PrayerCalculator(
                    prayers: payload.prayers, currentDate: prayer.time)
                return PrayerTimelineEntry(
                    date: prayer.time,
                    prayers: payload.prayers,
                    currentPrayer: calculator.currentPrayer,
                    nextPrayer: calculator.nextPrayer,
                    nextPrayers: calculator.nextFourPrayers,
                    progress: calculator.progress,
                    remainingTime: calculator.remainingTime,
                    userLanguage: payload.userLanguage,
                    userLocation: payload.userLocation,
                    updatedAt: payload.updatedAt,
                    currentPrayerDate: calculator.currentPrayer?.date
                )
            }

            let entries = [currentEntry] + prayerEntries

            // Refresh timeline 1 second after the last prayer or every 15 minutes
            let refreshDate: Date = {
                if let lastPrayer = upcomingPrayers.last?.time {
                    return Calendar.current.date(byAdding: .second, value: 1, to: lastPrayer)!
                } else {
                    return Calendar.current.date(byAdding: .minute, value: 15, to: now)!
                }
            }()

            let timeline = Timeline(entries: entries, policy: .after(refreshDate))
            completion(timeline)
        }
    }

    // func getTimeline(
    //     in context: Context, completion: @escaping (Timeline<PrayerTimelineEntry>) -> Void
    // ) {
    //     let now = Date()
    //     Task {
    //         let payload = await PrayerProvider.loadPrayerData()
    //         WidgetConstants.currentLanguage = payload.userLanguage

    //         // Build timeline entries from upcoming prayer times.
    //         let upcomingPrayers = payload.prayers.filter { $0.time >= now }
    //         let maxEntries = min(upcomingPrayers.count, 30)
    //         var entries: [PrayerTimelineEntry] = []

    //         for i in 0..<maxEntries {
    //             let entryDate = upcomingPrayers[i].time
    //             let calculator = PrayerCalculator(prayers: payload.prayers, currentDate: entryDate)
    //             let entry = PrayerTimelineEntry(
    //                 date: entryDate,
    //                 prayers: payload.prayers,
    //                 currentPrayer: calculator.currentPrayer,
    //                 nextPrayer: calculator.nextPrayer,
    //                 nextPrayers: calculator.nextFourPrayers,
    //                 progress: calculator.progress,
    //                 remainingTime: calculator.remainingTime,
    //                 userLanguage: payload.userLanguage,
    //                 userLocation: payload.userLocation,
    //                 updatedAt: payload.updatedAt,
    //                 currentPrayerDate: calculator.currentPrayer?.date
    //             )
    //             entries.append(entry)
    //         }

    //         // Fallback: if there are no upcoming prayers, use the current time.
    //         if entries.isEmpty {
    //             let calculator = PrayerCalculator(prayers: payload.prayers, currentDate: now)
    //             let entry = PrayerTimelineEntry(
    //                 date: now,
    //                 prayers: payload.prayers,
    //                 currentPrayer: calculator.currentPrayer,
    //                 nextPrayer: calculator.nextPrayer,
    //                 nextPrayers: calculator.nextFourPrayers,
    //                 progress: calculator.progress,
    //                 remainingTime: calculator.remainingTime,
    //                 userLanguage: payload.userLanguage,
    //                 userLocation: payload.userLocation,
    //                 updatedAt: payload.updatedAt,
    //                 currentPrayerDate: calculator.currentPrayer?.date
    //             )
    //             entries.append(entry)
    //         }

    //         // Refresh timeline one second after the last entry.
    //         let lastEntryDate = entries.last!.date
    //         let refreshDate =
    //             Calendar.current.date(byAdding: .second, value: 1, to: lastEntryDate)
    //             ?? now.addingTimeInterval(60)
    //         let timeline = Timeline(entries: entries, policy: .after(refreshDate))
    //         completion(timeline)
    //     }
    // }

    private static func loadPrayerData() async -> PrayerPayload {
        if let jsonString: String = UserDefaults(suiteName: "group.salawati_prayer_time")?.string(
            forKey: "prayerDataNew"),
            let data = jsonString.data(using: .utf8)
        {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .millisecondsSince1970
            do {
                let payload = try decoder.decode(PrayerPayload.self, from: data)
                // Use the full set of prayer data (sorted) for the timeline.
                return PrayerPayload(
                    userLanguage: payload.userLanguage,
                    userLocation: payload.userLocation,
                    updatedAt: payload.updatedAt,
                    prayers: payload.prayers.sorted { $0.time < $1.time }
                )
            } catch {
                print("Widget: JSON Decoding Error: \(error)")
                return generateMockPayload()
            }
        }
        return generateMockPayload()
    }

    // Updated mock payload generating 25 days of prayer times.
    static func generateMockPayload() -> PrayerPayload {
        let now = Date()
        let prayers = generateMockData25Days()
        return PrayerPayload(
            userLanguage: "ar",
            userLocation: "موقع غير معروف",
            updatedAt: now,
            prayers: prayers
        )
    }

    static func generateMockData25Days() -> [PrayerTime] {
        let base = Calendar.current.startOfDay(for: Date())
        let labels = ["Fajr", "Dhuhr", "Asr", "Maghrib", "Isha"]
        var list: [PrayerTime] = []

        for day in 0..<25 {
            let dayBase = Calendar.current.date(byAdding: .day, value: day, to: base)!
            // Assign realistic times (e.g., Fajr at 5 AM)
            let offsets: [TimeInterval] = [
                5 * 3600, 12 * 3600, 15 * 3600, 18 * 3600, 20 * 3600,
            ]
            for (index, label) in labels.enumerated() {
                let prayerTime = dayBase.addingTimeInterval(offsets[index])
                list.append(
                    PrayerTime(
                        date: dayBase,
                        label: label,
                        time: prayerTime,
                        iconName: "icon_name",
                        timezoneOffset: 0
                    )
                )
            }
        }
        return list.sorted { $0.time < $1.time }
    }
}

// MARK: - Widget Views

struct NextPrayerSmallWidgetEntryView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        let now = Date()
        ZStack {
            Color.clear.widgetBackground().ignoresSafeArea()
            VStack(alignment: .center, spacing: 8) {
                if let nextPrayer = entry.nextPrayer {
                    ProgressCircle(progress: entry.progress, icon: nextPrayer.iconName)
                    //     .frame(width: 60, height: 60)
                    //     .accessibilityElement(children: .ignore)
                    //     .accessibilityLabel(
                    //         "Next Prayer: \(LocalizedStrings.prayerName(nextPrayer.label, language: entry.userLanguage))"
                    //     )
                    Text(
                        LocalizedStrings.prayerName(nextPrayer.label, language: entry.userLanguage)
                    )
                    .font(.headline)
                    .foregroundColor(.white)
                    .lineLimit(1)
                    Text(WidgetConstants.timeFormatter.string(from: nextPrayer.time))
                        .font(.subheadline)
                        .foregroundColor(.white)
                    // Dynamic countdown using TimelineView.
                    // TimelineView(.periodic(from: Date(), by: 1)) { context in
                    //     let currentDate = context.date
                    //     let calculator = PrayerCalculator(
                    //         prayers: entry.prayers, currentDate: currentDate)
                    //     Text(
                    //         WidgetConstants.componentsFormatter.string(
                    //             from: calculator.remainingTime) ?? "--:--:--"
                    //     )
                    //     .font(.subheadline)
                    //     .foregroundColor(.white)
                    // }

                    Text(timerInterval: now...nextPrayer.time)
                        .font(.subheadline)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)  // Optional: for multi-line alignment

                } else {
                    LocalizedText(key: "No Prayer")
                        .foregroundColor(.white)
                }
            }
            .padding()
            .accessibilityElement(children: .combine)
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
    }
}

struct AllPrayersSmallWidgetEntryView: View {
    var entry: PrayerTimelineEntry

    // Filter prayers to current day and take first 5
    private var currentDayPrayers: [PrayerTime] {
        let calendar = Calendar.current
        let currentDate = entry.currentPrayerDate ?? entry.date
        return entry.prayers.filter {
            calendar.isDate($0.date, inSameDayAs: currentDate)
        }.prefix(5).map { $0 }
    }

    var body: some View {
        ZStack {
            Color.clear.widgetBackground()
            VStack(alignment: .leading, spacing: 8) {
                if currentDayPrayers.isEmpty {
                    Text(
                        LocalizedStrings.string(
                            for: "No Prayer Times Available", language: entry.userLanguage)
                    )
                    .foregroundColor(.white)
                } else {
                    ForEach(currentDayPrayers) { prayer in
                        HStack {
                            Image(
                                systemName: prayer.time <= Date()
                                    ? "checkmark.circle.fill" : "circle"
                            )
                            .foregroundColor(.white)
                            PrayerText(label: prayer.label)
                                .font(.caption)
                                .foregroundColor(.white)
                            Spacer()
                            Text(WidgetConstants.timeFormatter.string(from: prayer.time))
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                        .accessibilityElement(children: .combine)
                    }
                }
            }
            .padding()
        }
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
    }
}
struct SalahWidgetEntryView: View {
    var entry: PrayerTimelineEntry
    @State private var isHidden: Bool  // Remove default

    // Custom initializer to set @State from parent
    init(entry: PrayerTimelineEntry, isHidden: Bool) {
        self.entry = entry
        self._isHidden = State(initialValue: isHidden)
    }

    var body: some View {
        ZStack {
            Color.clear.widgetBackground()
            VStack {
                if !isHidden {
                    DateHeaderView(prayerDate: entry.currentPrayerDate)
                        .frame(height: 40)
                    Spacer()
                } else {
                    EmptyView()
                }
                // Use a dynamic TimelineView that updates every second using the current time.
                // TimelineView(.periodic(from: Date(), by: 1)) { context in
                //     let currentDate = context.date
                //     let calculator = PrayerCalculator(
                //         prayers: entry.prayers, currentDate: currentDate)

                //     PrayerProgressHeader(
                //         currentPrayer: calculator.currentPrayer,
                //         nextPrayer: calculator.nextPrayer,
                //         progress: calculator.progress,
                //         remainingTime: calculator.remainingTime,
                //         language: entry.userLanguage
                //     )
                //     .accessibilityElement(children: .combine)
                // }
                PrayerProgressHeader(
                    currentPrayer: entry.currentPrayer,
                    nextPrayer: entry.nextPrayer,
                    progress: entry.progress,
                    remainingTime: entry.remainingTime,
                    language: entry.userLanguage
                )
                .accessibilityElement(children: .combine)
                Spacer()
                PrayerTimelineRow(prayers: entry.nextPrayers, language: entry.userLanguage)
                    .frame(height: 60)
                Spacer()
                if !isHidden {
                    Spacer()
                    MetadataView(
                        language: entry.userLanguage,
                        location: entry.userLocation,
                        updatedAt: entry.updatedAt
                    )
                } else {
                    EmptyView()
                }
            }
            .padding()
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
    }
}

struct ExtraMediumPrayerWidgetView: View {
    var entry: PrayerTimelineEntry
    var body: some View {
        let now = Date()
        ZStack {
            Color.clear.widgetBackground().ignoresSafeArea()
            VStack(spacing: 20) {
                VStack(spacing: 8) {
                    HStack {
                        ProgressCircle(
                            progress: entry.progress,
                            icon: entry.nextPrayer?.iconName ?? "clock"
                        )
                        .accessibilityElement(children: .ignore)
                        .accessibilityLabel("Next Prayer Progress")
                        VStack(alignment: .leading) {
                            LocalizedText(key: "Next Prayer")
                                .font(.subheadline.bold())
                                .foregroundColor(.white.opacity(0.8))
                            Text(
                                LocalizedStrings.prayerName(
                                    entry.nextPrayer?.label ?? "--",
                                    language: entry.userLanguage)
                            )
                            .font(.title2.bold())
                            .foregroundColor(.white)
                            .lineLimit(1)
                        }
                        Spacer()
                        VStack(alignment: .trailing) {
                            if let nextTime = entry.nextPrayer?.time {

                                Text(
                                    timerInterval: now...nextTime

                                )

                                // Text(
                                //     WidgetConstants.componentsFormatter.string(
                                //         from: calculator.remainingTime)
                                //         ?? "--:--:--"
                                // )
                                .font(.subheadline.bold())
                                .foregroundColor(.white)
                                .multilineTextAlignment(.trailing)

                                Text(WidgetConstants.timeFormatter.string(from: nextTime))
                                    .font(.subheadline.bold())
                                    .foregroundColor(.white)
                            }
                        }
                    }
                    Spacer()
                    HStack {
                        if let currentPrayer = entry.currentPrayer {
                            VStack {
                                Image(systemName: currentPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        currentPrayer.label,
                                        language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                        GeometryReader { geo in
                            ZStack(alignment: .leading) {
                                Capsule().fill(Color.white.opacity(0.3))
                                Capsule()
                                    .fill(Color.white)
                                    .frame(width: geo.size.width * CGFloat(entry.progress))
                            }
                        }
                        .frame(height: 8)
                        if let nextPrayer = entry.nextPrayer {
                            VStack {
                                Image(systemName: nextPrayer.iconName)
                                    .font(.caption.bold())
                                    .foregroundColor(.white)
                                Text(
                                    LocalizedStrings.prayerName(
                                        nextPrayer.label,
                                        language: entry.userLanguage)
                                )
                                .font(.caption)
                                .foregroundColor(.white)
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding()
            }
        }
        .widgetURL(URL(string: "com.salawati.app://widget&homeWidget"))
        .environment(\.userLanguage, entry.userLanguage)
        .environment(\.layoutDirection, entry.layoutDirection)
    }
}
struct MetadataView: View {
    let language: String?
    let location: String?
    let updatedAt: Date?

    var body: some View {

        VStack(alignment: .leading, spacing: 4) {
            HStack {
                if let location = location {
                    HStack {
                        LocalizedText(key: "Location:")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                        Text(location)
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                Spacer()
                if let updatedAt = updatedAt {
                    HStack {
                        LocalizedText(key: "Updated:")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                        Text(WidgetConstants.timeFormatter.string(from: updatedAt))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .frame(height: 60)

    }
}
struct DateHeaderView: View {
    let prayerDate: Date?
    @Environment(\.userLanguage) private var language

    var body: some View {
        if let date = prayerDate {
            HStack {
                Text(WidgetConstants.dayFormatter(forLanguage: language).string(from: date))
                    .font(.headline)
                    .foregroundColor(.white)
                Text(WidgetConstants.fullDateFormatter(forLanguage: language).string(from: date))
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 16)
        }
    }
}

// MARK: - Supporting Views

struct ProgressCircle: View {
    let progress: Double
    let icon: String
    @Environment(\.layoutDirection) var layoutDirection

    var body: some View {
        ZStack {
            Circle().stroke(Color.white.opacity(0.3), lineWidth: 4)
            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(Color.white, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                .rotationEffect(
                    layoutDirection == .rightToLeft ? .degrees(90) : .degrees(-90)
                )
                .animation(.linear(duration: 1), value: progress)
            Image(systemName: icon)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
        }
        .frame(width: 35, height: 35)
        .accessibilityElement(children: .ignore)
        // .accessibilityLabel("Prayer progress: \(Int(progress * 100))%")
    }
}

struct PrayerTimeTile: View {
    let prayer: PrayerTime
    let language: String?

    var body: some View {
        VStack {
            ZStack {
                Circle().fill(Color.white.opacity(0.2))
                Image(systemName: prayer.iconName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            .frame(width: 30, height: 30)
            Text(WidgetConstants.timeFormatter.string(from: prayer.time))
                .font(.caption2.weight(.bold))
                .foregroundColor(.white)
            Text(LocalizedStrings.prayerName(prayer.label, language: language))
                .font(.caption2.weight(.bold))
                .foregroundColor(.white.opacity(0.9))
        }
        .accessibilityElement(children: .combine)
    }
}

// MARK: - Widget Configurations

struct NextPrayerSmallWidget: Widget {
    let kind: String = "NextPrayerSmallWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            NextPrayerSmallWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Next Prayer"))
        .description(LocalizedStrings.string(for: "Next Prayer Description"))
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}

struct AllPrayersSmallWidget: Widget {
    let kind: String = "AllPrayersSmallWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            AllPrayersSmallWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "All Prayers"))
        .description(LocalizedStrings.string(for: "All Prayers Description"))
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}

struct MediumPrayerWidget: Widget {
    let kind: String = "MediumPrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            SalahWidgetEntryView(entry: entry, isHidden: true)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times"))
        .description(LocalizedStrings.string(for: "Prayer Times Description"))
        .supportedFamilies([.systemMedium])
        .contentMarginsDisabled()
    }
}

struct LargePrayerWidget: Widget {
    let kind: String = "LargePrayerWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            SalahWidgetEntryView(entry: entry, isHidden: false)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times - Full"))
        .description(LocalizedStrings.string(for: "Prayer Times - Full Description"))
        .supportedFamilies([.systemLarge])
        .contentMarginsDisabled()
    }
}

struct ExtraMediumPrayerWidget: Widget {
    let kind: String = "ExtraMediumPrayerWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            ExtraMediumPrayerWidgetView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Extra Medium Prayer Widget"))
        .description(LocalizedStrings.string(for: "Extra Medium Prayer Widget Description"))
        .supportedFamilies([.systemMedium])
        .contentMarginsDisabled()
    }
}

@main
struct SalahWidgetBundle: WidgetBundle {
    @WidgetBundleBuilder
    var body: some Widget {
        NextPrayerSmallWidget()
        AllPrayersSmallWidget()
        MediumPrayerWidget()
        // ExtraMediumPrayerWidget()
        LargePrayerWidget()
        // LargeScrollablePrayerWidget()
    }
}

struct PrayerProgressHeader: View {
    let currentPrayer: PrayerTime?
    let nextPrayer: PrayerTime?
    let progress: Double
    let remainingTime: TimeInterval
    let language: String?

    var body: some View {
        GeometryReader { geometry in
            let availableWidth = geometry.size.width
            let now: Date = Date()

            HStack(spacing: availableWidth * 0.05) {
                ProgressCircle(progress: progress, icon: currentPrayer?.iconName ?? "clock")
                VStack(alignment: .leading, spacing: 4) {
                    LocalizedText(key: "Next Prayer")
                        .font(availableWidth < 150 ? .caption : .subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    Text(LocalizedStrings.prayerName(nextPrayer?.label ?? "--", language: language))
                        .font(.title2.weight(.semibold))
                        .foregroundColor(.white)
                        .lineLimit(1)
                        .minimumScaleFactor(0.5)
                }
                Spacer()
                VStack(alignment: .trailing, spacing: 4) {
                    // Directly display the dynamic countdown based on remainingTime.
                    if let nextTime = nextPrayer?.time {
                        // Text(
                        //     WidgetConstants.componentsFormatter.string(from: remainingTime)
                        //         ?? "--:--:--"
                        // )

                        Text(
                            timerInterval: now...nextTime

                        )

                        // Text(
                        //     WidgetConstants.componentsFormatter.string(
                        //         from: calculator.remainingTime)
                        //         ?? "--:--:--"
                        // )
                        .font(.subheadline.bold())
                        .foregroundColor(.white)
                        .multilineTextAlignment(.trailing)
                        .font(.headline.weight(.bold))
                        .foregroundColor(.white)
                        .lineLimit(1)
                        .minimumScaleFactor(0.5)

                        Text(WidgetConstants.timeFormatter.string(from: nextTime))
                            .font(.headline.weight(.bold))
                            .foregroundColor(.white)
                            .lineLimit(1)
                    }
                }
            }
            .padding(.horizontal, availableWidth * 0.05)
        }
    }
}

struct PrayerTimelineRow: View {
    let prayers: [PrayerTime]
    let language: String?

    var body: some View {
        HStack(spacing: 12) {
            ForEach(prayers.prefix(5)) { prayer in
                PrayerTimeTile(prayer: prayer, language: language)
            }
        }
        .padding(.horizontal, 10)
    }
}

// MARK: - New Large Scrollable Widget View

struct LargeScrollablePrayerWidgetEntryView: View {
    var entry: PrayerTimelineEntry

    var body: some View {
        let now: Date = Date()

        VStack(alignment: .center, spacing: 12) {
            // A header that displays the full date (using the first prayer's date)
            if let firstPrayer = entry.prayers.first {
                Text(
                    WidgetConstants.fullDateFormatter(forLanguage: entry.userLanguage).string(
                        from: firstPrayer.date)
                )
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal)
                .multilineTextAlignment(.center)
            }

            // List all prayers
            ForEach(entry.prayers.prefix(5)) { prayer in
                HStack(alignment: .top, spacing: 4) {
                    ZStack {
                        Circle().stroke(Color.white.opacity(0.3), lineWidth: 4)
                        Circle()
                            .stroke(Color.white, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                            .rotationEffect(
                                .degrees(90)
                            )

                        Image(systemName: prayer.iconName)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    Spacer()
                    Text(
                        LocalizedStrings.prayerName(
                            prayer.label, language: entry.userLanguage)
                    )
                    .font(.headline)
                    .foregroundColor(.white)
                    Spacer()
                    Text(WidgetConstants.timeFullFormatter.string(from: prayer.time))
                        .font(.subheadline)
                        .foregroundColor(.white)
                    Spacer()

                    // Text(
                    //     WidgetConstants.fullDateFormatter(forLanguage: entry.userLanguage)
                    //         .string(from: prayer.date)
                    // )
                    // .font(.caption)
                    // .foregroundColor(.white.opacity(0.8))

                }
                .padding(.vertical, 4)
                Divider().background(Color.white.opacity(0.3))
            }
        }
        .padding()
        .widgetBackground()  // Apply gradient background here
        .ignoresSafeArea()
    }
}

struct LargeScrollablePrayerWidget: Widget {
    let kind: String = "LargeScrollablePrayerWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            LargeScrollablePrayerWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "Prayer Times List"))
        .description(LocalizedStrings.string(for: "A list of all prayer times with full dates."))
        .supportedFamilies([.systemLarge])
        .contentMarginsDisabled()
    }
}



struct AllPrayersSmallWidget: Widget {
    let kind: String = "AllPrayersSmallWidget"
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PrayerProvider()) { entry in
            AllPrayersSmallWidgetEntryView(entry: entry)
                .environment(\.userLanguage, entry.userLanguage)
                .environment(\.layoutDirection, entry.layoutDirection)
        }
        .configurationDisplayName(LocalizedStrings.string(for: "All Prayers"))
        .description(LocalizedStrings.string(for: "All Prayers Description"))
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}