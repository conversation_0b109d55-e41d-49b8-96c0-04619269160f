import 'package:flutter_test/flutter_test.dart';
import 'package:awesome_notifications_service/src/utils/notification_utils.dart';
import 'package:awesome_notifications_service/src/constants/notification_constants.dart';
import 'package:awesome_notifications_service/src/models/prayer_notification.dart';

void main() {
  group('Sunnah Prayer Notifications', () {
    test('should include Sunnah prayers in allPrayers list', () {
      final allPrayers = NotificationConstants.allPrayers;
      final obligatoryPrayers = NotificationConstants.prayers;
      final sunnahPrayers = NotificationConstants.sunnahPrayers;

      // Verify all prayers contains both obligatory and Sunnah prayers
      expect(allPrayers.length,
          equals(obligatoryPrayers.length + sunnahPrayers.length));

      // Verify all obligatory prayers are included
      for (final prayer in obligatoryPrayers) {
        expect(allPrayers.contains(prayer), isTrue,
            reason: 'allPrayers should contain obligatory prayer: $prayer');
      }

      // Verify all Sunnah prayers are included
      for (final prayer in sunnahPrayers) {
        expect(allPrayers.contains(prayer), isTrue,
            reason: 'allPrayers should contain Sunnah prayer: $prayer');
      }
    });

    test('should have correct Sunnah prayer names', () {
      final sunnahPrayers = NotificationConstants.sunnahPrayers;

      expect(sunnahPrayers.length, equals(2));
      expect(sunnahPrayers.contains(NotificationConstants.middleOfTheNight),
          isTrue);
      expect(sunnahPrayers.contains(NotificationConstants.lastThirdOfTheNight),
          isTrue);
    });

    test('should localize Sunnah prayer names correctly in Arabic', () {
      final middleOfNightArabic = NotificationUtils.getLocalizedPrayerName(
          NotificationConstants.middleOfTheNight,
          locale: 'ar');
      final lastThirdArabic = NotificationUtils.getLocalizedPrayerName(
          NotificationConstants.lastThirdOfTheNight,
          locale: 'ar');

      expect(middleOfNightArabic, equals('منتصف الليل'));
      expect(lastThirdArabic, equals('الثلث الأخير من الليل'));
    });

    test('should localize Sunnah prayer names correctly in English', () {
      final middleOfNightEnglish = NotificationUtils.getLocalizedPrayerName(
          NotificationConstants.middleOfTheNight,
          locale: 'en');
      final lastThirdEnglish = NotificationUtils.getLocalizedPrayerName(
          NotificationConstants.lastThirdOfTheNight,
          locale: 'en');

      expect(
          middleOfNightEnglish, equals(NotificationConstants.middleOfTheNight));
      expect(
          lastThirdEnglish, equals(NotificationConstants.lastThirdOfTheNight));
    });

    test(
        'should generate appropriate notification body for Sunnah prayers in Arabic',
        () {
      final middleOfNightBody = NotificationUtils.getLocalizedNotificationBody(
          NotificationConstants.prayerTime,
          NotificationConstants.middleOfTheNight,
          locale: 'ar');
      final lastThirdBody = NotificationUtils.getLocalizedNotificationBody(
          NotificationConstants.prayerTime,
          NotificationConstants.lastThirdOfTheNight,
          locale: 'ar');

      expect(middleOfNightBody, equals('حان الآن وقت قيام منتصف الليل'));
      expect(lastThirdBody, equals('حان الآن وقت قيام الثلث الأخير من الليل'));
    });

    test(
        'should generate appropriate notification body for Sunnah prayers in English',
        () {
      final middleOfNightBody = NotificationUtils.getLocalizedNotificationBody(
          NotificationConstants.prayerTime,
          NotificationConstants.middleOfTheNight,
          locale: 'en');
      final lastThirdBody = NotificationUtils.getLocalizedNotificationBody(
          NotificationConstants.prayerTime,
          NotificationConstants.lastThirdOfTheNight,
          locale: 'en');

      expect(
          middleOfNightBody, equals('Now its time for night prayer (Qiyam)'));
      expect(
          lastThirdBody, equals('Now its time for night prayer (Last third)'));
    });

    test(
        'should generate appropriate pre-notification body for Sunnah prayers (for completeness)',
        () {
      // Note: In practice, Sunnah prayers don't use pre-notifications, but the utility method should still work
      final middleOfNightPreBody =
          NotificationUtils.getLocalizedNotificationBody(
              NotificationConstants.prePrayerWarning,
              NotificationConstants.middleOfTheNight,
              locale: 'ar');
      final lastThirdPreBody = NotificationUtils.getLocalizedNotificationBody(
          NotificationConstants.prePrayerWarning,
          NotificationConstants.lastThirdOfTheNight,
          locale: 'en');

      expect(middleOfNightPreBody, equals('15 دقيقة على وقت القيام'));
      expect(lastThirdPreBody, equals('15 minutes for night prayer'));
    });

    test('PrayerTimes should handle Sunnah prayer times correctly', () {
      final now = DateTime.now();
      final prayerTimes = PrayerTimes(
        fajr: now.add(const Duration(hours: 1)),
        sunrise: now.add(const Duration(hours: 2)),
        dhuhr: now.add(const Duration(hours: 6)),
        asr: now.add(const Duration(hours: 9)),
        maghrib: now.add(const Duration(hours: 12)),
        isha: now.add(const Duration(hours: 14)),
        middleOfNight: now.add(const Duration(hours: 18)),
        lastThirdOfNight: now.add(const Duration(hours: 20)),
      );

      // Test getPrayerTime method for Sunnah prayers
      final middleOfNightTime =
          prayerTimes.getPrayerTime(NotificationConstants.middleOfTheNight);
      final lastThirdTime =
          prayerTimes.getPrayerTime(NotificationConstants.lastThirdOfTheNight);

      expect(middleOfNightTime, isNotNull);
      expect(lastThirdTime, isNotNull);
      expect(middleOfNightTime, equals(prayerTimes.middleOfNight));
      expect(lastThirdTime, equals(prayerTimes.lastThirdOfNight));
    });

    test('PrayerTimes toMap should include Sunnah prayers when available', () {
      final now = DateTime.now();
      final prayerTimes = PrayerTimes(
        fajr: now.add(const Duration(hours: 1)),
        sunrise: now.add(const Duration(hours: 2)),
        dhuhr: now.add(const Duration(hours: 6)),
        asr: now.add(const Duration(hours: 9)),
        maghrib: now.add(const Duration(hours: 12)),
        isha: now.add(const Duration(hours: 14)),
        middleOfNight: now.add(const Duration(hours: 18)),
        lastThirdOfNight: now.add(const Duration(hours: 20)),
      );

      final prayerMap = prayerTimes.toMap();

      // Verify obligatory prayers are included
      expect(prayerMap.containsKey(NotificationConstants.fajr), isTrue);
      expect(prayerMap.containsKey(NotificationConstants.dhuhr), isTrue);
      expect(prayerMap.containsKey(NotificationConstants.isha), isTrue);

      // Verify Sunnah prayers are included
      expect(prayerMap.containsKey(NotificationConstants.middleOfTheNight),
          isTrue);
      expect(prayerMap.containsKey(NotificationConstants.lastThirdOfTheNight),
          isTrue);

      // Verify total count
      expect(prayerMap.length, equals(8)); // 6 obligatory + 2 Sunnah
    });

    test('PrayerNotificationConfig should work with Sunnah prayer names', () {
      // Note: While config can have pre-notification and iqamah settings,
      // the service will ignore these for Sunnah prayers and use default sound
      final middleOfNightConfig = PrayerNotificationConfig(
        prayerName: NotificationConstants.middleOfTheNight,
        isNotified: true,
        isPreNotified: false, // Not used for Sunnah prayers
        soundIndex: 2, // Will be overridden to use default sound
        useDefaultSound: false, // Will be overridden to true
      );

      final lastThirdConfig = PrayerNotificationConfig(
        prayerName: NotificationConstants.lastThirdOfTheNight,
        isNotified: true,
        isPreNotified: false, // Not used for Sunnah prayers
        iqamahMinutes: null, // Not used for Sunnah prayers
        soundIndex: 1, // Will be overridden to use default sound
        useDefaultSound: false, // Will be overridden to true
      );

      expect(middleOfNightConfig.prayerName,
          equals(NotificationConstants.middleOfTheNight));
      expect(middleOfNightConfig.isNotified, isTrue);

      expect(lastThirdConfig.prayerName,
          equals(NotificationConstants.lastThirdOfTheNight));
      expect(lastThirdConfig.isNotified, isTrue);
      expect(lastThirdConfig.iqamahMinutes, isNull);
    });

    test('should generate unique notification IDs for Sunnah prayers', () {
      final scheduledTime = DateTime(2025, 6, 25, 2, 0); // 2 AM
      final ids = <int>[];

      // Sunnah prayers only generate main notification IDs (no pre or iqamah)
      for (final sunnahPrayer in NotificationConstants.sunnahPrayers) {
        final id = NotificationUtils.generateNotificationId(
          notificationType: NotificationConstants.prayerTime,
          identifier: '${sunnahPrayer}_main',
          scheduledTime: scheduledTime,
        );
        ids.add(id);
      }

      // Verify all IDs are unique
      final uniqueIds = ids.toSet();
      expect(uniqueIds.length, equals(ids.length),
          reason: 'All Sunnah prayer notification IDs should be unique');
      expect(
          ids.length, equals(2)); // 2 Sunnah prayers × 1 type each (main only)
    });
  });
}
