import 'dart:io';

import 'package:get/utils.dart';

import '../constants/notification_constants.dart';
import '../models/prayer_notification.dart';

/// Utility functions for handling notification sounds
class SoundUtils {
  /// Get athan sound path by type and short version preference
  static String getAthanSoundPath(AthanSoundType soundType, bool isShort) {
    final soundName = _getAthanSoundName(soundType, true);
    if (soundName != null) {
      // CORRECT: The path for a native resource must NOT include the extension.
      final soundPath = 'resource://raw/$soundName';
      return soundPath;
    }
    return 'resource://raw/athan3'; // Default fallback
  }

  static String? getAthanSoundPathAssets(
      AthanSoundType soundType, bool isShort) {
    final soundName = _getAthanSoundName(soundType, isShort);
    if (soundName != null) {
      // Use appropriate extension based on sound type
      String extension;
      if (soundType == AthanSoundType.bird) {
        extension = '.mp3'; // Bird sound is MP3
      } else {
        extension = '.wav'; // Other athan sounds are WAV
      }
      final soundPath = 'assets/sounds/$soundName$extension';
      print('🔊 Generated sound path: $soundPath');
      return soundPath;
    }
    return null;
  }

  /// Get athan sound name without path
  static String? getAthanSoundName(AthanSoundType soundType, bool isShort) {
    return _getAthanSoundName(soundType, isShort);
  }

  /// Internal method to get sound name
  static String? _getAthanSoundName(AthanSoundType soundType, bool isShort) {
    String? soundName;
    switch (soundType) {
      case AthanSoundType.athan1:
        soundName = isShort
            ? NotificationConstants.kAthan1Short
            : NotificationConstants.kAthan1;
        break;
      case AthanSoundType.athan2:
        soundName = isShort
            ? NotificationConstants.kAthan2Short
            : NotificationConstants.kAthan2;
        break;
      case AthanSoundType.athan3:
        soundName = isShort
            ? NotificationConstants.kAthan3Short
            : NotificationConstants.kAthan3;
        break;
      case AthanSoundType.athan4:
        soundName = isShort
            ? NotificationConstants.kAthan4Short
            : NotificationConstants.kAthan4;
        break;
      case AthanSoundType.bird:
        soundName = NotificationConstants.kBird;
        break;
    }
    Get.log(
        '🎵 _getAthanSoundName: $soundType, isShort: $isShort → $soundName');
    return soundName;
  }

  /// Get dhikr sound path
  static String? getDhikrSoundPath(String soundName) {
    if (soundName.isEmpty) return null;
    return 'resource://raw/$soundName.mp3'; // Dhikr sounds are .mp3
  }

  /// Get iqama sound path
  static String getIqamaSoundPath() {
    return 'resource://raw/${NotificationConstants.kIqama}';
  }

  /// Get pre-athan sound path
  static String getPreAthanSoundPath() {
    return 'resource://raw/${NotificationConstants.kPreAthan}';
  }

  /// Get sound path for prayer notification
  static String getPrayerSoundPath({
    required String prayerName,
    required int soundIndex,
    required bool useDefaultSound,
    required bool isShort,
  }) {
    print('🔊 SoundUtils.getPrayerSoundPath called:');
    print('   - prayerName: $prayerName');
    print('   - soundIndex: $soundIndex');
    print('   - useDefaultSound: $useDefaultSound');
    print('   - isShort: $isShort');

    if (useDefaultSound) {
      print('   - Result: "athan3" (using default sound)');
      return 'athan3'; // Use a default sound instead of null
    }

    // Special case for sunrise - always use bird sound
    if (prayerName.toLowerCase() ==
        NotificationConstants.sunrise.toLowerCase()) {
      final soundPath = getAthanSoundPath(AthanSoundType.bird, false) ?? 'bird';
      print('   - Result: $soundPath (sunrise bird sound)');
      return soundPath;
    }

    // Get athan sound based on index
    final soundTypes = AthanSoundType.values;
    if (soundIndex >= 0 && soundIndex < soundTypes.length) {
      // Include all sound types including bird
      String? soundPath;
      if (soundIndex == 4) {
        soundPath = getAthanSoundPath(soundTypes[0], isShort);
      } else {
        soundPath = getAthanSoundPath(soundTypes[soundIndex], isShort);
      }

      soundPath ??= 'athan3';

      print(
          '   - Result: $soundPath (athan sound index $soundIndex) for1 ${prayerName.toLowerCase()} ');
      return soundPath;
    }

    // Default to athan3 if index is invalid
    final soundPath =
        getAthanSoundPath(AthanSoundType.athan3, isShort) ?? 'athan3';
    print('   - Result: $soundPath (default athan3, invalid index)');
    return soundPath;
  }

  /// Get all available athan sounds
  static List<AthanSoundInfo> getAvailableAthanSounds() {
    return [
      AthanSoundInfo(
        type: AthanSoundType.athan1,
        name: 'أذان 1',
        nameEn: 'Athan 1',
        fileName: NotificationConstants.kAthan1,
      ),
      AthanSoundInfo(
        type: AthanSoundType.athan2,
        name: 'أذان 2',
        nameEn: 'Athan 2',
        fileName: NotificationConstants.kAthan2,
      ),
      AthanSoundInfo(
        type: AthanSoundType.athan3,
        name: 'أذان 3',
        nameEn: 'Athan 3',
        fileName: NotificationConstants.kAthan3,
      ),
      AthanSoundInfo(
        type: AthanSoundType.athan4,
        name: 'أذان 4',
        nameEn: 'Athan 4',
        fileName: NotificationConstants.kAthan4,
      ),
    ];
  }

  /// Get all available dhikr sounds
  static List<DhikrSoundInfo> getAvailableDhikrSounds() {
    return [
      DhikrSoundInfo(
        name: 'تسبيح وحمد',
        nameEn: 'Tasbih and Hamd',
        fileName: NotificationConstants.soundTasbhamd,
        description: 'سبحان الله والحمد لله',
      ),
      DhikrSoundInfo(
        name: 'تكبير',
        nameEn: 'Takbeer',
        fileName: NotificationConstants.soundTakbeer,
        description: 'الله أكبر',
      ),
      DhikrSoundInfo(
        name: 'توحيد',
        nameEn: 'Tawheed',
        fileName: NotificationConstants.soundTawheed,
        description: 'لا إله إلا الله',
      ),
      DhikrSoundInfo(
        name: 'تسبيح وتعظيم',
        nameEn: 'Tasbih and Ta\'dheem',
        fileName: NotificationConstants.soundTasbta3zeem,
        description: 'سبحان الله العظيم',
      ),
      DhikrSoundInfo(
        name: 'استغفار',
        nameEn: 'Istighfar',
        fileName: NotificationConstants.soundEsteghfar,
        description: 'أستغفر الله',
      ),
      DhikrSoundInfo(
        name: 'صلاة على النبي',
        nameEn: 'Salah on Prophet',
        fileName: NotificationConstants.soundSalah,
        description: 'اللهم صل على محمد',
      ),
      DhikrSoundInfo(
        name: 'الباقيات الصالحات',
        nameEn: 'Al-Baqiyat As-Salihat',
        fileName: NotificationConstants.soundBaqyat,
        description: 'سبحان الله والحمد لله ولا إله إلا الله والله أكبر',
      ),
      DhikrSoundInfo(
        name: 'حوقلة',
        nameEn: 'Hawqala',
        fileName: NotificationConstants.soundHawqalah,
        description: 'لا حول ولا قوة إلا بالله',
      ),
      DhikrSoundInfo(
        name: 'دعاء الغم',
        nameEn: 'Du\'a Al-Ghamm',
        fileName: NotificationConstants.soundGhamm,
        description: 'لا إله إلا الله العظيم الحليم',
      ),
      DhikrSoundInfo(
        name: 'العدد',
        nameEn: 'Al-Adad',
        fileName: NotificationConstants.soundAdadd,
        description: 'اللهم أعني على ذكرك وشكرك وحسن عبادتك',
      ),
    ];
  }

  /// Check if sound file exists in resources
  static bool soundFileExists(String fileName) {
    // This would need platform-specific implementation
    // For now, assume all predefined sounds exist
    final predefinedSounds = [
      NotificationConstants.kAthan1,
      NotificationConstants.kAthan2,
      NotificationConstants.kAthan3,
      NotificationConstants.kAthan4,
      NotificationConstants.kAthan1Short,
      NotificationConstants.kAthan2Short,
      NotificationConstants.kAthan3Short,
      NotificationConstants.kAthan4Short,
      NotificationConstants.kBird,
      NotificationConstants.kIqama,
      NotificationConstants.kPreAthan,
      NotificationConstants.soundGhamm,
      NotificationConstants.soundAdadd,
      NotificationConstants.soundTawheed,
      NotificationConstants.soundTasbta3zeem,
      NotificationConstants.soundTasbhamd,
      NotificationConstants.soundSalah,
      NotificationConstants.soundEsteghfar,
      NotificationConstants.soundBaqyat,
      NotificationConstants.soundHawqalah,
      NotificationConstants.soundTakbeer,
    ];

    return predefinedSounds.contains(fileName);
  }

  /// Get sound duration (estimated)
  static Duration getSoundDuration(String fileName, {bool isShort = false}) {
    if (fileName.contains('athan')) {
      return isShort ? const Duration(seconds: 30) : const Duration(minutes: 3);
    } else if (fileName.contains('iqama')) {
      return const Duration(seconds: 45);
    } else if (fileName.contains('pre_athan')) {
      return const Duration(seconds: 10);
    } else if (fileName.contains('bird')) {
      return const Duration(seconds: 15);
    } else {
      // Dhikr sounds
      return const Duration(seconds: 5);
    }
  }

  /// Validate sound path format
  static bool isValidSoundPath(String? soundPath) {
    if (soundPath == null || soundPath.isEmpty) return false;

    // Check if it's a resource path
    if (soundPath.startsWith('resource://raw/')) {
      final fileName = soundPath.substring('resource://raw/'.length);
      // Remove extension for validation since soundFileExists expects filename without extension
      final fileNameWithoutExt = fileName.contains('.')
          ? fileName.substring(0, fileName.lastIndexOf('.'))
          : fileName;
      return soundFileExists(fileNameWithoutExt);
    }

    // Check if it's a file path
    if (soundPath.startsWith('/') || soundPath.contains('://')) {
      return File(soundPath).existsSync();
    }

    return false;
  }
}

/// Athan sound information model
class AthanSoundInfo {
  final AthanSoundType type;
  final String name;
  final String nameEn;
  final String fileName;

  const AthanSoundInfo({
    required this.type,
    required this.name,
    required this.nameEn,
    required this.fileName,
  });
}

/// Dhikr sound information model
class DhikrSoundInfo {
  final String name;
  final String nameEn;
  final String fileName;
  final String description;

  const DhikrSoundInfo({
    required this.name,
    required this.nameEn,
    required this.fileName,
    required this.description,
  });
}

/// Sound constants for awesome notifications
class NotificationSoundConstants {
  // Athan sounds
  static const String kAthan1 = 'athan1';
  static const String kAthan1Short = 'athan1_short';
  static const String kAthan2 = 'athan2';
  static const String kAthan2Short = 'athan2_short';
  static const String kAthan3 = 'athan3';
  static const String kAthan3Short = 'athan3_short';
  static const String kAthan4 = 'athan4';
  static const String kAthan4Short = 'athan4_short';
  static const String kBird = 'bird';
  static const String kIqama = 'iqama';
  static const String kPreAthan = 'pre_athan';
}
