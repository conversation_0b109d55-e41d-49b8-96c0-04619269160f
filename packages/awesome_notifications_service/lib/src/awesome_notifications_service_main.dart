import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:logger/logger.dart';

import 'constants/notification_constants.dart';
import 'models/athkar_notification.dart';
import 'models/prayer_notification.dart';
import 'services/athkar_notification_service.dart';
import 'services/awesome_notifications_manager.dart';
import 'services/firebase_notification_service.dart';
import 'services/permission_service.dart';
import 'services/prayer_notification_service.dart';
import 'utils/notification_utils.dart';

/// Main service class for awesome notifications
/// This is the primary interface for the main app to interact with the notification system
class AwesomeNotificationsService {
  static final AwesomeNotificationsService _instance =
      AwesomeNotificationsService._internal();
  factory AwesomeNotificationsService() => _instance;
  AwesomeNotificationsService._internal();

  final Logger _logger = Logger();

  // Core services
  final AwesomeNotificationsManager _notificationManager =
      AwesomeNotificationsManager();
  final PermissionService _permissionService = PermissionService();
  final PrayerNotificationService _prayerService = PrayerNotificationService();
  final AthkarNotificationService _athkarService = AthkarNotificationService();
  final FirebaseNotificationService _firebaseService =
      FirebaseNotificationService();

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// Initialize the notification service
  /// This should be called once during app startup
  static Future<bool> initialize() async {
    final instance = AwesomeNotificationsService();
    return await instance._initialize();
  }

  /// Internal initialization method
  Future<bool> _initialize() async {
    try {
      _logger.i('Initializing AwesomeNotificationsService');

      // Initialize core notification manager
      final managerInitialized = await _notificationManager.initialize();
      if (!managerInitialized) {
        _logger.e('Failed to initialize notification manager');
        return false;
      }

      // Initialize permission service
      await _permissionService.initialize();

      // Initialize Firebase service
      await _firebaseService.initialize();

      _isInitialized = true;
      _logger.i('AwesomeNotificationsService initialized successfully');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error initializing AwesomeNotificationsService',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Request all required permissions
  /// Returns true if all permissions are granted
  static Future<bool> requestPermissions() async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      _instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    return await instance._permissionService
        .requestAllPermissions()
        .then((result) => result.granted);
  }

  /// Check current permission status
  static Future<PermissionStatus> getPermissionStatus() async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      _instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    return await instance._permissionService.checkPermissionStatus();
  }

  /// Open notification settings
  static Future<void> openNotificationSettings() async {
    final instance = AwesomeNotificationsService();
    await instance._permissionService.openAppSettings();
  }

  /// Open exact alarm settings (Android only)
  static Future<void> openExactAlarmSettings() async {
    final instance = AwesomeNotificationsService();
    await instance._permissionService.openExactAlarmSettings();
  }

  /// Get explanation for exact alarm permission
  static String getExactAlarmPermissionExplanation() {
    final instance = AwesomeNotificationsService();
    return instance._permissionService.getExactAlarmPermissionExplanation();
  }

  // ==================== PRAYER NOTIFICATIONS ====================

  /// Schedule prayer notifications
  static Future<bool> schedulePrayerNotifications({
    required PrayerTimes prayerTimes,
    required Map<String, PrayerNotificationConfig> notificationConfigs,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      _instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    return await instance._prayerService.schedulePrayerNotifications(
      prayerTimes: prayerTimes,
      notificationConfigs: notificationConfigs,
      daysAhead: daysAhead,
      locale: locale,
    );
  }

  /// Cancel all prayer notifications
  static Future<bool> cancelAllPrayerNotifications() async {
    final instance = AwesomeNotificationsService();
    return await instance._prayerService.cancelAllPrayerNotifications();
  }

  /// Cancel notifications for specific prayer
  static Future<bool> cancelPrayerNotifications(String prayerName) async {
    final instance = AwesomeNotificationsService();
    return await instance._prayerService.cancelPrayerNotifications(prayerName);
  }

  /// Check if prayer notifications are scheduled
  static Future<bool> hasPrayerNotifications() async {
    final instance = AwesomeNotificationsService();
    return await instance._prayerService.hasPrayerNotifications();
  }

  /// Get count of scheduled prayer notifications (for debugging)
  static Future<int> getScheduledPrayerNotificationsCount() async {
    final instance = AwesomeNotificationsService();
    return await instance._prayerService.getScheduledPrayerNotificationsCount();
  }

  /// Force refresh prayer notifications (for debugging)
  static Future<bool> forceRefreshPrayerNotifications({
    required PrayerTimes prayerTimes,
    required Map<String, PrayerNotificationConfig> notificationConfigs,
    String locale = 'ar',
  }) async {
    final instance = AwesomeNotificationsService();

    // Force initialization if needed
    if (!instance._isInitialized) {
      await instance._initialize();
    }

    // Cancel all existing notifications first
    await instance._prayerService.cancelAllPrayerNotifications();

    // Wait a moment for cancellation to complete
    await Future.delayed(const Duration(milliseconds: 500));

    // Schedule new notifications
    return await instance._prayerService.schedulePrayerNotifications(
      prayerTimes: prayerTimes,
      notificationConfigs: notificationConfigs,
      daysAhead: 4,
      locale: locale,
    );
  }

  /// Update prayer notification configuration
  static Future<bool> updatePrayerNotificationConfig({
    required String prayerName,
    required PrayerNotificationConfig config,
    required PrayerTimes prayerTimes,
    String locale = 'ar',
  }) async {
    final instance = AwesomeNotificationsService();
    return await instance._prayerService.updatePrayerNotificationConfig(
      prayerName: prayerName,
      config: config,
      prayerTimes: prayerTimes,
      locale: locale,
    );
  }

  // ==================== ATHKAR NOTIFICATIONS ====================

  /// Schedule morning athkar notifications
  static Future<bool> scheduleMorningAthkar({
    required AthkarNotificationConfig config,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      _instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    return await instance._athkarService.scheduleMorningAthkar(
      config: config,
      daysAhead: daysAhead,
      locale: locale,
    );
  }

  /// Schedule evening athkar notifications
  static Future<bool> scheduleEveningAthkar({
    required AthkarNotificationConfig config,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      _instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    return await instance._athkarService.scheduleEveningAthkar(
      config: config,
      daysAhead: daysAhead,
      locale: locale,
    );
  }

  /// Schedule dhikr reminder notifications
  static Future<bool> scheduleDhikrReminders({
    required AthkarNotificationConfig config,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      _instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    return await instance._athkarService.scheduleDhikrReminders(
      config: config,
      daysAhead: daysAhead,
      locale: locale,
    );
  }

  /// Cancel athkar notifications by type
  static Future<bool> cancelAthkarNotifications(AthkarType type) async {
    final instance = AwesomeNotificationsService();
    return await instance._athkarService.cancelAthkarNotifications(type);
  }

  /// Cancel all athkar notifications
  static Future<bool> cancelAllAthkarNotifications() async {
    final instance = AwesomeNotificationsService();
    return await instance._athkarService.cancelAllAthkarNotifications();
  }

  /// Check if athkar notifications are scheduled
  static Future<bool> hasAthkarNotifications() async {
    final instance = AwesomeNotificationsService();
    return await instance._athkarService.hasAthkarNotifications();
  }

  /// Get scheduled athkar notifications count
  static Future<int> getScheduledAthkarNotificationsCount() async {
    final instance = AwesomeNotificationsService();
    return await instance._athkarService.getScheduledAthkarNotificationsCount();
  }

  // ==================== FIREBASE NOTIFICATIONS ====================

  /// Handle foreground Firebase message
  static Future<void> handleForegroundFirebaseMessage(
      Map<String, dynamic> message) async {
    final instance = AwesomeNotificationsService();
    await instance._firebaseService.handleForegroundMessage(message);
  }

  /// Handle background Firebase message
  static Future<void> handleBackgroundFirebaseMessage(
      Map<String, dynamic> message) async {
    await FirebaseNotificationService.handleBackgroundMessage(message);
  }

  /// Handle notification opened from Firebase
  static Future<void> handleFirebaseNotificationOpened(
      Map<String, dynamic> message) async {
    final instance = AwesomeNotificationsService();
    await instance._firebaseService.handleNotificationOpened(message);
  }

  /// Send custom notification
  static Future<bool> sendCustomNotification({
    required String title,
    required String body,
    Map<String, String>? data,
    String? imageUrl,
  }) async {
    final instance = AwesomeNotificationsService();
    return await instance._firebaseService.sendCustomNotification(
      title: title,
      body: body,
      data: data,
      imageUrl: imageUrl,
    );
  }

  // ==================== NAVIGATION METHODS ====================

  /// Set navigation callback for handling notification taps
  static void setNavigationCallback(
      Future<void> Function(ReceivedAction) callback) {
    AwesomeNotificationsManager.setNavigationCallback(callback);
  }

  /// Clear navigation callback
  static void clearNavigationCallback() {
    AwesomeNotificationsManager.clearNavigationCallback();
  }

  // ==================== GENERAL METHODS ====================

  /// Cancel all notifications
  static Future<bool> cancelAllNotifications() async {
    final instance = AwesomeNotificationsService();
    return await instance._notificationManager.cancelAllNotifications();
  }

  /// Cancel all notifications
  static Future<bool> cancelAllSchedulesNotifications() async {
    final instance = AwesomeNotificationsService();
    return await instance._notificationManager
        .cancelAllSchedulesNotifications();
  }

  /// Schedule a test notification (for testing purposes)
  static Future<bool> scheduleTestNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    String soundName = 'athan3',
  }) async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    try {
      // Create a unique channel key for this scheduled notification
      final channelKey =
          'scheduled_sound_channel_${DateTime.now().millisecondsSinceEpoch}';

      instance._logger.i('🔊 Scheduling notification with sound: $soundName');
      instance._logger.i('📢 Creating dedicated channel: $channelKey');

      // Create a channel with the specific sound
      await instance._notificationManager.awesomeNotifications.setChannel(
        NotificationChannel(
          channelKey: channelKey,
          channelName: 'Scheduled Sound Channel',
          channelDescription: 'Channel for scheduled notification with sound',
          playSound: true,
          soundSource: Platform.isAndroid
              ? 'resource://raw/$soundName'
              : '$soundName.wav',
          defaultRingtoneType: DefaultRingtoneType.Alarm,
          importance: NotificationImportance.Max,
          criticalAlerts: true,
        ),
      );

      // Create notification content
      final content = NotificationContent(
        id: 9999, // Test notification ID
        channelKey: channelKey, // Use the dedicated channel
        title: title,
        body: body,
        payload: {'type': 'test_notification', 'sound': soundName},
        category: NotificationCategory.Alarm,
        criticalAlert: true,
        wakeUpScreen: true,
        fullScreenIntent: true,
        customSound:
            Platform.isAndroid ? 'resource://raw/$soundName' : '$soundName.wav',
      );

      // Schedule the notification
      final success = await instance._notificationManager.awesomeNotifications
          .createNotification(
        content: content,
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
      );

      instance._logger.i(
          '💡 Test notification scheduled for ${scheduledTime.toLocal()}, result: $success');
      return success;
    } catch (e) {
      instance._logger.e('⛔ Error scheduling test notification', error: e);
      return false;
    }
  }

  /// Schedule a test prayer notification (for testing prayer notification flow)
  static Future<bool> scheduleTestPrayerNotification({
    required String prayerName,
    required DateTime scheduledTime,
    String locale = 'ar',
    String soundName = 'athan3',
  }) async {
    final instance = AwesomeNotificationsService();
    if (!instance._isInitialized) {
      instance._logger.w('Service not initialized, initializing now...');
      await instance._initialize();
    }

    try {
      // Create a unique channel key for this test prayer notification
      final channelKey =
          'prayer_sound_channel_${DateTime.now().millisecondsSinceEpoch}';

      instance._logger
          .i('🔊 Scheduling prayer notification with sound: $soundName');
      instance._logger.i('📢 Creating dedicated prayer channel: $channelKey');

      // Create a channel with the specific sound
      await instance._notificationManager.awesomeNotifications.setChannel(
        NotificationChannel(
          channelKey: channelKey,
          channelName: 'Prayer Sound Channel',
          channelDescription: 'Channel for prayer notification with sound',
          playSound: true,
          soundSource: Platform.isAndroid
              ? 'resource://raw/$soundName'
              : '$soundName.wav',
          defaultRingtoneType: DefaultRingtoneType.Alarm,
          importance: NotificationImportance.Max,
          criticalAlerts: true,
        ),
      );

      // Create a test prayer notification using the prayer notification service
      final localizedPrayerName =
          NotificationUtils.getLocalizedPrayerName(prayerName, locale: locale);
      final localizedBody = NotificationUtils.getLocalizedNotificationBody(
        NotificationConstants.prayerTime,
        prayerName,
        locale: locale,
      );

      // Create notification content with explicit sound configuration
      final content = NotificationContent(
        id: 8888, // Test prayer notification ID
        channelKey: channelKey, // Use the dedicated channel
        title: localizedPrayerName,
        body: localizedBody,
        payload: {
          'payload': NotificationConstants.athanNotificationsPayload,
          'prayer_name': prayerName,
          'prayer_type': 'main',
          'sound': soundName
        },
        category: NotificationCategory.Alarm,
        criticalAlert: true,
        wakeUpScreen: true,
        fullScreenIntent: true,
        customSound:
            Platform.isAndroid ? 'resource://raw/$soundName' : '$soundName.wav',
      );

      // Convert action buttons
      final actionButtons =
          NotificationUtils.createDefaultActionButtons(locale: locale)
              .map((button) => NotificationActionButton(
                    key: button.key,
                    label: button.label,
                    requireInputText: false,
                    autoDismissible: button.autoDismissible,
                    isDangerousOption: button.isDangerousOption,
                  ))
              .toList();

      // Schedule the notification directly
      final success = await instance._notificationManager.awesomeNotifications
          .createNotification(
        content: content,
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
        actionButtons: actionButtons,
      );

      instance._logger.i(
          '🕌 Test prayer notification scheduled for ${scheduledTime.toLocal()}, result: $success');
      return success;
    } catch (e) {
      instance._logger
          .e('⛔ Error scheduling test prayer notification', error: e);
      return false;
    }
  }

  /// Get total scheduled notifications count
  static Future<int> getTotalScheduledNotificationsCount() async {
    final instance = AwesomeNotificationsService();
    final notifications =
        await instance._notificationManager.getScheduledNotifications();
    return notifications.length;
  }

  /// Check if any notifications are scheduled
  static Future<bool> hasAnyNotifications() async {
    final count = await getTotalScheduledNotificationsCount();
    return count > 0;
  }

  /// Get notification statistics
  static Future<NotificationStatistics> getNotificationStatistics() async {
    final instance = AwesomeNotificationsService();

    final prayerCount =
        await instance._prayerService.getScheduledPrayerNotificationsCount();
    final athkarCount =
        await instance._athkarService.getScheduledAthkarNotificationsCount();
    final firebaseCount = await instance._firebaseService
        .getScheduledFirebaseNotificationsCount();
    final totalCount = await getTotalScheduledNotificationsCount();

    return NotificationStatistics(
      totalNotifications: totalCount,
      prayerNotifications: prayerCount,
      athkarNotifications: athkarCount,
      firebaseNotifications: firebaseCount,
    );
  }

  /// Diagnostic method to check notification system health
  static Future<NotificationDiagnostics> runDiagnostics() async {
    final instance = AwesomeNotificationsService();

    try {
      // Check initialization
      final isInitialized = instance._isInitialized;

      // Check permissions
      final permissionStatus =
          await instance._permissionService.checkPermissionStatus();

      // Get notification counts
      final stats = await getNotificationStatistics();

      // Check if awesome notifications is working through the manager
      bool isAwesomeNotificationsAllowed = false;
      try {
        final managerStatus =
            await instance._notificationManager.getPermissionStatus();
        isAwesomeNotificationsAllowed = managerStatus.hasNotificationPermission;
      } catch (e) {
        // AwesomeNotifications might not be initialized
        isAwesomeNotificationsAllowed = false;
      }

      return NotificationDiagnostics(
        isServiceInitialized: isInitialized,
        hasNotificationPermission: permissionStatus.hasNotificationPermission,
        hasExactAlarmPermission: permissionStatus.hasExactAlarmPermission,
        hasCriticalAlertPermission: permissionStatus.hasCriticalAlertPermission,
        isAwesomeNotificationsAllowed: isAwesomeNotificationsAllowed,
        statistics: stats,
        message:
            _generateDiagnosticMessage(isInitialized, permissionStatus, stats),
      );
    } catch (e) {
      return NotificationDiagnostics(
        isServiceInitialized: false,
        hasNotificationPermission: false,
        hasExactAlarmPermission: false,
        hasCriticalAlertPermission: false,
        isAwesomeNotificationsAllowed: false,
        statistics: const NotificationStatistics(
          totalNotifications: 0,
          prayerNotifications: 0,
          athkarNotifications: 0,
          firebaseNotifications: 0,
        ),
        message: 'Error running diagnostics: $e',
        error: e.toString(),
      );
    }
  }

  static String _generateDiagnosticMessage(
    bool isInitialized,
    PermissionStatus permissionStatus,
    NotificationStatistics stats,
  ) {
    final issues = <String>[];

    if (!isInitialized) issues.add('Service not initialized');
    if (!permissionStatus.hasNotificationPermission) {
      issues.add('Missing notification permission');
    }
    if (!permissionStatus.hasExactAlarmPermission) {
      issues.add('Missing exact alarm permission');
    }
    if (stats.totalNotifications == 0) issues.add('No notifications scheduled');

    if (issues.isEmpty) {
      return 'All systems operational. ${stats.totalNotifications} notifications scheduled.';
    } else {
      return 'Issues found: ${issues.join(', ')}';
    }
  }

  /// Dispose the service
  static void dispose() {
    final instance = AwesomeNotificationsService();
    instance._notificationManager.dispose();
    instance._isInitialized = false;
    instance._logger.d('AwesomeNotificationsService disposed');
  }
}

/// Notification statistics model
class NotificationStatistics {
  final int totalNotifications;
  final int prayerNotifications;
  final int athkarNotifications;
  final int firebaseNotifications;

  const NotificationStatistics({
    required this.totalNotifications,
    required this.prayerNotifications,
    required this.athkarNotifications,
    required this.firebaseNotifications,
  });

  @override
  String toString() {
    return 'NotificationStatistics(total: $totalNotifications, prayer: $prayerNotifications, athkar: $athkarNotifications, firebase: $firebaseNotifications)';
  }
}

/// Notification diagnostics model
class NotificationDiagnostics {
  final bool isServiceInitialized;
  final bool hasNotificationPermission;
  final bool hasExactAlarmPermission;
  final bool hasCriticalAlertPermission;
  final bool isAwesomeNotificationsAllowed;
  final NotificationStatistics statistics;
  final String message;
  final String? error;

  const NotificationDiagnostics({
    required this.isServiceInitialized,
    required this.hasNotificationPermission,
    required this.hasExactAlarmPermission,
    required this.hasCriticalAlertPermission,
    required this.isAwesomeNotificationsAllowed,
    required this.statistics,
    required this.message,
    this.error,
  });

  bool get isHealthy =>
      isServiceInitialized &&
      hasNotificationPermission &&
      isAwesomeNotificationsAllowed &&
      statistics.totalNotifications > 0;

  @override
  String toString() {
    return 'NotificationDiagnostics(healthy: $isHealthy, message: $message, stats: $statistics)';
  }
}
