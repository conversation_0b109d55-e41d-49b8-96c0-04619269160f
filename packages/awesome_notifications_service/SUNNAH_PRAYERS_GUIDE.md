# Sunnah Prayer Notifications Guide

This guide explains how to use the enhanced PrayerNotificationService to schedule notifications for both obligatory prayers and Sunnah prayers.

## Overview

The PrayerNotificationService now supports scheduling notifications for:

### Obligatory Prayers (Full Features)
- **Fajr, <PERSON>, <PERSON><PERSON><PERSON>, Asr, Magh<PERSON>b, Isha**
- ✅ Main prayer notifications
- ✅ Pre-prayer warnings (15 minutes before)
- ✅ Iqamah notifications (for congregational prayers)
- ✅ Custom Athan sounds
- ✅ All notification types and customizations

### Sunnah Prayers (Simplified)
- **Middle Of The Night (منتصف الليل)**
- **Last Third Of The Night (الثلث الأخير من الليل)**
- ✅ Main prayer notifications only
- ❌ No pre-prayer warnings (not needed for optional prayers)
- ❌ No Iqamah notifications (individual prayers)
- ✅ Always uses default notification sound (gentle reminder)

## Usage Example

```dart
import 'package:awesome_notifications_service/awesome_notifications_service.dart';

Future<void> scheduleAllPrayerNotifications() async {
  // Define prayer times including Sunnah prayers
  final prayerTimes = PrayerTimes(
    fajr: DateTime(2025, 6, 25, 5, 30),
    sunrise: DateTime(2025, 6, 25, 6, 45),
    dhuhr: DateTime(2025, 6, 25, 12, 30),
    asr: DateTime(2025, 6, 25, 15, 45),
    maghrib: DateTime(2025, 6, 25, 18, 20),
    isha: DateTime(2025, 6, 25, 19, 45),
    // Sunnah prayer times
    middleOfNight: DateTime(2025, 6, 25, 23, 30),
    lastThirdOfNight: DateTime(2025, 6, 26, 2, 30),
  );

  // Configure notifications for all prayers
  final notificationConfigs = <String, PrayerNotificationConfig>{
    // Obligatory prayers with full features
    NotificationConstants.fajr: PrayerNotificationConfig(
      prayerName: NotificationConstants.fajr,
      isNotified: true,
      isPreNotified: true,
      iqamahMinutes: 15,
      soundIndex: 0, // Custom Athan sound
      useDefaultSound: false,
    ),
    
    NotificationConstants.dhuhr: PrayerNotificationConfig(
      prayerName: NotificationConstants.dhuhr,
      isNotified: true,
      isPreNotified: true,
      iqamahMinutes: 10,
      soundIndex: 1,
      useDefaultSound: false,
    ),
    
    // ... other obligatory prayers ...
    
    // Sunnah prayers with simplified configuration
    NotificationConstants.middleOfTheNight: PrayerNotificationConfig(
      prayerName: NotificationConstants.middleOfTheNight,
      isNotified: true,
      // Note: isPreNotified and iqamahMinutes will be ignored
      // useDefaultSound will be overridden to true
      isPreNotified: false, // Not used
      iqamahMinutes: null,  // Not used
      soundIndex: 2,        // Will be overridden
      useDefaultSound: false, // Will be overridden to true
    ),
    
    NotificationConstants.lastThirdOfTheNight: PrayerNotificationConfig(
      prayerName: NotificationConstants.lastThirdOfTheNight,
      isNotified: true,
      // Simplified configuration for Sunnah prayers
    ),
  };

  // Schedule all notifications
  final success = await AwesomeNotificationsService.schedulePrayerNotifications(
    prayerTimes: prayerTimes,
    notificationConfigs: notificationConfigs,
    daysAhead: 7,
    locale: 'ar', // or 'en'
  );

  if (success) {
    print('✅ All prayer notifications scheduled successfully');
  } else {
    print('❌ Failed to schedule prayer notifications');
  }
}
```

## Key Differences

### Obligatory Prayer Notifications
- **Sound**: Custom Athan sounds based on `soundIndex`
- **Pre-notifications**: 15-minute warnings available
- **Iqamah**: Congregation call notifications available
- **Customization**: Full control over all settings

### Sunnah Prayer Notifications
- **Sound**: Always uses default system notification sound
- **Pre-notifications**: Not available (automatically disabled)
- **Iqamah**: Not available (automatically disabled)
- **Customization**: Simplified to `isNotified` only

## Available Sunnah Prayers

```dart
// Access Sunnah prayer constants
NotificationConstants.middleOfTheNight      // "Middle Of The Night"
NotificationConstants.lastThirdOfTheNight   // "Last Third Of The Night"

// Get all prayers (obligatory + Sunnah)
final allPrayers = NotificationConstants.allPrayers;
// Returns: [Fajr, Sunrise, Dhuhr, Asr, Maghrib, Isha, Middle Of The Night, Last Third Of The Night]
```

## Localization

Both Arabic and English are supported:

```dart
// Arabic
NotificationUtils.getLocalizedPrayerName(
  NotificationConstants.middleOfTheNight, 
  locale: 'ar'
); // Returns: "منتصف الليل"

// English  
NotificationUtils.getLocalizedPrayerName(
  NotificationConstants.lastThirdOfTheNight, 
  locale: 'en'
); // Returns: "Last Third Of The Night"
```

## Best Practices

1. **Always enable Sunnah prayers**: Set `isNotified: true` for users who want optional prayer reminders
2. **Don't configure pre-notifications**: They will be ignored for Sunnah prayers
3. **Don't configure Iqamah**: Not applicable for individual Sunnah prayers
4. **Use appropriate timing**: Calculate night prayer times based on local sunset/sunrise
5. **Respect user preferences**: Allow users to enable/disable Sunnah prayer notifications independently

## Testing

Run the comprehensive test suite:

```bash
cd packages/awesome_notifications_service
flutter test test/sunnah_prayer_test.dart
```

This ensures all Sunnah prayer functionality works correctly with proper localization and notification generation.
