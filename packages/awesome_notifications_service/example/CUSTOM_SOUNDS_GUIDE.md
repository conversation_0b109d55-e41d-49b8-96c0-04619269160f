# Custom Notification Sounds Guide

This guide demonstrates how to use custom notification sounds with the `awesome_notifications_service` package in Flutter applications.

## Overview

The `awesome_notifications_service` package supports custom notification sounds through multiple approaches:

1. **Asset-based sounds** - Sound files stored in Flutter assets
2. **Resource-based sounds** - Sound files stored in native platform resources
3. **Predefined sounds** - Built-in sounds provided by the package

## Asset Configuration

### 1. Adding Sound Files to Assets

Place your sound files in the `assets/sounds/` directory:

```
assets/
  sounds/
    bird.mp3
    custom_notification.mp3
    custom_athan.wav
```

### 2. Registering Assets in pubspec.yaml

Ensure your `pubspec.yaml` includes the assets directory:

```yaml
flutter:
  uses-material-design: true
  assets:
    - assets/sounds/
```

## Supported Audio Formats

The package supports various audio formats:

- **MP3** (.mp3) - Most common, good compression
- **WAV** (.wav) - Uncompressed, high quality
- **OGG** (.ogg) - Open source format
- **M4A** (.m4a) - Apple's format

## Implementation Examples

### 1. Prayer Notifications with Custom Sounds

```dart
Future<void> testCustomSoundPrayerNotification() async {
  final now = DateTime.now();
  final testTime = now.add(const Duration(seconds: 5));
  
  final prayerTimes = PrayerTimes(
    fajr: testTime,
    sunrise: testTime.add(const Duration(minutes: 1)),
    dhuhr: testTime.add(const Duration(minutes: 2)),
    asr: testTime.add(const Duration(minutes: 3)),
    maghrib: testTime.add(const Duration(minutes: 4)),
    isha: testTime.add(const Duration(minutes: 5)),
  );

  // Configure notification with custom sound
  final notificationConfigs = {
    'Fajr': PrayerNotificationConfig(
      prayerName: 'Fajr',
      isNotified: true,
      isPreNotified: false,
      soundIndex: 4, // Bird sound index - uses assets/sounds/bird.mp3
      useDefaultSound: false, // Important: set to false for custom sounds
    ),
  };

  final success = await AwesomeNotificationsService.schedulePrayerNotifications(
    prayerTimes: prayerTimes,
    notificationConfigs: notificationConfigs,
    daysAhead: 1,
    locale: 'en',
  );
}
```

### 2. Sound Index Reference

The package uses sound indices to reference different sounds:

- **Index 0**: Athan1 (default athan sound)
- **Index 1**: Athan2 
- **Index 2**: Athan3
- **Index 3**: Athan4
- **Index 4**: Bird sound (from assets/sounds/bird.mp3)

### 3. Resource Path Format

The package internally converts asset references to resource paths:

```dart
// Asset file: assets/sounds/bird.mp3
// Internal resource path: resource://raw/bird
```

## Testing Custom Sounds

### Running the Test

1. Open the example app
2. Grant notification permissions
3. Tap "Test Custom Sound (5s)" button
4. Wait 5 seconds for the notification with custom bird sound

### Test Implementation

The test function demonstrates:

```dart
Future<void> _testCustomSoundNotifications() async {
  try {
    final now = DateTime.now();
    
    // Test: Prayer notification with custom bird sound (asset-based)
    final testTime1 = now.add(const Duration(seconds: 5));
    final prayerTimes = PrayerTimes(
      fajr: testTime1,
      // ... other prayer times
    );

    // Configure notification with custom sound (bird sound from assets)
    final notificationConfigs = {
      'Fajr': PrayerNotificationConfig(
        prayerName: 'Fajr',
        isNotified: true,
        isPreNotified: false,
        soundIndex: 4, // Bird sound index - uses assets/sounds/bird.mp3
        useDefaultSound: false,
      ),
    };

    final success = await AwesomeNotificationsService.schedulePrayerNotifications(
      prayerTimes: prayerTimes,
      notificationConfigs: notificationConfigs,
      daysAhead: 1,
      locale: 'en',
    );

    // Show result to user
    _showResult('Custom Sound Prayer Test', success,
        'Custom bird sound notification will appear in 5 seconds');
  } catch (e) {
    _showError('Custom Sound Test', e.toString());
  }
}
```

## Best Practices

### 1. File Size Optimization

- Keep sound files small (< 1MB) for better performance
- Use compressed formats like MP3 for longer sounds
- Use WAV for short, high-quality sounds

### 2. Platform Considerations

- **Android**: Supports all common audio formats
- **iOS**: Prefers M4A, WAV, and MP3 formats
- Test on both platforms to ensure compatibility

### 3. Sound Duration

- **Notification sounds**: 2-10 seconds recommended
- **Athan sounds**: Can be longer (1-3 minutes)
- **Alert sounds**: Keep under 5 seconds

### 4. Volume and Quality

- Normalize audio levels across all sound files
- Test sounds at different device volume levels
- Consider accessibility requirements

## Troubleshooting

### Common Issues

1. **Sound not playing**: Check file format and asset registration
2. **Default sound plays instead**: Ensure `useDefaultSound: false`
3. **File not found**: Verify asset path and pubspec.yaml configuration

### Debug Tips

```dart
// Enable debug logging to see sound path resolution
print('Sound index: ${config.soundIndex}');
print('Use default sound: ${config.useDefaultSound}');
```

## Advanced Usage

### Custom Sound Utilities

The package provides utilities for sound management:

```dart
// Check if sound file exists
bool exists = SoundUtils.soundFileExists('bird');

// Get sound duration estimate
Duration duration = SoundUtils.getSoundDuration('bird');

// Validate sound path format
bool isValid = SoundUtils.isValidSoundPath('resource://raw/bird');
```

## Integration with Tests

The example includes comprehensive tests in `test/widget_test.dart`:

- Sound path validation tests
- Configuration validation tests
- Asset management tests
- UI integration tests

Run tests with:
```bash
flutter test
```

## Conclusion

Custom notification sounds enhance user experience by providing:

- **Personalization**: Users can identify notification types by sound
- **Cultural relevance**: Use appropriate sounds for different regions
- **Brand consistency**: Maintain audio branding across notifications

Follow this guide to implement custom notification sounds effectively in your Flutter applications using the `awesome_notifications_service` package.
