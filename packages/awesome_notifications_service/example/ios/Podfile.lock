PODS:
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_timezone (0.0.1):
    - Flutter
  - IosAwnCore (0.10.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter

DEPENDENCIES:
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)

SPEC REPOS:
  trunk:
    - IosAwnCore

EXTERNAL SOURCES:
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"

SPEC CHECKSUMS:
  awesome_notifications: 0f432b28098d193920b11a44cfa9d2d9313a3888
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_timezone: 7c838e17ffd4645d261e87037e5bebf6d38fe544
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d

PODFILE CHECKSUM: 348faa674fa9662d9ce8bea1f3db14d67055fdb6

COCOAPODS: 1.16.2
