import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_locale.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/custom_animated_dropdown.dart';
import 'package:salawati/features/tasbeeh/presentation/controller/tasbeeh_controller.dart';

class TasbeehDropDown extends StatelessWidget {
  const TasbeehDropDown({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => CustomAnimatedDropdown<int>(
        key: Key(
            "${TasabeehController.tasbeehat.length}tasbeeh${TasabeehController.selectedTasbeehIndex.value}"),
        items: List.generate(TasabeehController.tasbeehat.length, (index) {
          return index;
        }),
        expandedHeaderPadding:
            EdgeInsets.symmetric(vertical: 4.h, horizontal: 16.w),
        closedFillColor: Colors.transparent,
        initialItem: TasabeehController.selectedTasbeehIndex.value,
        headerBuilder: (context, item, enabled) =>
            GetBuilder<AppLocale>(builder: (controller) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                TasabeehController.tasbeehat[item].title,
                style: TextStyle(
                  color: AppColor.kOrangeColor,
                  fontSize: 17.sp,
                ),
              ),
            ],
          );
        }),
        listItemBuilder: (context, value, isSelected, onTap) =>
            GetBuilder<AppLocale>(builder: (controller) {
          return Obx(() => Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomText(
                    TasabeehController.tasbeehat[value].title,
                    style: const TextStyle(
                      color: AppColor.kBlackColor,
                    ),
                  ),
                  if (TasabeehController.tasbeehat[value].canBeDeleted) ...[
                    const Spacer(),
                    FilledButton(
                      style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            vertical: 4,
                            horizontal: 0,
                          ),
                          backgroundColor: Colors.red,
                          textStyle: const TextStyle(
                            fontSize: 11,
                          )),
                      onPressed: () {
                        Get.find<TasabeehController>().deleteTasbeeh(value);
                      },
                      child: CustomText(
                        "Delete".tr,
                      ),
                    ),
                  ]
                ],
              ));
        }),
        hintText: 'Select tasbeeh',
        hintBuilder: (context, hint, enabled) => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              hint,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18.sp,
              ),
            ),
          ],
        ),
        onChanged: (value) {
          Get.find<TasabeehController>().changeSelectedTasbeehIndex(value ?? 0);
        },
      ),
    );
  }
}
