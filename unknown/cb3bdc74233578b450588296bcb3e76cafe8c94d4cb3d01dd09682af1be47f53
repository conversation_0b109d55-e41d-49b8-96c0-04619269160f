import 'dart:async';

import 'package:salawati/core/data/data_service.dart';
import 'package:salawati/core/data/data_state.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/mosque/data/models/mosque_model.dart';

class MosqueRepo2 {
  static Future<DataState<MosquesModel>> getNearMosques(
      double lat, double long) async {
    String language = (cacheMemory.read('lang') ?? 'ar');
    final response = await DataService.get(
        url: 'https://maps.googleapis.com/maps/api/place/nearbysearch/json',
        queryParameters: {
          'language': language == 'en' ? 'en' : language,
          'location': '$lat,$long',
          'radius': 5000,
          'type': 'mosque',
          // todo: remove maps on testing
          'key': GOOGLE_API_KEY,
        });

    return DataService.dataRepoRequest(
        response: response, fromJson: MosquesModel.fromJson);
  }
}
