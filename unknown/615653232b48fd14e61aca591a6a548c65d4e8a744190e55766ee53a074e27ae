import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';

class KaabaDistanceText extends StatelessWidget {
  const KaabaDistanceText({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: FittedBox(
        alignment: AlignmentDirectional.centerStart,
        fit: BoxFit.scaleDown,
        child: Row(
          children: [
            CustomText(
              'The distance to the Kaaba is',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            8.horizontalSpace,
            GetBuilder<PrayerController>(builder: (_) {
              double kaabaDistance =
                  Get.find<PrayerController>().getKaabaDistance();
              return CustomText(
                "${NumberLocalization.localizeNumber(kaabaDistance > 1000 ? kaabaDistance / 1000 : kaabaDistance)} ${kaabaDistance > 1000 ? 'KM'.tr : 'M'}",
                style: TextStyle(
                  color: AppColor.kOrangeColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 17.sp,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
