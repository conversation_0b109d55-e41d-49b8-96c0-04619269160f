import 'package:flutter/foundation.dart';

class CitiesModel {
  List<City> cties;
  CitiesModel({required this.cties});

  factory CitiesModel.fromJson(List ctiesList) => CitiesModel(
        cties: ctiesList.map((x) => City.fromJson(x)).toList(),
      );
}

class City {
  final String city;
  final String country;
  final String? placeId;
  City({
    required this.city,
    required this.country,
    required this.placeId,
  });

  factory City.fromJson(Map<String, dynamic> json) {
    return City(
      city: json['structured_formatting']['main_text'],
      country: json['structured_formatting']['secondary_text'],
      placeId: json['place_id'],
    );
  }
}

class LocalCitiesModel {
  List<LocalCity> cities;
  LocalCitiesModel({required this.cities});

  factory LocalCitiesModel.fromJson(List localCtiesList) => LocalCitiesModel(
        cities: localCtiesList.map((x) => LocalCity.fromJson(x)).toList(),
      );
}

class LocalCity {
  final String arabicName;
  static const String arabicNameKey = "arabic_name";

  final String asciiName;
  static const String asciiNameKey = "ascii_name";

  final String englishName;
  static const String englishNameKey = "name";

  final int geonameid;
  static const String geonameidKey = "geonameid";

  final int id;
  static const String idKey = "id";

  final double latitude;
  static const String latitudeKey = "latitude";

  final double longitude;
  static const String longitudeKey = "longitude";

  final int countryId;
  static const String countryIdKey = "country_id";

  final int isParent;
  static const String isParentKey = "is_parent";
  Country? country;
  static const String countryKey = "country";
  final String countryName;
  static const String countryNameKey = "country_name";
  final String countryArabicName;
  static const String countryArabicNameKey = "country_arabic_name";
  LocalCity({
    required this.arabicName,
    required this.asciiName,
    required this.englishName,
    required this.geonameid,
    required this.id,
    required this.latitude,
    required this.longitude,
    required this.countryId,
    required this.isParent,
    required this.country,
    required this.countryName,
    required this.countryArabicName,
  });

  LocalCity copyWith({
    String? arabicName,
    String? asciiName,
    String? englishName,
    String? frenchName,
    int? geonameid,
    int? id,
    double? latitude,
    double? longitude,
    dynamic urduName,
    int? countryId,
    int? isParent,
    String? countryName,
    String? countryArabicName,
  }) {
    return LocalCity(
      arabicName: arabicName ?? this.arabicName,
      asciiName: asciiName ?? this.asciiName,
      englishName: englishName ?? this.englishName,
      geonameid: geonameid ?? this.geonameid,
      id: id ?? this.id,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      countryId: countryId ?? this.countryId,
      isParent: isParent ?? this.isParent,
      country: country,
      countryName: countryName ?? this.countryName,
      countryArabicName: countryArabicName ?? this.countryArabicName,
    );
  }

  factory LocalCity.fromJson(Map<String, dynamic> json) {
    return LocalCity(
      id: json['_id'] ?? 0,
      asciiName: json['ascii_name'] ?? '',
      englishName: json['name'] ?? '',
      arabicName: json['arabic_name'] ?? '',
      latitude: _parseCoordinate(json['latitude']),
      longitude: _parseCoordinate(json['longitude']),
      geonameid: json['geonameid'] ?? 0,
      countryId: json['country_id'] ?? 0,
      isParent: json['is_parent'] ?? 0,
      country:
          json['country'] != null ? Country.fromJson(json['country']) : null,
      countryName: json['country_name'] ?? '',
      countryArabicName: json['country_arabic_name'] ?? '',
    );
  }

  static double _parseCoordinate(dynamic value) {
    if (value == null) return 0.0;

    // Handle double values directly from SQLite
    if (value is double) return value;

    // Handle int to double conversion
    if (value is int) return value.toDouble();

    // Handle string representations
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        debugPrint('Coordinate parsing error: $e');
        return 0.0;
      }
    }

    return 0.0;
  }

  Map<String, dynamic> toJson() => {
        "arabic_name": arabicName,
        "ascii_name": asciiName,
        "name": englishName,
        "geonameid": geonameid,
        "id": id,
        "latitude": latitude,
        "longitude": longitude,
        "country_id": countryId,
        'is_parent': isParent,
        'country': country,
        countryName: countryName,
        countryArabicName: countryArabicName,
      };

  @override
  String toString() {
    return "$arabicName, $asciiName, $englishName, $geonameid, $id, $latitude, $longitude, $countryId,$isParent,$country,$countryName,$countryArabicName";
  }
}

class Country {
  Country({
    required this.arabicName,
    this.calculationMethod,
    this.countryId,
    this.daylightSaving,
    required this.name,
  });

  final String arabicName;
  static const String arabicNameKey = "country_arabic_name";

  final int? calculationMethod;
  static const String calculationMethodKey = "calculation_method";

  final int? countryId;
  static const String countryIdKey = "country_id";

  final int? daylightSaving;
  static const String daylightSavingKey = "daylight_saving";

  final String name;
  static const String nameKey = "name";

  Country copyWith({
    String? arabicName,
    int? calculationMethod,
    int? countryId,
    int? daylightSaving,
    String? name,
  }) {
    return Country(
      arabicName: arabicName ?? this.arabicName,
      calculationMethod: calculationMethod ?? this.calculationMethod,
      countryId: countryId ?? this.countryId,
      daylightSaving: daylightSaving ?? this.daylightSaving,
      name: name ?? this.name,
    );
  }

  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      arabicName: json["country_arabic_name"] ?? "",
      calculationMethod: json["calculation_method"] ?? 0,
      countryId: json["country_id"] ?? 0,
      daylightSaving: json["daylight_saving"] ?? 0,
      name: json["name"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "country_arabic_name": arabicName,
        "calculation_method": calculationMethod,
        "country_id": countryId,
        "daylight_saving": daylightSaving,
        "name": name,
      };

  @override
  String toString() {
    return "$arabicName, $calculationMethod, $countryId, $daylightSaving, $name, ";
  }
}
