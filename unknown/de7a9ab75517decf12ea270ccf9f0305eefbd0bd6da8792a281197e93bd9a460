import 'package:adhan/adhan.dart';
import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:instagram_page_indicator/instagram_page_indicator.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/prayer/presentation/widgets/prayer_grid_tile.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

import '../../../../core/utils/app_functions.dart';

class PrayersGridView extends StatelessWidget {
  const PrayersGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX<PrayerController>(
      builder: (controller) {
        if (controller.prayerTimes.value == null) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final prayerTimes = controller.prayerTimes.value!;
        final prayers = {
          FAJR: prayerTimes.fajr,
          SUNRISE: prayerTimes.sunrise,
          DHUHR: prayerTimes.dhuhr,
          ASR: prayerTimes.asr,
          MAGHRIB: prayerTimes.maghrib,
          ISHA: prayerTimes.isha,
        };

        Prayer calculatedNextPrayer = AppFunctions.nextPrayer(prayerTimes);
        final sunnahTimes = SunnahTimes(prayerTimes);
        final sunnahPrayers = {
          MIDDLEOFTHENIGHT: sunnahTimes.middleOfTheNight,
          LASTTHIRDOFTHENIGHT: sunnahTimes.lastThirdOfTheNight,
        };
        SunnahPrayer calculatedNextSunnahPrayer =
            AppFunctions.nextPrayerForSunnah(prayerTimes, sunnahTimes);

        return Column(
          children: [
            ExpandablePageView.builder(
              itemCount: 1000,
              controller: controller.prayersPageController,
              onPageChanged: (value) => controller.changePrayerDatePage(
                value - PrayerController.initialPrayerPage,
              ),
              itemBuilder: (context, index) {
                // 1) compute both maps as before
                final prayers = <String, DateTime>{
                  FAJR: prayerTimes.fajr,
                  SUNRISE: prayerTimes.sunrise,
                  DHUHR: prayerTimes.dhuhr,
                  ASR: prayerTimes.asr,
                  MAGHRIB: prayerTimes.maghrib,
                  ISHA: prayerTimes.isha,
                };
                final sunnahPrayers = <String, DateTime>{
                  MIDDLEOFTHENIGHT: sunnahTimes.middleOfTheNight,
                  LASTTHIRDOFTHENIGHT: sunnahTimes.lastThirdOfTheNight,
                };

                return Column(
                  children: [
                    // ─── Regular prayers: ~3 per row ───────────────────
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: GridView.builder(
                        padding: EdgeInsets.only(bottom: 10),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: prayers.length,
                        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                          maxCrossAxisExtent: 120
                              .w, // ~3 items per row on phone :contentReference[oaicite:3]{index=3}
                          crossAxisSpacing: 8.w,
                          mainAxisSpacing: 8.w,
                        ),
                        itemBuilder: (ctx, i) {
                          final name = prayers.keys.elementAt(i);
                          return Obx(() => PrayerGridTile(
                                prayerName: name,
                                prayerTime: prayers[name]!,
                                isNotified: SettingsController
                                    .instance
                                    .athanNotificationsMap
                                    .value[name]!
                                    .isNotified,
                                isNextPrayer: name ==
                                    calculatedNextPrayer.name.capitalizeFirst,
                              ));
                        },
                      ),
                    ),

                    // ─── Sunnah prayers: exactly 2 per row ────────────
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: GridView.builder(
                        shrinkWrap: true,
                        padding: EdgeInsets.all(0),
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: sunnahPrayers.length,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 8.w,
                          mainAxisSpacing: 8.w,
                          childAspectRatio: 2.5,
                        ),
                        itemBuilder: (BuildContext context, int j) {
                          final name = sunnahPrayers.keys.elementAt(j);
                          return Obx(() => PrayerSunnahGridTile(
                                prayerName: name,
                                prayerTime: sunnahPrayers[name]!,
                                isNotified: SettingsController
                                    .instance
                                    .athanNotificationsMap
                                    .value[name]!
                                    .isNotified,
                                isNextPrayer: name ==
                                    calculatedNextSunnahPrayer
                                        .name.capitalizeFirst,
                              ));
                        },
                      ),
                    ),
                  ],
                );
              },
            ),

            // ExpandablePageView.builder(
            //   itemCount: 1000,
            //   controller: controller.prayersPageController,
            //   onPageChanged: (value) => controller.changePrayerDatePage(
            //     value - PrayerController.initialPrayerPage,
            //   ),
            //   itemBuilder: (context, index) {
            //     return Column(
            //       children: [
            //         Padding(
            //           padding: const EdgeInsets.symmetric(horizontal: 8),
            //           child: GridView.builder(
            //             shrinkWrap: true,
            //             physics: const NeverScrollableScrollPhysics(),
            //             itemCount: prayers.length,
            //             gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
            //               maxCrossAxisExtent: 120.w,
            //               crossAxisSpacing: 8.w,
            //               mainAxisSpacing: 8.w,
            //             ),
            //             itemBuilder: (BuildContext context, int index) {
            //               final prayerName = prayers.keys.toList()[index];

            //               return Obx(() => PrayerGridTile(
            //                     prayerName: prayerName,
            //                     prayerTime: prayers[prayerName]!,
            //                     isNotified: SettingsController
            //                         .instance
            //                         .athanNotificationsMap
            //                         // ignore: invalid_use_of_protected_member
            //                         .value[prayerName]!
            //                         .isNotified,
            //                     isNextPrayer: prayerName ==
            //                         calculatedNextPrayer.name.capitalizeFirst,
            //                   ));
            //             },
            //           ),
            //         ),
            //         Padding(
            //           padding: const EdgeInsets.symmetric(
            //               horizontal: 8, vertical: 0),
            //           child: GridView.builder(
            //             shrinkWrap: true,
            //             physics: const NeverScrollableScrollPhysics(),
            //             itemCount: sunnahPrayers.length,
            //             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            //               crossAxisCount: sunnahPrayers.length,
            //               crossAxisSpacing: 8.0,
            //               mainAxisSpacing: 8.0,
            //               childAspectRatio: 2,
            //             ),
            //             itemBuilder: (BuildContext context, int index) {
            //               final prayerName = sunnahPrayers.keys.toList()[index];
            //               return Obx(() => PrayerSunnahGridTile(
            //                     prayerName: prayerName,
            //                     prayerTime: sunnahPrayers[prayerName]!,
            //                     isNotified: SettingsController
            //                         .instance
            //                         .athanNotificationsMap
            //                         // ignore: invalid_use_of_protected_member
            //                         .value[prayerName]!
            //                         .isNotified,
            //                     isNextPrayer: prayerName ==
            //                         calculatedNextSunnahPrayer
            //                             .name.capitalizeFirst,
            //                   ));
            //             },
            //           ),
            //         ),
            //       ],
            //     );
            //   },
            // ),
            3.verticalSpace,
            InstagramPageIndicator(
              itemCount: 1000,
              controller: controller.prayersPageController,
              dotSize: 11,
              dotSelectedSize: 15,
              dotSpacing: 20,
              dotSelectedColor: AppColor.kOrangeColor,
              visibleDotCount: 7,
              visibleDotThreshold: 2,
              orientation: Axis.horizontal,
              reverse: false,
            ),
          ],
        );
      },
    );
  }
}
// class 2 extends StatelessWidget {
//   final PrayerTimes prayerTimes;
//   final Map<String, DateTime> prayers;
//   final String? nextPrayer;
//   final String? currentCity;

//   const PrayersGridView2({
//     super.key,
//     required this.prayerTimes,
//     required this.prayers,
//     this.nextPrayer,
//     this.currentCity,
//   });

//   @override
//   Widget build(BuildContext context) {
//     var isRtl = cacheMemory.read('lang') == 'ar';
//     final now = DateTime.now();

//     return Directionality(
//       textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
//       child: Column(
//         children: [
//           Container(
//             height: 75,
//             padding: const EdgeInsets.all(0),
//             margin: const EdgeInsets.all(0),
//             alignment: Alignment.topCenter,
//             decoration: const BoxDecoration(
//               color: AppColor.kDarkBlueColor,
//               // image: DecorationImage(
//               //   image: AssetImage('assets/images/islamic.jpg'),
//               //   fit: BoxFit.fitWidth,
//               // ),
//               // colorFilter: ColorFilter.mode(
//               //     Color.fromARGB(255, 75, 82, 145), BlendMode.hardLight)),
//             ),
//             child: Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
//               child: Column(
//                 children: [
//                   CustomText(
//                     '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}',
//                     style: const TextStyle(color: Colors.white),
//                   ),
//                   // CustomText(
//                   //   currentCity ?? '',
//                   //   style: const TextStyle(fontWeight: FontWeight.bold),
//                   // ),
//                   CustomText(
//                     intl.DateFormat('EEEE dd MMMM yyyy', 'ar_SA')
//                         .format(DateTime.now()),
//                     style: const TextStyle(fontWeight: FontWeight.bold),
//                   ),
//                   const SizedBox(height: 5),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Flexible(
//                         child: CustomDate(isHijry: false, dateTime: now),
//                       ),
//                       Flexible(
//                         child: CustomDate(isHijry: true, dateTime: now),
//                       ),
//                     ],
//                   )
//                 ],
//               ),
//             ),
//           ),
//           for (var i = 0; i < prayers.length; i++)
//             // if (i >= 1 && i <= 5)
//             PrayerGridTile2(
//               prayerName: prayers.keys.toList()[i],
//               prayerTime: prayers[prayers.keys.toList()[i]]!,
//               isNotified: nextPrayer == prayers.keys.toList()[i],
//               isRtl: isRtl,
//             ),
//         ],
//       ),
//     );
//   }
// }
