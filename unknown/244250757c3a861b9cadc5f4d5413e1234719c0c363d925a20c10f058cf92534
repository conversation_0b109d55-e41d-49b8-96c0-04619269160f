// import 'dart:io';

// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import 'package:salawati/core/utils/app_consts.dart';
// import 'package:salawati/core/utils/location_controller.dart';
// import 'package:salawati/features/home_widget/prayer_times_home_widget.dart';
// import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
// import 'package:workmanager/workmanager.dart';

// /// Manages home widget updates using WorkManager
// class HomeWidgetManager {
//   // WorkManager task identifiers
//   static const String periodicWidgetUpdateTask = 'periodicWidgetUpdate';
//   static const String immediateWidgetUpdateTask = 'immediateWidgetUpdate';

//   // Update frequency (15 minutes)
//   static const Duration updateFrequency = Duration(minutes: 15);

//   /// Initialize the WorkManager for widget updates
//   static Future<void> initialize() async {
//     if (!Platform.isAndroid && !Platform.isIOS) return;

//     try {
//       // Initialize WorkManager
//       await Workmanager().initialize(
//         callbackDispatcher,
//         isInDebugMode: false,
//       );

//       // Schedule periodic updates
//       await schedulePeriodicUpdates();

//       // Perform an immediate update
//       await requestImmediateUpdate();

//       debugPrint('🔵 HomeWidgetManager initialized successfully');
//     } catch (e) {
//       debugPrint('🔴 Failed to initialize HomeWidgetManager: $e');
//     }
//   }

//   /// Schedule periodic widget updates
//   static Future<void> schedulePeriodicUpdates() async {
//     if (!Platform.isAndroid && !Platform.isIOS) return;

//     try {
//       await Workmanager().registerPeriodicTask(
//         periodicWidgetUpdateTask,
//         periodicWidgetUpdateTask,
//         frequency: updateFrequency,
//         constraints: Constraints(
//           networkType: NetworkType.not_required,
//           requiresBatteryNotLow: false,
//           requiresCharging: false,
//           requiresDeviceIdle: false,
//           requiresStorageNotLow: false,
//         ),
//         existingWorkPolicy: ExistingWorkPolicy.replace,
//         backoffPolicy: BackoffPolicy.linear,
//         backoffPolicyDelay: const Duration(minutes: 5),
//       );

//       debugPrint('🔵 Periodic widget updates scheduled');
//     } catch (e) {
//       debugPrint('🔴 Failed to schedule periodic widget updates: $e');
//     }
//   }

//   /// Request an immediate widget update
//   static Future<void> requestImmediateUpdate() async {
//     if (!Platform.isAndroid && !Platform.isIOS) return;

//     try {
//       await Workmanager().registerOneOffTask(
//         immediateWidgetUpdateTask,
//         immediateWidgetUpdateTask,
//         initialDelay: const Duration(seconds: 5),
//         constraints: Constraints(
//           networkType: NetworkType.not_required,
//           requiresBatteryNotLow: false,
//           requiresCharging: false,
//           requiresDeviceIdle: false,
//           requiresStorageNotLow: false,
//         ),
//         existingWorkPolicy: ExistingWorkPolicy.replace,
//       );

//       debugPrint('🔵 Immediate widget update requested');
//     } catch (e) {
//       debugPrint('🔴 Failed to request immediate widget update: $e');
//     }
//   }

//   /// Cancel all scheduled widget updates
//   static Future<void> cancelAllUpdates() async {
//     if (!Platform.isAndroid && !Platform.isIOS) return;

//     try {
//       // await Workmanager().cancelAll();
//       debugPrint('🔵 All widget updates cancelled');
//     } catch (e) {
//       debugPrint('🔴 Failed to cancel widget updates: $e');
//     }
//   }
// }

// /// The callback dispatcher for WorkManager
// @pragma('vm:entry-point')
// void callbackDispatcher() {
//   Workmanager().executeTask((taskName, inputData) async {
//     try {
//       debugPrint('🔵 Executing widget update task: $taskName');
//       // Get.lazyPut<LocationController>(() => LocationController(), fenix: true);

//       // // Register controllers
//       // Get.put<AppLocale>(AppLocale(), permanent: true);
//       // Get.put<PrayerController>(PrayerController(), permanent: true);
//       // Get.put<SettingsController>(
//       //   SettingsController(prayerController: Get.find()),
//       //   permanent: true,
//       // );
//       // Initialize app group for iOS
//       if (Platform.isIOS) {
//         await PrayerWidgetService.initializeAppGroup();
//       }

//       // Create a temporary instance of PrayerController if needed
//       final bool needsTemporaryController =
//           !Get.isRegistered<PrayerController>();
//       final bool needsTemporaryController2 =
//           !Get.isRegistered<LocationController>();
//       if (needsTemporaryController && needsTemporaryController2) {
//         Get.lazyPut<LocationController>(() => LocationController(),
//             fenix: true);

//         // Create a temporary controller for background updates
//         final tempController = PrayerController();

//         // Load saved location data
//         final double? latitude = cacheMemory.read(LATITUDE);
//         final double? longitude = cacheMemory.read(LONGITUDE);
//         final String? cityName = cacheMemory.read(CITY);

//         if (latitude != null && longitude != null) {
//           tempController.latitude = latitude;
//           tempController.longitude = longitude;
//           tempController.currentCity.value = cityName ?? 'Unknown';

//           // Update the widget with the temporary controller
//           await PrayerWidgetService.updatePrayerTimeWidget(tempController);
//         }
//       } else {
//         // Use the existing controller
//         await PrayerWidgetService.updatePrayerTimeWidget(
//             Get.find<PrayerController>());
//       }

//       debugPrint('🔵 Widget update task completed successfully');
//       return true;
//     } catch (e) {
//       debugPrint('🔴 Widget update task failed: $e');
//       return false;
//     }
//   });
// }
