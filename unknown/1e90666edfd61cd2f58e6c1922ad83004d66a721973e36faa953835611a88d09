import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:salawati/core/data/data_service.dart';
import 'package:salawati/core/data/data_state.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/features/settings/data/models/cities_model.dart';
import 'package:salawati/features/settings/data/models/location_model.dart';

import '../../../mosque/data/repo/cities_repo.dart';

class SettingsSearchRepo {
  Future<DataState> getCities({required String input}) async {
    String language = (cacheMemory.read('lang') ?? 'ar');
    final response = await DataService.get(
        url: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',
        queryParameters: {
          'language': language == 'en' ? 'eng' : language,
          'input': input,
          'key': GOOGLE_API_KEY,
          'types': '(cities)',
        });
    return DataService.dataRepoRequest(
        response: response, fromJson: CitiesModel.fromJson);
  }

  Future<DataState> getCityLocation({required String placeId}) async {
    final response = await DataService.get(
        url: 'https://maps.googleapis.com/maps/api/place/details/json',
        queryParameters: {
          'place_id': placeId,
          'key': GOOGLE_API_KEY,
          'fields': 'geometry',
        });
    return DataService.dataRepoRequest(
        response: response, fromJson: LocationModel.fromJson);
  }

  Future<DataState> getLocalCitiesByLatLong(
      {required double latitude, required double longitude}) async {
    const radiusKm = 50;
    const limit = 20;

    final db = CitiesRepo.db;
    // Convert to radians once at the beginning
    final centerLatRad = degreesToRadians(latitude);
    final centerLongRad = degreesToRadians(longitude);

    // Calculate the bounding box with a larger margin
    const fullRadiusRad = radiusKm * 1.25;
    final queryLatMin = centerLatRad - fullRadiusRad * 1.25;
    final queryLatMax = centerLatRad + fullRadiusRad * 1.25;
    final queryLongMin = centerLongRad - fullRadiusRad * 1.25;
    final queryLongMax = centerLongRad + fullRadiusRad * 1.25;

    // Round coordinates to 6 decimal places
    final roundedLatMin = roundDouble(queryLatMin, 6);
    final roundedLatMax = roundDouble(queryLatMax, 6);
    final roundedLongMin = roundDouble(queryLongMin, 6);
    final roundedLongMax = roundDouble(queryLongMax, 6);

    // debugPrint(
    //     'Latitude Min: $roundedLatMin, Latitude Max: $roundedLatMax, Longitude Min: $roundedLongMin, Longitude Max: $roundedLongMax');

    // Execute the query using rawQuery
    final resultFromDB = await db.rawQuery('''
      SELECT *
      FROM cities
      WHERE latitude >= ? AND latitude <= ?
      AND longitude >= ? AND longitude <= ?
      ORDER BY (ABS(latitude - ?) + ABS(longitude - ?)) * 111.13952305
      LIMIT $limit
    ''', [
      roundedLatMin,
      roundedLatMax,
      roundedLongMin,
      roundedLongMax,
      latitude,
      longitude
    ]);

    // debugPrint('Result from DB:$resultFromDB');
    final localCities =
        resultFromDB.map((row) => LocalCity.fromJson(row)).toList();
    // Convert QueryResultSet to List<LocalCitiesModel>
    final localCitiesModels = LocalCitiesModel(cities: localCities);

    return DataSuccess(localCitiesModels);
  }

  Future<DataState> getLocalCitiesByName({required String cityName}) async {
    const limit = 20;

    final db = CitiesRepo.db;

    // Prepare the search term for case-insensitive comparison
    final searchTerm = '%${cityName.toLowerCase()}%';

    // Execute the query using rawQuery
    final resultFromDB = await db.rawQuery('''
  SELECT c.*, co.name AS country_name, co.arabic_name AS country_arabic_name
    FROM cities c
    LEFT JOIN countries co ON c.country_id = co.country_id
    WHERE (LOWER(c.arabic_name) LIKE ? OR LOWER(c.name) LIKE ?)
      AND c.iscities15000 = 1
    ORDER BY
      CASE
        WHEN LOWER(c.arabic_name) = ? THEN 0
        WHEN LOWER(c.name) = ? THEN 1
        ELSE 2
      END,
      LOWER(c.arabic_name),
      LOWER(c.name)
    LIMIT $limit
  ''', [
      searchTerm,
      searchTerm,
      cityName.toLowerCase(),
      cityName.toLowerCase()
    ]);
    // debugPrint('Result from DB:$resultFromDB');
    debugPrint('Raw DB results for $resultFromDB:');
    // for (var row in resultFromDB) {
    // debugPrint('City: ${row['arabic_name']} '
    // 'Lat: ${row['latitude']}, Lng: ${row['longitude']}');
    // }
    // Convert QueryResultSet to List<LocalCitiesModel>
    // Convert QueryResultSet to List<LocalCitiesModel>
    final localCities =
        resultFromDB.map((row) => LocalCity.fromJson(row)).toList();

    // Create a map of country IDs to country names
    final countryMap = {
      for (var e in localCities.map((city) => MapEntry(city.countryId,
          Country(name: city.englishName, arabicName: city.arabicName))))
        e.key: e.value
    };

    // Update each city with its corresponding country name
    for (var city in localCities) {
      if (countryMap.containsKey(city.countryId)) {
        city.country = countryMap[city.countryId];
      }
    }

    final localCitiesModels = LocalCitiesModel(cities: localCities);

    return DataSuccess(localCitiesModels);
  }

  Future<DataState> getLocalCityLocation(
      {required double latitude, required double longitude}) async {
    // final response = await DataService.get(
    //     url: 'https://maps.googleapis.com/maps/api/place/details/json',
    //     queryParameters: {
    //       'place_id': placeId,
    //       'key': GOOGLE_API_KEY,
    //       'fields': 'geometry',
    //     });

    final localCitiesModels = LocationModel(lat: latitude, lng: longitude);

    return DataSuccess(localCitiesModels);
    // return DataService.dataRepoRequest(
    //     response: response, fromJson: LocationModel.fromJson);
  }

  double roundDouble(double value, int places) {
    return (value * math.pow(10.0, places)).round() / math.pow(10.0, places);
  }
}

double degreesToRadians(double deg) => deg * math.pi / 180.0;

double radiansToDegrees(double rad) => rad * 180.0 / math.pi;
