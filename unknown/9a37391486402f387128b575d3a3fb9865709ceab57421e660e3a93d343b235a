import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/permissions/presentation/controller/permission_flow_controller.dart';
import 'package:salawati/features/permissions/presentation/widgets/permission_card.dart';

class PermissionCompleteScreen extends GetView<PermissionFlowController> {
  const PermissionCompleteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),

            // Content
            Column(
              children: [
                // Header
                _buildHeader(),

                // Progress indicator
                _buildProgressIndicator(),

                32.verticalSpace,

                // Completion card
                Expanded(
                  child: Center(
                    child: SingleChildScrollView(
                      child: Obx(() => PermissionCard(
                        icon: controller.getStepIcon(PermissionStep.complete),
                        title: controller.getStepTitle(PermissionStep.complete),
                        description: controller.getStepDescription(PermissionStep.complete),
                        buttonText: controller.getStepButtonText(PermissionStep.complete),
                        onAllow: controller.requestCurrentPermission,
                        isLoading: controller.isLoading,
                        canSkip: false, // No skip option on completion screen
                      )),
                    ),
                  ),
                ),

                24.verticalSpace,
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppColor.kRectangleColor.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.arrow_back,
                color: AppColor.kGreyColor,
                size: 20.sp,
              ),
            ),
          ),

          Expanded(
            child: CustomText(
              'Setup Complete',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppColor.kOrangeColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Empty space to balance the layout
          SizedBox(
            width: 40.w,
            height: 40.w,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // Progress bar (completed)
          Container(
            height: 4.h,
            decoration: BoxDecoration(
              color: AppColor.kOrangeColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          8.verticalSpace,

          // Step indicator
          CustomText(
            'Completed!',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColor.kOrangeColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
